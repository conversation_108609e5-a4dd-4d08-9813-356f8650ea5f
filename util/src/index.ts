export * as gcp from './gcp.js';
export * as base64 from './base64.js';
export * as time from './time.js';
export { default as chunked } from './chunked.js';
export { default as wait } from './wait.js';
export { default as truncate } from './truncate.js';
export { default as getDomain } from './url.js';

export function promiseFulfilled<T>(
    value: PromiseSettledResult<T>
): value is PromiseFulfilledResult<T> {
    return value.status === 'fulfilled';
}

export function isDefined<T>(value: T | undefined): value is T {
    return value !== undefined;
}

export function groupBy<T, K extends keyof any>(
    list: T[],
    getKey: (item: T) => K
): Record<K, T[]> {
    return list.reduce(
        (result, item) => {
            const key = getKey(item);
            if (!result[key]) {
                result[key] = [];
            }
            result[key].push(item);
            return result;
        },
        {} as Record<K, T[]>
    );
}
