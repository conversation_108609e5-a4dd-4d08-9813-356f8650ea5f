export default function truncate(
    input: string | undefined,
    length: number,
    ellipsis: boolean = true
): string {
    if (!input) return '';

    if (input.length > length) {
        if (ellipsis) {
            return input.substring(0, length) + '...';
        }

        if (!ellipsis) {
            const truncated = input.substring(0, length);
            const lastPeriodIndex = truncated.lastIndexOf('.');
            if (lastPeriodIndex !== -1) {
                return truncated.substring(0, lastPeriodIndex + 1);
            }
            return truncated;
        }
    }

    return input;
}
