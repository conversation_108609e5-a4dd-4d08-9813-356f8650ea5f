{"name": "@quarterback/extractotron", "module": "index.ts", "type": "module", "scripts": {"build": "pnpm tsc", "download": "pnpm build && dotenvx run --env-file=.env.production.local -- node ./dist/download.js", "extract": "pnpm build && dotenvx run --env-file=.env.production.local -- node ./dist/extract.js", "csvify": "pnpm build && dotenvx run --env-file=.env.production.local -- node ./dist/csvify.js", "pages": "pnpm build && dotenvx run --env-file=.env.production.local -- node ./dist/pages.js"}, "files": ["dist"], "peerDependencies": {"typescript": "^5.0.0"}, "packageManager": "pnpm@9.9.0", "dependencies": {"@cantoo/pdf-lib": "^2.2.3", "@date-fns/tz": "^1.2.0", "@dotenvx/dotenvx": "^0.44.1", "@fast-csv/format": "^5.0.2", "@fast-csv/parse": "^5.0.0", "@google-cloud/documentai": "^8.11.0", "@hyzyla/pdfium": "^2.1.2", "@opendocsg/pdf2md": "^0.1.31", "@quarterback/util": "workspace:*", "arg": "^5.0.2", "base-x": "^4.0.0", "date-fns": "^4.1.0", "openai": "4.71.0", "pdf-lib": "^1.17.1", "playwright": "1.46.1", "sharp": "^0.33.5", "tsx": "^4.8.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.12.7", "typescript": "^5.4.5"}}