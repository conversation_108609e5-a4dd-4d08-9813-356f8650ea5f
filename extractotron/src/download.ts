import arg from 'arg';
import { URL } from 'url';
import { chromium } from 'playwright';
import { parse } from 'date-fns';
import { z } from 'zod';

import * as fs from 'fs';
import { Readable } from 'stream';
import { finished } from 'stream/promises';

const args = arg({});

const AnnouncementsSchema = z.object({
    data: z.object({
        items: z.array(
            z.object({
                headline: z.string(),
                documentKey: z.string(),
                symbol: z.string(),
                date: z.coerce.date()
            })
        )
    })
});

type AnnouncementsSchema = z.infer<typeof AnnouncementsSchema>;

async function download(url: string, path: string) {
    await finished(
        Readable.fromWeb(await fetch(url).then((it) => it.body!)).pipe(
            fs.createWriteStream(path)
        )
    );
}

async function getTickers(): Promise<Array<{ symbol: string; document: string }>> {
    const url = `https://asx.api.markitdigital.com/asx-research/1.0/markets/announcements?announcementTypes\[\]=quarterly+cash+flow+report&dateStart=2025-01-01&dateEnd=2025-02-04&page=0&itemsPerPage=1000&summaryCountsDate=2025-02-07`;

    const response = await fetch(url);
    const json = AnnouncementsSchema.parse(await response.json());

    const bySymbol = Object.groupBy(json.data.items, (it) => it.symbol);

    return Object.keys(bySymbol)
        .map((symbol) => bySymbol[symbol]![0])
        .map((it) => ({ symbol: it.symbol, document: it.documentKey }));
}

const tickers = await getTickers();

for (const ticker of tickers) {
    await download(
        `https://cdn-api.markitdigital.com/apiman-gateway/ASX/asx-research/1.0/file/${ticker.document}`,
        `./reports/${ticker.symbol}.pdf`
    );
}

// console.log(tickers.size);
// console.log(Array.from(tickers).sort().join('\n'));

// const browser = await chromium.launch({ headless: true });
// const context = await browser.newContext();

// for (const symbol of tickers.slice(0, 100)) {
//     const url = new URL('https://www.asx.com.au/asx/v2/statistics/announcements.do');
//
//     url.searchParams.append('by', 'asxCode');
//     url.searchParams.append('asxCode', symbol);
//     url.searchParams.append('timeframe', 'D');
//     url.searchParams.append('period', 'M3');
//
//     const page = await context.newPage();
//     await page.goto(url.toString());
//
//     const table = page
//         .locator(`table[summary='Most recent company announcements']`)
//         .first();
//
//     const announcements = await Promise.all(
//         await table
//             .locator('tbody > tr')
//             .all()
//             .then((rows) =>
//                 rows.map(async (row) => {
//                     const columns = await row.locator('td').all();
//
//                     return {
//                         at: parse(
//                             await columns[0]
//                                 .innerText()
//                                 .then((it) => it.replace('\n', ' ')),
//                             'dd/MM/yyyy h:mm aa',
//                             new Date()
//                         ),
//                         sensitive: await columns[1]
//                             .locator('img')
//                             .count()
//                             .then((it) => it > 0),
//                         pdf: await columns[2].locator('a').getAttribute('href'),
//                         title: await columns[2]
//                             .locator('a')
//                             .evaluate((el) => el.firstChild.textContent)
//                             .then((it) => it.trim())
//                     };
//                 })
//             )
//     );
//
//     const report = announcements.find(
//         (it) =>
//             it.title.toLowerCase().includes('quarterly activities') ||
//             it.title.toLowerCase().includes('quarterly report') ||
//             it.title.toLowerCase().includes('appendix 5b')
//     );
//
//     if (report && report.pdf) {
//         const event = page.waitForEvent('download');
//
//         try {
//             await page.goto(`https://www.asx.com.au${report.pdf}`, {
//                 timeout: 10000
//             });
//
//             await page.locator('input[value="Agree and proceed"]').click();
//         } catch (_) {}
//
//         const download = await event;
//         await download.saveAs(`./reports/${symbol}.pdf`);
//
//         console.log(`${symbol}: ${report.title}`);
//     } else {
//         console.log(`No report found for ${symbol}`);
//
//         for (const announcement of announcements) {
//             console.log(`    ${announcement.title}`);
//         }
//     }
//
//     await page.close();
// }
//
// await context.close();
// await browser.close();
