import * as fs from 'fs/promises';
import pages from './pages.js';
import * as documentai from '@google-cloud/documentai';
import { PDFDocument } from '@cantoo/pdf-lib';
import { chunked } from '@quarterback/util';

const processor = `projects/775036113941/locations/us/processors/790cbf37b184b01e`;
const client = new documentai.v1.DocumentProcessorServiceClient();

function range(start: number, end: number): number[] {
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
}

function extractLabelsAndValues(document: any): Record<string, any> {
    const labelsAndValues: Record<string, any> = {};

    function recurseEntities(entities: any[], parentObject: Record<string, any>) {
        for (const entity of entities) {
            const label = entity.type || 'Unknown';
            const value = entity.mentionText;

            // If there are nested properties, initialize or ensure an object structure
            if (entity.properties && entity.properties.length > 0) {
                if (!parentObject[label] || typeof parentObject[label] !== 'object') {
                    parentObject[label] = {};
                }
                // Recursively populate nested properties
                recurseEntities(entity.properties, parentObject[label]);
            } else if (value !== undefined) {
                // Set value only if it's defined and there are no nested properties
                parentObject[label] = value;
            }
        }
    }

    // Start the recursion with top-level entities
    if (document.entities) {
        recurseEntities(document.entities, labelsAndValues);
    }

    return labelsAndValues;
}

const pageItems = Object.keys(pages).length;
const step = Math.min(pageItems, 5);
console.log(`Total reports: ${pageItems}`);
const chunks = chunked(Object.keys(pages), 10);
let startIndex: number = 0;
let endIndex = step;
const errors: any[] = [];

while (startIndex < chunks.length) {
    for (const chunk of chunks.slice(startIndex, endIndex)) {
        console.log('Starting chunk...');
        await Promise.all(
            chunk.map(async (symbol) => {
                const appendix = await PDFDocument.create();

                const report = await PDFDocument.load(
                    new Uint8Array(await fs.readFile(`./reports/${symbol}.pdf`)),
                    {
                        throwOnInvalidObject: true,
                        ignoreEncryption: true,
                        password: ''
                    }
                );

                const copied = await appendix.copyPages(
                    report,
                    range(pages[symbol][0] - 1, pages[symbol][1] - 1)
                );

                for (const copy of copied) {
                    appendix.addPage(copy);
                }

                await fs.writeFile(`./appendices/${symbol}.pdf`, await appendix.save());

                try {
                    const [{ document }] = await client.processDocument({
                        name: processor,
                        rawDocument: {
                            content: await appendix.saveAsBase64(),
                            mimeType: 'application/pdf'
                        }
                    });
                    const result = extractLabelsAndValues(document);
                    await fs.writeFile(
                        `./results/${symbol}.json`,
                        JSON.stringify(result)
                    );
                    console.log({ symbol, result });
                } catch (error) {
                    errors.push({ symbol, error });
                }
            })
        );

        console.log('Chunk completed');
    }

    startIndex += step;
    endIndex = Math.min(endIndex + step, chunks.length);
}

if (errors.length > 0) {
    console.log('writing errors file...');
    await fs.writeFile('error.json', JSON.stringify(errors));
}
