import pages from './pages.js';
import * as csv from '@fast-csv/format';
import * as fs from 'fs/promises';
import * as fsSync from 'fs';

const stream = csv.format({
    headers: [
        'Symbol',
        'Denomination',
        '1.2 a) Payments for exploration & evaluation',
        '1.2 e) Payments for administration and corporate costs',
        '2.1 d) Payments to acquire or for exploration & evaluation',
        '2.6 Net cash from / (used in) investing activities',
        '3.1 Proceeds from issues of equity securities (excluding convertible debt securities)',
        '3.2 Proceeds from issue of convertible debt securities',
        '3.3 Proceeds from exercise of options',
        '3.4 Transaction costs related to issues of equity securities or convertible debt securities',
        '3.5 Proceeds from borrowings',
        '3.6 Repayment of borrowings',
        '3.7 Transaction costs related to loans and borrowings',
        '3.8 Dividends paid',
        '3.9 Other (provide details if material)',
        '3.10 Net cash from / (used in) financing activities',
        '7.5 Unused financing facilities available at quarter end',
        '8.3 Total relevant outgoings',
        '8.4 Cash and cash equivalents at quarter end (item 4.6)',
        // '8.6 Total available funding',
        '8.7 Estimated quarters of funding available',
        '8.8.2 Has the entity taken any steps to raise further cash?'
    ]
});

const out = fsSync.createWriteStream('./output.csv');

stream.pipe(out);

// \$A^{\prime}000
function parseDenomination(value: string): { currency: string; multiplier: number } {
    const denomination = { currency: 'AUD', multiplier: 1000 };
    const normalised = value.replace('^{\\prime}', "'").replace('\\$', '$');

    if (normalised.startsWith('$U') || normalised.startsWith('US$')) {
        denomination.currency = 'USD';
    }

    const zeroes = (normalised.match(/0/g) || []).length;

    return denomination;
}

/**
 * Parse numbers represented in financial notation with negatives in parenthesis
 * @param value
 */
function parseFinancialNotation(value: string): number {
    if (value.includes('(') || value.includes(')')) {
        return parseFloat(value.replace(/[()]/g, '')) * -1;
    } else {
        return parseFloat(value);
    }
}

function normaliseFinancialNotation(value: string | undefined): string {
    return value ?? '';
    // return value ? parseFinancialNotation(value).toString() : '';
}

function fundraisingSteps(quarters: string, steps: string): string {
    const available = parseFinancialNotation(quarters);

    if (steps.toLowerCase().startsWith('yes')) {
        return 'Yes';
    } else if (steps.toLowerCase().startsWith('no')) {
        return 'No';
    } else {
        if (available < 2) {
            return 'N/A';
        } else {
            return 'Unknown';
        }
    }
}

let startIndex: number = 0;
let endIndex: number = 5;
const step: number = 5;
const totalLength = Object.keys(pages).length;

while (startIndex < totalLength) {
    for (const symbol of Object.keys(pages).slice(startIndex, endIndex)) {
        const output: any = JSON.parse(
            await fs.readFile(`./results/${symbol}.json`, 'utf-8')
        );

        stream.write([
            symbol,
            output?.denomination ?? '$A',
            normaliseFinancialNotation(
                output?.operating?.operating_payments?.exploration_evaluation
            ),
            normaliseFinancialNotation(
                output?.operating?.operating_payments?.administration_corporate_costs
            ),
            normaliseFinancialNotation(
                output?.investing?.investing_payments?.exploration_evaluation
            ),
            normaliseFinancialNotation(output?.investing?.net_cash),
            normaliseFinancialNotation(
                output?.financing?.proceeds_issue_equity_securities
            ),
            normaliseFinancialNotation(
                output?.financing?.proceeds_issue_convertible_debt_securities
            ),
            normaliseFinancialNotation(output?.financing?.proceeds_exercise_options),
            normaliseFinancialNotation(
                output?.financing?.transaction_costs_equity_debt_securities
            ),
            normaliseFinancialNotation(output?.financing?.proceeds_borrowings),
            normaliseFinancialNotation(output?.financing?.repayment_borrowings),
            normaliseFinancialNotation(
                output?.financing?.transaction_costs_loans_borrowings
            ),
            normaliseFinancialNotation(output?.financing?.dividends_paid),
            normaliseFinancialNotation(output?.financing?.other),
            normaliseFinancialNotation(output?.financing?.net_cash),
            normaliseFinancialNotation(output?.facilities?.unused),
            normaliseFinancialNotation(output?.estimates?.total_relevant_outgoings),
            normaliseFinancialNotation(output?.estimates?.cash_end),
            // normaliseFinancialNotation(output?.estimates?.total_available_funding),
            normaliseFinancialNotation(output?.estimates?.quarters_available),
            fundraisingSteps(
                output?.estimates?.quarters_available ?? '0',
                output?.estimates?.fundraising_steps ?? ''
            )
        ]);
    }
    startIndex += step;
    endIndex = Math.min(endIndex + step, totalLength);
}

stream.end();
