import pdfplumber
from os import listdir

# reports = ['SAN.pdf']
reports = listdir('../reports')

data = {}

for report in reports:
    try:
        with pdfplumber.open("../reports/{report}".format(report=report)) as pdf:
            has_5b = False

            start = None
            end = None

            for page in pdf.pages:
                has_5b = len(page.search('Appendix 5B', case=False)) > 0 or has_5b

                if start == None and len(page.search('Cash flows from operating activities', case=False)) > 0:
                    start = page.page_number

                if len(page.search('Compliance statement', case=False)) > 0:
                    end = page.page_number

            if not has_5b:
                print('Appendix 5B Missing {report}'.format(report=report))
            else:
                data.update({report.replace('.pdf', ''):[start, end]})
    
    except Exception as e:
        print(e,report)
        print('oh nyo~ it wooks wike da wepowt is cowwupted! pwease twy again >w<')

if(len(data) > 0):
    with open("./pages.ts", "w") as f:
        f.write('export default ' + str(data) + 'as { [key: string]: [number, number] };')
    f.close()
else:
    print('No reports found')