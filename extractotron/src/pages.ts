const pages = {
    GPR: [1, 6],
    LMS: [11, 15],
    NAE: [28, 32],
    GES: [6, 10],
    WA1: [15, 19],
    GED: [12, 16],
    PFE: [8, 12],
    DVP: [1, 5],
    '1AE': [7, 11],
    CRI: [13, 17],
    AIV: [1, 5],
    SYR: [9, 13],
    FAL: [12, 16],
    MRQ: [18, 22],
    DMM: [1, 5],
    ASO: [10, 14],
    DLI: [15, 19],
    SLS: [21, 25],
    ZEU: [9, 13],
    APC: [8, 12],
    FTZ: [7, 11],
    JAV: [17, 21],
    C1X: [17, 21],
    ORD: [14, 17],
    TEM: [15, 19],
    MKR: [13, 17],
    JNO: [7, 11],
    CDR: [31, 35],
    M2M: [17, 21],
    FTL: [15, 19],
    ASN: [17, 21],
    ADG: [16, 20],
    VEN: [14, 18],
    TRE: [12, 15],
    OEL: [13, 17],
    WAF: [1, 5],
    WC8: [17, 21],
    WCN: [13, 17],
    NMG: [13, 17],
    PHO: [10, 13],
    EEG: [1, 5],
    BCA: [17, 21],
    RDN: [14, 18],
    EE1: [8, 11],
    LCY: [1, 5],
    GRL: [9, 13],
    WWI: [6, 10],
    LCL: [14, 18],
    GR8: [9, 13],
    LMG: [8, 11],
    LM1: [18, 22],
    ERL: [15, 19],
    WMG: [19, 23],
    BUY: [9, 13],
    IG6: [11, 15],
    SAN: [1, 5],
    PUR: [19, 21],
    PDN: [1, 5],
    NME: [4, 8],
    LTR: [18, 22],
    GGX: [3, 7],
    NMR: [1, 5],
    LU7: [17, 21],
    FML: [12, 18],
    HAW: [17, 21],
    STA: [7, 13],
    HCH: [26, 30],
    AEV: [8, 12],
    MRR: [9, 12],
    JBY: [45, 48],
    ASL: [18, 22],
    AQD: [29, 33],
    MEL: [9, 13],
    SNX: [12, 15],
    MKG: [1, 5],
    FZR: [9, 12],
    MHK: [15, 19],
    ORP: [1, 5],
    TG1: [24, 28],
    DAL: [21, 24],
    MDI: [7, 10],
    ASM: [19, 23],
    CHR: [1, 5],
    ARI: [10, 14],
    ADD: [13, 17],
    MRD: [9, 13],
    DTM: [7, 11],
    PEK: [11, 15],
    BUX: [15, 19],
    ERM: [1, 5],
    NAG: [1, 5],
    BCB: [18, 22],
    RDM: [19, 23],
    BLZ: [7, 11],
    BOA: [1, 5],
    GRX: [9, 13],
    BNR: [6, 10],
    BOE: [1, 5],
    BMM: [21, 25],
    NTU: [1, 5],
    E25: [11, 15],
    REZ: [14, 18],
    IRD: [5, 9],
    EQE: [2, 6],
    EQR: [17, 22],
    NNL: [7, 10],
    PEX: [15, 19],
    BYH: [11, 15],
    HAR: [9, 13],
    CR9: [5, 9],
    AKO: [18, 22],
    FNR: [16, 20],
    SVL: [26, 30],
    SLB: [9, 13],
    HYT: [13, 17],
    MEI: [16, 20],
    MGA: [1, 5],
    QXR: [7, 11],
    TDO: [16, 19],
    CDT: [12, 16],
    VSR: [12, 16],
    CHW: [15, 19],
    ARL: [23, 27],
    SMG: [4, 8],
    ADV: [18, 21],
    HMG: [12, 16],
    AEE: [12, 16],
    AKN: [1, 5],
    SUV: [8, 12],
    SVM: [9, 12],
    ODY: [13, 17],
    HAS: [1, 5],
    PEN: [1, 5],
    PIM: [8, 12],
    RRR: [13, 17],
    PR1: [29, 32],
    BML: [8, 12],
    RHK: [10, 14],
    KEY: [7, 11],
    GSN: [19, 23],
    PSC: [19, 23],
    BM8: [16, 20],
    RIM: [21, 25],
    KGD: [8, 12],
    RHI: [17, 21],
    BMN: [1, 5],
    REY: [1, 5],
    IR1: [17, 21],
    NWM: [25, 29],
    IPX: [9, 13],
    GIB: [11, 15],
    RR1: [8, 12],
    NMT: [1, 5],
    PGD: [16, 20],
    KRR: [16, 19],
    VKA: [14, 18],
    DUN: [9, 13],
    VHM: [6, 8],
    ADC: [14, 18],
    MPK: [14, 17],
    DYM: [16, 20],
    ADT: [9, 13],
    '1MC': [16, 20],
    MGU: [1, 5],
    VRC: [10, 16],
    FUL: [11, 15],
    M3M: [14, 18],
    ARN: [15, 19],
    HWK: [27, 31],
    MHM: [9, 13],
    TG6: [13, 17],
    SCN: [8, 12],
    HVY: [10, 14],
    MDX: [6, 10],
    SMS: [11, 15],
    MGT: [12, 16],
    AQC: [6, 10],
    MEK: [1, 4],
    DYL: [1, 5],
    AGY: [1, 5],
    AKM: [12, 16],
    SVY: [1, 5],
    PDI: [20, 24],
    BVR: [7, 11],
    BTM: [1, 5],
    GSM: [15, 19],
    BMO: [30, 34],
    PRS: [13, 17],
    RIL: [8, 12],
    PR2: [37, 41],
    LAT: [1, 4],
    KAU: [13, 17],
    RNX: [13, 16],
    LEL: [23, 26],
    KLR: [11, 15],
    LIO: [1, 5],
    BDG: [5, 9],
    PL3: [8, 13],
    BSN: [1, 5],
    GML: [14, 18],
    PNM: [4, 7],
    WEL: [5, 9],
    LSA: [13, 17],
    LPM: [9, 13],
    NIM: [16, 19],
    VMG: [3, 7],
    OCN: [7, 11],
    ALY: [1, 4],
    CYM: [10, 14],
    FEG: [10, 13],
    AVW: [6, 10],
    T88: [14, 18],
    CND: [9, 13],
    MM1: [10, 14],
    SGA: [17, 21],
    DES: [7, 11],
    OZM: [17, 20],
    CLZ: [8, 13],
    AUZ: [18, 22],
    MCM: [14, 19],
    OZZ: [11, 14],
    TMS: [16, 19],
    SHN: [1, 5],
    MVL: [6, 10],
    TX3: [15, 19],
    VAR: [12, 16],
    HFR: [6, 10],
    TVN: [16, 20],
    LRS: [10, 14],
    G6M: [6, 12],
    WGR: [16, 20],
    LRD: [14, 18],
    PAT: [20, 24],
    BSX: [9, 13],
    EVR: [14, 20],
    ETM: [10, 14],
    WIN: [16, 20],
    BRK: [18, 22],
    LIN: [9, 13],
    ENT: [4, 7],
    PWN: [14, 18],
    LEX: [1, 5],
    ENV: [32, 36],
    RBX: [6, 9],
    EV1: [7, 11],
    PNN: [13, 17],
    EVG: [15, 19],
    GAL: [1, 5],
    ALM: [14, 18],
    CVV: [10, 14],
    DRX: [21, 25],
    AOA: [14, 18],
    MXR: [18, 22],
    AAJ: [14, 18],
    MAG: [14, 18],
    AW1: [39, 43],
    TON: [7, 11],
    DKM: [12, 16],
    MBK: [1, 5],
    TAR: [9, 13],
    ZMI: [12, 16],
    MOM: [11, 15],
    CBE: [14, 18],
    DEG: [1, 5],
    TAS: [20, 24],
    AYM: [8, 12],
    TMG: [13, 17],
    COB: [14, 16], // check again - preview export req --done
    AUN: [17, 21],
    TM1: [9, 13],
    M4M: [14, 18],
    JRV: [11, 17],
    OM1: [7, 10],
    TZN: [10, 14],
    SQX: [6, 10],
    CUL: [31, 35],
    '88E': [12, 16],
    SS1: [23, 27],
    IMA: [20, 24],
    GCR: [14, 18],
    KTA: [14, 17],
    PBL: [1, 5],
    PLG: [9, 13],
    GMN: [50, 54],
    NPM: [9, 13],
    LKE: [1, 5],
    WR1: [13, 16],
    PUA: [10, 14],
    BKY: [17, 20],
    GUL: [1, 5],
    EMH: [8, 12],
    PTR: [16, 20],
    YRL: [20, 24],
    KM1: [24, 27],
    ICN: [4, 8],
    PLC: [9, 12],
    RTG: [16, 20],
    GLN: [11, 15],
    GBE: [10, 15],
    G50: [14, 18],
    I88: [19, 23],
    GBR: [25, 29],
    PCL: [1, 5],
    XAM: [25, 29],
    HE8: [9, 13],
    A8G: [10, 14],
    ACP: [6, 11],
    CXO: [10, 14],
    FFM: [23, 26],
    FDR: [8, 12],
    MTC: [14, 18],
    TLG: [9, 13],
    MAU: [1, 5],
    CNB: [18, 22],
    TOK: [23, 27],
    QPM: [8, 12],
    CAZ: [12, 15],
    MMA: [13, 17],
    SGQ: [82, 86],
    AXL: [24, 28],
    D3E: [7, 11],
    MOH: [8, 12],
    MLS: [51, 55],
    CMO: [8, 12],
    TMB: [25, 28],
    MAT: [14, 18],
    MTB: [26, 31],
    QEM: [10, 13],
    CVR: [8, 11],
    CTM: [14, 17],
    BGL: [1, 5],
    RAG: [1, 4],
    GWR: [6, 10],
    ENR: [22, 26],
    IXR: [17, 21],
    GW1: [14, 18],
    RON: [9, 13],
    E79: [1, 5],
    BRX: [16, 18],
    POL: [13, 17],
    ION: [8, 12],
    CTO: [6, 10],
    ACS: [10, 14],
    CMM: [14, 17],
    CAY: [6, 10],
    AX8: [17, 20],
    TAT: [11, 15],
    ZNC: [25, 29],
    AXN: [15, 19],
    DEV: [12, 16],
    HRE: [7, 11],
    AUH: [5, 9],
    COD: [13, 17],
    CXM: [9, 14],
    OMA: [8, 12],
    CZR: [9, 13],
    AAM: [21, 25],
    HIO: [6, 10],
    CTN: [19, 23],
    AMN: [1, 5],
    FHS: [1, 5],
    VMC: [19, 23],
    ILT: [19, 23],
    LRV: [21, 25],
    LSR: [16, 20],
    GLL: [5, 7],
    RTR: [21, 25],
    ICL: [21, 25],
    RAS: [1, 5],
    IVZ: [6, 10],
    KNI: [22, 26],
    WSR: [10, 14],
    RLT: [4, 11],
    EL8: [17, 21],
    RLC: [12, 16],
    EMP: [1, 5],
    ELT: [19, 23],
    GT1: [18, 22],
    LGM: [28, 32],
    LKY: [9, 13],
    KLL: [5, 9],
    G88: [12, 16],
    NGX: [8, 12],
    GLA: [7, 11],
    RWD: [9, 13],
    INF: [1, 5],
    LRL: [19, 23],
    AOK: [6, 10],
    AM5: [1, 5],
    DRE: [1, 4],
    CWX: [22, 26],
    ANX: [23, 27],
    MTL: [6, 10],
    COI: [11, 13],
    AUR: [7, 11],
    CLE: [15, 19],
    AUE: [15, 19],
    MNB: [10, 14],
    MM8: [13, 17],
    SFM: [9, 13],
    XTC: [9, 13],
    TCG: [14, 18],
    CBY: [19, 23],
    DGR: [16, 20],
    TOE: [11, 15],
    SHP: [9, 15],
    TOR: [14, 18],
    VTM: [13, 17],
    MTM: [13, 17],
    FG1: [23, 27],
    CUF: [10, 14],
    SRI: [16, 20], // check again -- preview export req -- done
    BRU: [10, 14],
    NGY: [1, 5],
    PNR: [17, 21],
    LKO: [14, 18],
    NRX: [12, 16],
    PVT: [14, 18],
    RMI: [11, 15],
    EMS: [30, 34],
    EM2: [18, 22],
    KNG: [12, 16],
    NRZ: [4, 8],
    RVT: [14, 18],
    NES: [8, 12],
    EXR: [7, 11],
    GCM: [17, 21],
    IMI: [1, 5],
    INR: [1, 5],
    CUS: [13, 17],
    VMM: [21, 25],
    TTM: [10, 14],
    FIN: [10, 13],
    SRK: [9, 13],
    OMX: [15, 19],
    ABX: [13, 17],
    HHR: [10, 14],
    AUQ: [16, 21],
    FRS: [26, 29],
    MAY: [6, 8],
    ATU: [1, 5],
    TMX: [21, 27],
    TNC: [1, 5],
    SHE: [7, 11],
    JGH: [6, 9],
    VTX: [12, 16],
    MAN: [6, 10],
    CAV: [14, 18],
    AZI: [11, 15],
    SFX: [12, 15],
    MML: [1, 5],
    TAM: [12, 16],
    ATC: [11, 15],
    AUG: [19, 23],
    CXU: [27, 31],
    AAU: [10, 14],
    ALR: [8, 12],
    VML: [7, 11],
    AM7: [12, 16],
    GAS: [7, 11],
    IND: [11, 15],
    PLN: [10, 14],
    KOB: [14, 18],
    RND: [10, 14],
    EME: [11, 15],
    GTE: [1, 5],
    KAI: [22, 26],
    PVW: [15, 18], // check again - preview export req -- done
    BKT: [10, 15],
    LEG: [1, 5],
    RLL: [39, 43],
    GTR: [14, 18],
    GUE: [9, 13],
    RML: [3, 7],
    PXX: [13, 17],
    BGD: [1, 5],
    RC1: [9, 12],
    KNB: [21, 25],
    BRE: [16, 19],
    NFM: [13, 17],
    GL1: [15, 19],
    ICG: [8, 11], // check again - preview export req -- done
    USL: [16, 20],
    BPM: [9, 13],
    WEC: [12, 16],
    HGO: [19, 23],
    SPQ: [34, 38],
    SRN: [18, 22],
    ALV: [17, 21],
    TYX: [8, 12],
    CZN: [1, 4],
    OLY: [10, 14],
    AUC: [21, 25],
    CMG: [14, 18],
    TOU: [1, 5],
    HRN: [9, 13],
    MNS: [6, 9],
    CBH: [8, 12],
    AZL: [12, 16],
    TBR: [12, 15],
    CCM: [11, 14],
    MMI: [11, 14],
    CAE: [6, 10],
    S2R: [29, 33],
    ZAG: [11, 14],
    AWJ: [2, 6],
    SKM: [1, 5],
    CNJ: [10, 13],
    TMK: [5, 8],
    COY: [12, 16],
    T92: [18, 22],
    CY5: [29, 32],
    QGL: [9, 13],
    JPR: [1, 5],
    AMD: [16, 20],
    RXL: [14, 18],
    GBZ: [30, 34],
    PNT: [12, 16],
    NFL: [10, 13],
    WIA: [10, 13],
    IVG: [5, 9],
    NSM: [1, 5],
    BEZ: [14, 18],
    L1M: [40, 44],
    PVE: [9, 12],
    KAL: [15, 18],
    RMX: [19, 23],
    ROG: [16, 20],
    EMU: [13, 16],
    ITM: [9, 13],
    KLI: [15, 18],
    IVR: [21, 25],
    RB6: [12, 15],
    NH3: [10, 14],
    NHE: [13, 18],
    ALB: [9, 12],
    SRZ: [21, 25],
    AON: [15, 19],
    SPR: [20, 24],
    CVN: [7, 11], // check again - preview export req -- done
    ACM: [11, 15],
    AAR: [28, 32],
    FRB: [12, 16],
    CMD: [6, 10],
    TLM: [14, 18],
    AVL: [14, 18],
    HPR: [8, 12],
    CC9: [8, 12],
    AZ9: [15, 19],
    AXP: [5, 9],
    AYT: [9, 13],
    HRZ: [1, 5],
    C7A: [7, 11],
    SER: [9, 13],
    AZY: [33, 37],
    AVM: [13, 17],
    VUL: [1, 5],
    ATR: [1, 5],
    SKY: [1, 5],
    CLA: [8, 12],
    MTH: [15, 19],
    CTP: [9, 13],
    SRL: [10, 14],
    SPD: [21, 24],
    OCT: [13, 17],
    OB1: [9, 13],
    POS: [18, 23],
    EUR: [25, 30],
    GNM: [6, 10],
    POD: [7, 11],
    KZR: [1, 5],
    RCR: [26, 30],
    BIM: [6, 10],
    PTN: [1, 5],
    EMT: [9, 13],
    RNU: [22, 26],
    LDR: [18, 22],
    EMC: [1, 5],
    ENX: [10, 13],
    R8R: [12, 16],
    EG1: [9, 13],
    LLI: [38, 42],
    NTM: [1, 5],
    IPT: [1, 4],
    BUS: [18, 22],
    WA8: [31, 35],
    GGE: [10, 14],
    TRM: [16, 20],
    STK: [29, 33],
    OD6: [17, 21],
    FAR: [3, 6],
    ADX: [1, 5],
    AGC: [13, 17],
    M2R: [21, 25],
    TI1: [1, 5],
    ARU: [12, 17],
    CHN: [18, 21],
    ASQ: [16, 20],
    SMI: [14, 18],
    SLZ: [10, 14], // check again -- export from preview req -- done
    VRX: [10, 14],
    FXG: [9, 13],
    DAF: [4, 8], // check again -- export from preview req -- done
    TEE: [1, 5],
    TGM: [20, 25],
    THB: [30, 33],
    DME: [7, 11],
    VR8: [9, 13],
    AQX: [15, 19],
    AS1: [9, 12],
    MEG: [28, 32],
    ADY: [1, 5],
    DY6: [16, 20],
    ADN: [10, 14],
    AKA: [12, 16],
    WC1: [17, 22],
    BUR: [13, 17],
    LML: [14, 17],
    REC: [14, 18],
    IPB: [4, 8],
    GRE: [11, 15],
    KFM: [21, 25],
    PSL: [7, 11],
    NXM: [27, 30],
    EEL: [1, 4],
    LM8: [26, 30],
    PH2: [11, 15],
    EPM: [15, 19],
    LYK: [8, 12],
    NC1: [15, 19],
    GEN: [1, 5],
    G1A: [9, 13],
    KSN: [1, 4],
    PEC: [17, 21],
    FMR: [11, 15],
    DTR: [6, 10],
    FME: [7, 11],
    A1G: [12, 16],
    MRL: [10, 15],
    A11: [14, 18],
    DM1: [11, 15],
    ASR: [16, 20],
    TKL: [8, 12],
    VRL: [19, 23],
    ARV: [9, 13],
    DMG: [5, 9],
    ASE: [23, 27],
    CKA: [19, 24],
    SBR: [9, 13],
    JLL: [1, 5],
    ORN: [1, 5],
    TEG: [15, 19],
    MHC: [16, 20],
    TGN: [24, 28],
    SNG: [17, 21],
    TKM: [12, 16],
    AS2: [13, 18],
    MRZ: [1, 5],
    HLX: [14, 18],
    CRB: [7, 11],
    AHN: [3, 7],
    SUM: [20, 24],
    NWC: [14, 17],
    EGR: [21, 24],
    RDS: [10, 13],
    BCK: [9, 13],
    BNL: [5, 9],
    WTM: [1, 4],
    PRX: [15, 19],
    EFE: [1, 5],
    LOT: [17, 21],
    RGL: [19, 23],
    LYN: [17, 21],
    ERW: [13, 17], // check again -- export from preview req -- done
    PGY: [19, 25],
    HCD: [4, 8],
    TSO: [1, 4],
    CPN: [15, 19],
    STM: [16, 19],
    MQR: [19, 23],
    AGE: [16, 20],
    AGR: [1, 5],
    HMX: [18, 22],
    SMX: [15, 19],
    ARD: [16, 20],
    THR: [7, 11],
    HTM: [9, 12],
    XST: [5, 9],
    M24: [7, 11],
    ARR: [8, 12],
    AR3: [12, 16],
    AQI: [12, 16],
    FAU: [6, 10],
    CR1: [30, 33],
    QML: [15, 19],
    SUH: [10, 14],
    CPO: [1, 5],
    CST: [8, 12],
    AHK: [1, 5],
    PGO: [10, 14],
    WBE: [6, 9],
    ESR: [18, 22],
    GHM: [1, 4],
    IDA: [9, 13],
    NWF: [4, 8],
    WYX: [18, 22],
    BC8: [15, 19],
    REE: [18, 22],
    BMR: [28, 32],
    NYM: [26, 30],
    BMG: [12, 16],
    PRL: [2, 6],
    GRV: [11, 15],
    LLL: [1, 5],
    BAS: [9, 13],
    BTE: [6, 10],
    EQX: [8, 12],
    BTR: [29, 33],
    G11: [6, 10],
    KP2: [10, 14],
    PGM: [11, 15],
    WAK: [6, 10],
    FLG: [22, 26],
    STN: [16, 20],
    FL1: [9, 13],
    CRR: [15, 19],
    ODE: [13, 17],
    CR3: [20, 24],
    TSL: [5, 8],
    CPM: [7, 11],
    OKJ: [7, 11],
    MI6: [13, 16],
    CEL: [1, 5],
    C29: [8, 12],
    DBO: [1, 5],
    CGR: [9, 13],
    OSM: [9, 13],
    SMM: [13, 17],
    MEU: [14, 18],
    ZEO: [11, 15],
    JAL: [9, 13],
    FBM: [18, 22],
    HOR: [10, 14],
    CRS: [16, 19],
    SVG: [1, 5],
    STX: [21, 25],
    CRD: [8, 12],
    GDM: [11, 14],
    KRM: [37, 40],
    EQN: [18, 21],
    PKO: [9, 13],
    YAR: [12, 16],
    GHY: [20, 24],
    UVA: [18, 22],
    NVA: [17, 21],
    BCM: [1, 5],
    KKO: [9, 13],
    LNR: [11, 15],
    PRM: [4, 7],
    BLU: [9, 13],
    RIE: [12, 17],
    KGL: [14, 18]
} as { [key: string]: [number, number] };

export default pages;
