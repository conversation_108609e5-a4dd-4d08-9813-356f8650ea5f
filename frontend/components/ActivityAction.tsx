import React from 'react';
import Tooltip from '@/components/Tooltip';

export function ActivityAction({
    icon: Icon,
    tooltip,
    onClick
}: {
    icon: React.ComponentType<{ className?: string }>;
    tooltip: string;
    onClick: () => void;
}) {
    return (
        <Tooltip text={tooltip} offset={36}>
            <div
                onClick={onClick}
                className="p-2 hover:bg-gray-200 rounded-md cursor-pointer">
                <Icon className="size-4" />
            </div>
        </Tooltip>
    );
}

export default ActivityAction;
