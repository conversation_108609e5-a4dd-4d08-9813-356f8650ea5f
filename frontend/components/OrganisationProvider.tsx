'use client';

import React, {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useState
} from 'react';
import { ListedEntity, Organisation } from '@quarterback/types';
import useAPI from '@/api/hooks/util/useAPI';
import api from '@/api/fetchers/api';

interface OrganisationContext {
    organisations: Array<Organisation>;
    selected: { organisation: Organisation; entity: ListedEntity } | undefined;

    select(organisation: Organisation, entity: ListedEntity): void;
}

const OrganisationContext = createContext<OrganisationContext | undefined>(undefined);

export function useOrganisation() {
    const organisationContext = useContext(OrganisationContext)!;
    return organisationContext ?? null;
}

export default function OrganisationProvider({ children }: React.PropsWithChildren) {
    const { data, error, isLoading } = useAPI<Array<Organisation>>(
        api,
        `/v1/organisations`
    );

    const [selected, setSelected] = useState<
        { entity: ListedEntity; organisation: Organisation } | undefined
    >();

    useEffect(() => {
        if (!selected) {
            const organisation = data?.[0];
            const entity = data?.[0]?.users?.[0]?.entities?.[0];

            if (organisation && entity) {
                setSelected({ organisation, entity });
            }
        }
    }, [selected, data]);

    const select = useCallback((organisation: Organisation, entity: ListedEntity) => {
        setSelected({ organisation, entity });
    }, []);

    return (
        <OrganisationContext.Provider
            value={{ organisations: data ?? [], selected, select }}>
            {children}
        </OrganisationContext.Provider>
    );
}
