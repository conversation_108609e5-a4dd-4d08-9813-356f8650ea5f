import React, { useMemo } from 'react';
import { CheckCircleIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/20/solid';
import classNames from 'classnames';

export default function Alert({
    status,
    text,
    dismiss
}: {
    status: 'success' | 'error';
    text: string;
    dismiss: () => void;
}) {
    const colors = useMemo(() => {
        switch (status) {
            case 'success':
                return {
                    background: 'bg-green-50',
                    icon: 'text-green-400',
                    text: 'text-green-800',
                    dismiss:
                        'bg-green-50 text-green-500 hover:bg-green-100 focus:ring-green-600 focus:ring-offset-green-50'
                };
            case 'error':
                return {
                    background: 'bg-red-50',
                    icon: 'text-red-400',
                    text: 'text-red-800',
                    dismiss:
                        'bg-red-50 text-red-500 hover:bg-red-100 focus:ring-red-600 focus:ring-offset-red-50'
                };
        }
    }, [status]);

    const Icon = useMemo(() => {
        switch (status) {
            case 'success':
                return CheckCircleIcon;
            case 'error':
                return XCircleIcon;
        }
    }, [status]);

    return (
        <div className={classNames('rounded-md p-4', colors.background)}>
            <div className="flex">
                <div className="flex-shrink-0">
                    <Icon aria-hidden="true" className={classNames('h-5 w-5', colors.icon)} />
                </div>
                <div className="ml-3">
                    <p className={classNames('text-sm font-medium', colors.text)}>{text}</p>
                </div>
                <div className="ml-auto pl-3">
                    <div className="-mx-1.5 -my-1.5">
                        <button
                            type="button"
                            onClick={dismiss}
                            className={classNames(
                                'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                                colors.dismiss
                            )}>
                            <span className="sr-only">Dismiss</span>
                            <XMarkIcon aria-hidden="true" className="h-5 w-5" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
