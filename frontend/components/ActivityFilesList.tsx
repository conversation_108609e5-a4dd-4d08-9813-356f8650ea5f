import React, { useCallback, useState } from 'react';
import { ActivityFile } from '@quarterback/types';
import { DocumentIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { log } from '@quarterback/util/gcp';
import useFileDownloadMutation from '@/api/hooks/mutations/useFileDownloadMutation';

interface ActivityFilesListProps {
    files: ActivityFile[];
}

export default function ActivityFilesList({ files }: ActivityFilesListProps) {
    const [downloadingFiles, setDownloadingFiles] = useState<Record<string, boolean>>({});
    const { downloadFile } = useFileDownloadMutation();

    const handleDownload = useCallback(
        async (file: ActivityFile) => {
            if (!file.storagePath) {
                log('ERROR', 'File has no storage path', { file });
                return;
            }

            try {
                setDownloadingFiles((prev) => ({ ...prev, [file.storagePath!]: true }));

                // Download the file using the storage path
                const result = await downloadFile(file.storagePath);

                if (!result) {
                    throw new Error('Failed to download file');
                }
            } catch (error) {
                log('ERROR', 'Failed to download file', {
                    error
                });
            } finally {
                setDownloadingFiles((prev) => ({ ...prev, [file.storagePath!]: false }));
            }
        },
        [downloadFile]
    );

    if (!files || files.length === 0) {
        return null;
    }

    return (
        <ul className="divide-y divide-gray-100 ">
            {files.map((file) => (
                <li
                    key={file.storagePath}
                    className="flex items-center justify-between py-3 pl-3 pr-4 text-sm">
                    <div className="flex w-0 flex-1 items-center">
                        <DocumentIcon
                            className="h-5 w-5 flex-shrink-0 text-gray-400"
                            aria-hidden="true"
                        />
                        <span className="ml-2 w-0 flex-1 truncate">{file.fileName}</span>
                    </div>
                    <div className="ml-4 flex-shrink-0">
                        <button
                            onClick={() => handleDownload(file)}
                            disabled={downloadingFiles[file.storagePath!]}
                            className="font-medium text-indigo-600 hover:text-indigo-500 flex items-center">
                            {downloadingFiles[file.storagePath!] ? (
                                <span className="text-gray-500">Downloading...</span>
                            ) : (
                                <>
                                    <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                                    <span>Download</span>
                                </>
                            )}
                        </button>
                    </div>
                </li>
            ))}
        </ul>
    );
}
