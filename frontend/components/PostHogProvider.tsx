'use client';
import React from 'react';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';

const key = process.env.NEXT_PUBLIC_POSTHOG_KEY;
const host = process.env.NEXT_PUBLIC_POSTHOG_HOST;
const url = process.env.NEXT_PUBLIC_FRONTEND_URL ?? '';

if (typeof window !== 'undefined' && key && host && url === 'https://app.qback.au') {
    posthog.init(key, {
        api_host: host,
        person_profiles: 'identified_only'
    });
}

export default function CSPostHogProvider({ children }: React.PropsWithChildren) {
    return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
}
