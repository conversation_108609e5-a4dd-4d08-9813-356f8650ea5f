import { Activity, Activity2 } from '@quarterback/types';
import React, { useMemo } from 'react';

type ActivitySource =
    | 'hotcopper'
    | 'news'
    | 'asx-announcement'
    | 'tweet'
    | 'reddit'
    | 'linkedIn'
    | 'facebook'
    | 'instagram'
    | 'youtube'
    | 'tiktok'
    | 'discord'
    | 'telegram'
    | 'medium'
    | 'quora'
    | 'slack'
    | 'advfn'
    | 'applePodcast'
    | 'audible'
    | 'aussiestockforums'
    | 'castbox'
    | 'clubhouse'
    | 'iheartradio'
    | 'investorhub'
    | 'pinterest'
    | 'snapchat'
    | 'spotify'
    | 'stocktwits'
    | 'strawman'
    | 'tradingqna'
    | 'tumblr'
    | 'vimeo'
    | 'wechat'
    | 'whatsapp'
    | 'whirlpoolfinance'
    | 'bogleheads'
    | 'applepodcast'
    | null;
type Source = {
    source: ActivitySource;
    url?: string | null;
} | null;

export function getSourceIconFromURL(url: string | undefined): string | undefined {
    if (!url) return undefined;

    switch (new URL(url)?.host as string) {
        case 'www.tipranks.com':
            return '/sources/tipranks.webp';
        default:
            return `https://logo.clearbit.com/${new URL(url)?.host}?size=256`;
    }
}

export function onImageError(
    e: React.SyntheticEvent<HTMLImageElement>,
    activity: Activity | Activity2
) {
    const imgElement = e.currentTarget as HTMLImageElement;

    if (('type' in activity && activity.type === 'news') || 'news' in activity) {
        imgElement.src = '/sources/news.svg';
        imgElement.onerror = null;
    }

    return;
}

function icon(source: Source): string | undefined {
    switch (source?.source) {
        case 'asx-announcement':
            return '/sources/asx.svg';
        case 'hotcopper':
            return '/sources/hotcopper.svg';
        case 'tweet':
            return '/sources/x.svg';
        case 'reddit':
            return '/sources/reddit.svg';
        case 'linkedIn':
            return '/sources/linkedin.svg';
        default:
            if (source?.url) {
                return getSourceIconFromURL(source.url);
            }

            return undefined;
    }
}

function source(activity: Activity | Activity2): Source {
    if ('type' in activity) {
        return {
            source: activity.type === 'redditComment' ? 'reddit' : activity.type,
            url: activity?.url
        };
    } else {
        if ('asx' in activity) {
            return {
                source: 'asx-announcement'
            };
        }
        if ('tweet' in activity) {
            return {
                source: 'tweet'
            };
        }
        if ('news' in activity) {
            return {
                source: 'news',
                url: activity?.url
            };
        }
        if ('hotcopper' in activity)
            return {
                source: 'hotcopper'
            };
        if ('redditPost' in activity || 'redditComment' in activity) {
            return {
                source: 'reddit'
            };
        }

        if ('linkedIn' in activity)
            return {
                source: 'linkedIn'
            };
        if ('facebook' in activity)
            return {
                source: 'facebook',
                url: activity?.url
            };
        if ('instagram' in activity)
            return {
                source: 'instagram',
                url: activity?.url
            };

        if ('youtube' in activity)
            return {
                source: 'youtube',
                url: activity?.url
            };

        if ('tiktok' in activity)
            return {
                source: 'tiktok',
                url: activity?.url
            };

        if ('discord' in activity)
            return {
                source: 'discord',
                url: activity?.url
            };

        if ('telegram' in activity)
            return {
                source: 'telegram',
                url: activity?.url
            };

        if ('medium' in activity)
            return {
                source: 'medium',
                url: activity?.url
            };

        if ('quora' in activity)
            return {
                source: 'quora',
                url: activity?.url
            };

        if ('slack' in activity)
            return {
                source: 'slack',
                url: activity?.url
            };

        if ('advfn' in activity)
            return {
                source: 'advfn',
                url: activity?.url
            };

        if ('applepodcast' in activity)
            return {
                source: 'applepodcast',
                url: activity?.url
            };

        if ('audible' in activity)
            return {
                source: 'audible',
                url: activity?.url
            };

        if ('aussiestockforums' in activity)
            return {
                source: 'aussiestockforums',
                url: activity?.url
            };

        if ('castbox' in activity)
            return {
                source: 'castbox',
                url: activity?.url
            };

        if ('clubhouse' in activity)
            return {
                source: 'clubhouse',
                url: activity?.url
            };

        if ('iheartradio' in activity)
            return {
                source: 'iheartradio',
                url: activity?.url
            };

        if ('investorhub' in activity)
            return {
                source: 'investorhub',
                url: activity?.url
            };

        if ('pinterest' in activity)
            return {
                source: 'pinterest',
                url: activity?.url
            };

        if ('snapchat' in activity)
            return {
                source: 'snapchat',
                url: activity?.url
            };

        if ('spotify' in activity)
            return {
                source: 'spotify',
                url: activity?.url
            };

        if ('stocktwits' in activity)
            return {
                source: 'stocktwits',
                url: activity?.url
            };

        if ('strawman' in activity)
            return {
                source: 'strawman',
                url: activity?.url
            };

        if ('tradingqna' in activity)
            return {
                source: 'tradingqna',
                url: activity?.url
            };

        if ('tumblr' in activity)
            return {
                source: 'tumblr',
                url: activity?.url
            };

        if ('vimeo' in activity)
            return {
                source: 'vimeo',
                url: activity?.url
            };

        if ('wechat' in activity)
            return {
                source: 'wechat',
                url: activity?.url
            };

        if ('whatsapp' in activity)
            return {
                source: 'whatsapp',
                url: activity?.url
            };

        if ('whirlpoolfinance' in activity)
            return {
                source: 'whirlpoolfinance',
                url: activity?.url
            };

        if ('bogleheads' in activity)
            return {
                source: 'bogleheads',
                url: activity?.url
            };

        return null;
    }
}

export default function ActivitySourceIcon({
    activity,
    className = ''
}: {
    activity: Activity | Activity2;
    className: string;
}) {
    const path = useMemo(() => icon(source(activity)), [activity]);

    if (path) {
        // eslint-disable-next-line @next/next/no-img-element
        return (
            <img
                src={path}
                className={className}
                alt=""
                onError={(e) => onImageError(e, activity)}
            />
        );
    } else {
        return null;
    }
}
