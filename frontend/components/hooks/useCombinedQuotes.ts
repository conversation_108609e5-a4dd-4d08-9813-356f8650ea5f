import { TimeSeries, TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { useMemo } from 'react';

export default function useCombinedQuotes(
    indexSeries: TimeSeries | undefined,
    stockSeries: TimeSeries | undefined
) {
    return useMemo((): Array<{
        stock: TimeSeriesQuote;
        index: TimeSeriesQuote;
    }> => {
        if (stockSeries?.values?.length && indexSeries?.values?.length) {
            return stockSeries?.values
                .map((stockDay, i) => {
                    const indexDay = indexSeries?.values.find(
                        (d) => d.datetime === stockDay.datetime
                    );
                    return { stock: stockDay, index: indexDay };
                })
                .filter((it) => it.stock && it.index) as Array<{
                stock: TimeSeriesQuote;
                index: TimeSeriesQuote;
            }>;
        }

        return [];
    }, [indexSeries, stockSeries]);
}
