import React from 'react';
import classNames from 'classnames';

export interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
    checked: boolean;
}

export default function Checkbox({
    checked,
    className,
    ...props
}: CheckboxProps) {
    const baseStyles =
        'h-4 w-4 text-blue-600 border-gray-300 rounded focus:outline-none focus:ring-0 focus:ring-offset-0';

    return (
        <input
            type="checkbox"
            checked={checked}
            className={classNames(baseStyles, className)}
            {...props}
        />
    );
}
