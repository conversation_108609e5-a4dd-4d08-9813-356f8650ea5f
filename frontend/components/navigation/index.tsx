'use client';

import { pages } from '@/util/pages';
import Sidebar from './Sidebar';
import PageHeader from '../ui/PageHeader';

export default function Navigation({ children }: React.PropsWithChildren) {
    const sections = pages.filter((it) => it.sidebar);
    return (
        <main className="flex h-screen overflow-hidden bg-white">
            <Sidebar sections={sections} />

            <div className="flex-1 flex flex-col h-full">
                <PageHeader name="Activities" />
                <div className="flex-1 overflow-auto">{children}</div>
            </div>
        </main>
    );
}
