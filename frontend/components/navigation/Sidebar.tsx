'use client';

import React, { useState } from 'react';
import {
    ChevronDownIcon,
    ChevronUpIcon,
    MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

import { usePathname } from 'next/navigation';
import classNames from 'classnames';
import Link from 'next/link';
import { useCommandPalette } from '@/components/CommandPalette';
import Stats from '@/components/navigation/Stats';
import { useOrganisation } from '../OrganisationProvider';
import Initials from '../Initials';
import { SidebarSections } from '@/util/pages';
import CompanyPicker from './CompanyPicker';

function SidebarLink({
    name,
    icon: Icon,
    badge,
    href,
    color,
    isOpen
}: {
    name: string;
    icon?: React.ComponentType<{ className: string | undefined }> | null;
    badge?: string;
    href?: string | null;
    color?: string;
    isOpen?: boolean;
}) {
    const path = usePathname();

    function selected(href: string): boolean {
        return path.startsWith(href);
    }

    const getIcon = () =>
        Icon ? <Icon className="text-gray-400 h-5 w-5 shrink-0" /> : null;

    const getBadge = () =>
        badge !== undefined ? (
            <span
                aria-hidden="true"
                className="ml-auto w-9 min-w-max whitespace-nowrap rounded-full bg-white px-2.5 py-0.5 text-center text-xs font-medium leading-5 text-gray-600 ring-1 ring-inset ring-gray-200">
                {badge}
            </span>
        ) : null;

    if (!href) {
        return (
            <li>
                <span className="text-gray-700 group flex gap-x-2 rounded-md p-2 text-sm leading-6 font-semibold items-center">
                    {getIcon()}
                    {!Icon ? <Initials name={name} color={color} /> : null}
                    {name}
                    {isOpen ? (
                        <ChevronUpIcon className="text-gray-400  h-3 w-3 shrink-0" />
                    ) : (
                        <ChevronDownIcon className="text-gray-400  h-3 w-3 shrink-0" />
                    )}
                    {getBadge()}
                </span>
            </li>
        );
    }

    return (
        <li>
            <Link
                href={href}
                className={classNames(
                    selected(href) ? 'bg-gray-200' : 'text-gray-700  hover:bg-gray-100',
                    'group flex gap-x-2 rounded-md p-2 text-sm leading-6 font-semibold items-center'
                )}>
                {getIcon()}

                {!Icon ? <Initials name={name} color={color} /> : null}
                {name}
                {getBadge()}
            </Link>
        </li>
    );
}

export default function Sidebar({ sections }: { sections: SidebarSections }) {
    const commandPalette = useCommandPalette();
    const organisation = useOrganisation();
    const [openSection, setOpenSection] = useState<string | null>(null);

    const toggleSection = (sectionName: string) => {
        setOpenSection((prev) => (prev === sectionName ? null : sectionName));
    };

    return (
        <div className="h-screen w-72 bg-gray-100 flex flex-col border-r border-gray-200 z-10 gap-y-3 selection-none">
            <div className="border-b border-gray-300 h-[60px]">
                {/* <span className="bg-red-600 text-white rounded-md px-1 py-2 mr-2 ">
                    {organisation?.selected?.entity.symbol}
                </span>
                <span className="font-semibold text-xl">
                    {organisation?.selected?.entity.name} 
                </span> */}
                <CompanyPicker />

            </div>
            <nav className="flex flex-1 flex-col px-4">
                <div className="mb-2">
                    <Stats />
                </div>
                <div className="-mx-2 mt-2">
                    <label htmlFor="search" className="sr-only">
                        Search
                    </label>
                    <div onClick={() => commandPalette.open()} className="relative">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <MagnifyingGlassIcon
                                aria-hidden="true"
                                className="h-5 w-5 text-gray-400"
                            />
                        </div>
                        <div
                            id="query"
                            className="block w-full select-none rounded-md border-0 cursor-text bg-white py-1.5 pl-10 pr-14 ring-1 ring-inset ring-gray-300 text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            Search
                        </div>
                        <div className="absolute inset-y-0 right-0 flex py-1.5 pr-1.5">
                            <kbd className="inline-flex items-center rounded border border-gray-200 px-1 font-sans text-xs text-gray-400">
                                ⌘K
                            </kbd>
                        </div>
                    </div>
                </div>

                <ul role="list" className="flex-1 mt-2 ">
                    {sections.map((section, index) => {
                        const isDataSection = section.name.toLowerCase() === 'data';
                        const isOpen = openSection === section.name;
                        return (
                            <li key={index}>
                                {isDataSection ? (
                                    <div className="!mt-[5rem] text-gray-400 text-sm p-2 font-semibold">
                                        {section.name}
                                    </div>
                                ) : (
                                    <div
                                        onClick={() =>
                                            section.items.length &&
                                            toggleSection(section.name)
                                        }>
                                        <SidebarLink
                                            key={index}
                                            name={section.name}
                                            href={section.href}
                                            icon={section.icon}
                                            isOpen={isOpen}
                                        />
                                    </div>
                                )}
                                {section.items.length > 0 &&
                                    (isOpen || isDataSection) && (
                                        <ul
                                            role="list"
                                            className={`${!isDataSection ? 'ml-8' : ''}`}>
                                            {section.items.map((item, index) => (
                                                <SidebarLink
                                                    key={index}
                                                    name={item.name}
                                                    href={item.href}
                                                    icon={item.icon}
                                                    color={item.color}
                                                    badge={item.badge}
                                                />
                                            ))}
                                        </ul>
                                    )}
                            </li>
                        );
                    })}
                </ul>
            </nav>
            <div className="border-t border-gray-300 px-4 py-2">
                <img src="/logo-grey.svg" alt="quarterback-logo" className="w-2/3" />
            </div>
        </div>
    );
}
