import useQuote from '@/api/hooks/useQuote';
import React, { useMemo } from 'react';
import { useOrganisation } from '@/components/OrganisationProvider';
import ChangeIndicator from '@/components/ChangeIndicator';
import Tooltip from '@/components/Tooltip';
import useRisk from '@/api/hooks/useRisk';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import { startOfDay, sub } from 'date-fns';

export default function Stats() {
    const organisation = useOrganisation();

    const [from, to] = useMemo(() => {
        const to = startOfDay(sub(new Date(), { days: 1 }));
        const from = startOfDay(sub(to, { days: 7 }));
        return [from, to];
    }, []);

    const { data: timeSeries } = useTimeSeries(organisation?.selected?.entity, from, to);
    const { data: indexSeries } = useTimeSeries(
        { symbol: 'AXJO', exchange: 'ASX' },
        from,
        to
    );

    const { data: stockQuote } = useQuote(
        organisation?.selected
            ? {
                symbol: organisation.selected.entity.symbol,
                exchange: organisation.selected.entity.exchange
            }
            : undefined
    );

    const { data: indexQuote } = useQuote({ symbol: 'AXJO', exchange: 'ASX' });

    const { data: risk } = useRisk(
        organisation?.selected
            ? {
                symbol: organisation.selected.entity.symbol,
                exchange: organisation.selected.entity.exchange
            }
            : undefined,
        { symbol: 'AXJO', exchange: 'ASX' }
    );

    const change = ({
        currentClose,
        previousClose
    }: {
        currentClose: number;
        previousClose: number;
    }) => (currentClose && previousClose ? Math.log(currentClose / previousClose) : 0);

    const abnormal = useMemo(() => {
        if (
            risk &&
            timeSeries?.values?.length &&
            indexSeries?.values?.length &&
            stockQuote &&
            indexQuote
        ) {
            const previousStockClose =
                timeSeries.values[timeSeries.values.length - 1].close;
            const previousIndexClose =
                indexSeries.values[indexSeries.values.length - 1].close;

            const stockReturn = change({
                currentClose: stockQuote.close,
                previousClose: previousStockClose
            });

            const indexReturn = change({
                currentClose: indexQuote.close,
                previousClose: previousIndexClose
            });

            return stockReturn - (risk.intercept + risk.slope * indexReturn);
        }
        return undefined;
    }, [indexQuote, indexSeries, risk, stockQuote, timeSeries]);

    const [value, label] = useMemo(() => {
        if (timeSeries?.values?.length && stockQuote) {
            const previousClose = timeSeries.values[timeSeries.values.length - 1].close;
            const value = change({
                currentClose: stockQuote.close,
                previousClose: previousClose
            });
            return [value, `${(Math.abs(value) * 100).toFixed(2)}%`];
        }
        return [0, `0.00%`];
    }, [stockQuote, timeSeries]);

    const difference = useMemo(() => {
        if (timeSeries?.values?.length && stockQuote) {
            const previousClose = timeSeries.values[timeSeries.values.length - 1].close;
            const difference = stockQuote.close - previousClose;
            return difference.toFixed(2);
        }
        return 0;
    }, [stockQuote, timeSeries]);

    return (
        <div className="table-auto w-full">
            {stockQuote && (
                <div className="flex items-center text-sm">
                    <div>
                        <span className="text-3xl mr-4">
                            ${stockQuote?.close.toFixed(2)}
                        </span>
                    </div>
                    <ChangeIndicator value={value} label={`${difference} (${label})`} />
                </div>
            )}

            {abnormal ? (
                <div className="flex items-center text-sm">
                    <ChangeIndicator
                        value={abnormal}
                        label={`${Math.abs(abnormal * 100).toFixed(2)}%`}
                    />
                    <div className='mx-4'>
                        <Tooltip
                            leftOffset={100}
                            text={
                                <>
                                    <p>Cumulative Abnormal Returns (CAR)</p>
                                    <p className="pt-2">
                                        This measures how a company’s share price deviates from expected market performance,
                                        based on the ASX 200 Index (AXJO) over a rolling 250-day period.
                                    </p>
                                </>
                            }>
                            <span className="font-medium text-gray-500 cursor-help">
                                vs (AXJO)
                            </span>
                        </Tooltip>
                    </div>

                </div>
            ) : undefined}
        </div>
    );
}
