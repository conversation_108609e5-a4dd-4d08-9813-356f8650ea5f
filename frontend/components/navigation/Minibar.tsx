import {
    BellAlertIcon,
    BriefcaseIcon,
    BuildingOffice2Icon,
    ChatBubbleLeftRightIcon,
    Cog6ToothIcon,
    NewspaperIcon,
    QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';
import ProfileMenu from '@/components/navigation/ProfileMenu';
import React, { useMemo } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import classNames from 'classnames';
import { useUser } from '@/api/hooks/useUser';

function MinibarLink({
    title,
    icon: Icon,
    href,
    target,
    rel
}: {
    title: string;
    icon: React.ComponentType<{ className: string | undefined }>;
    href: string;
    target?: React.HTMLAttributeAnchorTarget;
    rel?: string;
}) {
    const path = usePathname();

    function selected(href: string): boolean {
        return path.startsWith(href);
    }

    return (
        <Link
            href={href}
            target={target}
            rel={rel}
            className={classNames(
                selected(href) ? 'bg-gray-200' : 'hover:bg-gray-200 ',
                'text-gray-800 p-2 rounded-lg group relative inline-block'
            )}>
            <Icon className="size-6 text-gray-700" />
            <div className="bg-gray-800 absolute left-full top-1/2 z-20 ml-3 -translate-y-1/2 whitespace-nowrap rounded-[5px] py-1.5 px-3.5 text-sm text-white opacity-0 group-hover:opacity-100 pointer-events-none">
                <span className="bg-gray-800 absolute left-[-3px] top-1/2 -z-10 h-2 w-2 -translate-y-1/2 rotate-45"></span>
                {title}
            </div>
        </Link>
    );
}

export default function Minibar() {
    const { data: user } = useUser();
    const { first_name: firstName, last_name: lastName } = user?.metadata ?? {};

    const email = user?.emails?.[0];

    const initials = useMemo(() => {
        if (firstName && lastName) {
            return `${firstName[0]}${lastName[0]}`.toUpperCase();
        } else if (email) {
            return `${email[0]}${email[1]}`.toUpperCase();
        }
    }, [firstName, lastName, email]);

    return (
        <div className="grow p-2.5 flex flex-col gap-y-4 items-center border-r">
            <img src="/logo.svg" alt="" className="size-11" />
            <div className="border-t self-stretch" />
            <div className="flex flex-col gap-y-1">
                <MinibarLink
                    title="Investor Relations"
                    icon={ChatBubbleLeftRightIcon}
                    href="/investors"
                />
            </div>
            <div className="border-t self-stretch" />
            <div className="flex flex-col gap-y-1">
                <MinibarLink title="Alerts" icon={BellAlertIcon} href="/alerts" />
            </div>

            {user?.permissions?.includes('admin') && (
                <>
                    <div className="border-t self-stretch" />
                    <div className="flex flex-col gap-y-1">
                        <MinibarLink
                            title="Listed Entities"
                            icon={BriefcaseIcon}
                            href="/admin/entities"
                        />
                        <MinibarLink
                            title="Organisations"
                            icon={BuildingOffice2Icon}
                            href="/admin/organisations"
                        />
                        <MinibarLink
                            title="News"
                            icon={NewspaperIcon}
                            href="/admin/news"
                        />
                    </div>
                </>
            )}

            <div className="mt-auto" />

            <div className="flex flex-col gap-y-1">
                <MinibarLink
                    title="Support"
                    icon={QuestionMarkCircleIcon}
                    href="https://share.hsforms.com/1hCdfRdygReyqWFyRc2rOnQpz78d"
                    target="_blank"
                    rel="noopener noreferrer"
                />
                <MinibarLink title="Settings" icon={Cog6ToothIcon} href="/settings" />
            </div>

            <div className="border-t self-stretch" />

            <ProfileMenu>
                <span className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-gray-500">
                    <span className="text-sm font-medium leading-none text-white">
                        {initials}
                    </span>
                </span>
            </ProfileMenu>
        </div>
    );
}
