import { Activity, Activity2 } from '@quarterback/types';
import React, { useMemo } from 'react';
import classNames from 'classnames';
import { EyeIcon, HandThumbUpIcon } from '@heroicons/react/24/outline';
import Tooltip from '@/components/Tooltip';
import { formatWithTimeZone } from '@/util/date';

function isActivity2(activity: Activity | Activity2): activity is Activity2 {
    return !('type' in activity);
}

function asActivity2(activity: Activity | Activity2): Activity2 {
    if (isActivity2(activity)) {
        return activity;
    } else {
        return {};
    }
}

function ActivityIcon({
    activity,
    className
}: {
    activity: Activity2;
    className?: string;
}) {
    if ('hotcopper' in activity) {
        return (
            <img
                src="https://hotcopper.com.au/images/default-avatar-l.png"
                className={className}
            />
        );
    } else if ('asx' in activity) {
        return <img src="/sources/asx.svg" className={className} />;
    } else if ('news' in activity) {
        return <img src="/sources/news.svg" className={className} />;
    } else if ('tweet' in activity) {
        return <img src="/sources/x.svg" className={className} />;
    } else if ('reddit' in activity) {
        return <img src="/sources/reddit.svg" className={className} />;
    } else {
        return (
            <div
                className={classNames(
                    'aspect-square rounded-full bg-gray-200',
                    className
                )}
            />
        );
    }
}

function fromSnakeCase(str: string): string {
    return str
        .toLowerCase()
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}

interface Badge {
    className: string;
    label: string;
}

function ActivityBadges({ activity }: { activity: Activity2 }) {
    const badges = useMemo(() => {
        const badges: Array<Badge> = [];

        if (activity.hotcopper?.sentiment) {
            badges.push({
                label: fromSnakeCase(activity.hotcopper.sentiment),
                className: 'bg-purple-50 text-purple-700 ring-purple-700/10'
            });
        }

        if (activity.hotcopper?.disclosure) {
            badges.push({
                label: fromSnakeCase(activity.hotcopper.disclosure),
                className: 'bg-blue-50 text-blue-700 ring-blue-700/10'
            });
        }

        return badges;
    }, [activity]);

    return (
        <div className="flex gap-x-2">
            {badges.map(({ label, className }, index) => (
                <span
                    key={index}
                    className={classNames(
                        className,
                        'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset'
                    )}>
                    {label}
                </span>
            ))}
        </div>
    );
}

interface Stat {
    Icon: React.ComponentType<{ className?: string }>;
    label: string;
    tooltip: string;
}

function ActivityStats({ activity }: { activity: Activity2 }) {
    const stats = useMemo(() => {
        const stats: Array<Stat> = [];

        if (activity.views !== undefined) {
            stats.push({
                Icon: EyeIcon,
                label: `${activity.views.toLocaleString()}`,
                tooltip: 'Views'
            });
        }

        if (activity.likes !== undefined) {
            stats.push({
                Icon: HandThumbUpIcon,
                label: `${activity.likes.toLocaleString()}`,
                tooltip: 'Likes'
            });
        }

        if (activity.hotcopper?.thread?.views !== undefined) {
            stats.push({
                Icon: EyeIcon,
                label: `${activity.hotcopper.thread.views.toLocaleString()}`,
                tooltip: 'Total thread views'
            });
        }

        return stats;
    }, [activity]);

    return (
        <div className="flex items-center gap-x-2 text-gray-500">
            {stats.map(({ Icon, ...stat }, index) => (
                <Tooltip key={index} text={stat.tooltip}>
                    <div className="flex items-center gap-x-2 cursor-help">
                        <Icon className="size-5 text-gray-600" />
                        <span>{stat.label}</span>
                    </div>
                </Tooltip>
            ))}
        </div>
    );
}

export default function ActivityCard({ activity }: { activity: Activity | Activity2 }) {
    const activity2 = useMemo(() => asActivity2(activity), [activity]);

    return (
        <div className="bg-white border rounded-lg p-4">
            {activity2.title && (
                <div>
                    <span className="text-base font-medium">{activity2.title}</span>
                </div>
            )}

            <div className="flex gap-x-2 items-center">
                <ActivityIcon activity={activity2} className="rounded-full h-10 w-auto" />
                <div className="flex flex-col">
                    {activity2.author?.name ||
                        (activity2.news?.source?.name && (
                            <span className="text-sm font-medium">
                                {activity2.author?.name || activity2.news?.source?.name}
                            </span>
                        ))}
                    <span className="text-xs text-gray-400">
                        {activity2.posted &&
                            formatWithTimeZone(activity2.posted, 'd MMMM, yyyy HH:mm')}
                    </span>
                </div>
            </div>

            <div className="flex justify-between mx-2">
                <ActivityStats activity={activity2} />
                <ActivityBadges activity={activity2} />
            </div>
        </div>
    );
}
