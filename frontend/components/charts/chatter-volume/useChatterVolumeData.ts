import { Activity } from '@quarterback/types';
import { useMemo } from 'react';
import { groupBy } from '@quarterback/util';
import { format as formatDate } from 'date-fns/format';
import { eachDayOfInterval } from 'date-fns';

export interface Day {
    day: Date;
    mentions: number;
    authors: number;
    views: number;
}

export default function useChatterVolumeData(
    activities: Array<Activity>,
    from: Date,
    to: Date
) {
    const byDay = useMemo(
        () => groupBy(activities, (it) => formatDate(it.posted, 'yyyy-MM-dd')),
        [activities]
    );

    return useMemo(() => {
        return eachDayOfInterval({ start: from, end: to }).map((day) => {
            const views = (byDay[formatDate(day, 'yyyy-MM-dd')] ?? []).reduce(
                (sum, activity) => {
                    if (activity.type === 'tweet') {
                        return activity.views;
                    } else {
                        return 0;
                    }
                },
                0
            );

            const authors = new Set(
                (byDay[formatDate(day, 'yyyy-MM-dd')] ?? [])
                    .filter(
                        (it) =>
                            it.type === 'hotcopper' ||
                            it.type === 'tweet' ||
                            it.type === 'linkedIn' ||
                            it.type === 'reddit' ||
                            it.type === 'redditComment'
                    )
                    .map((it) => it.author?.key)
            );
            const mentions = (byDay[formatDate(day, 'yyyy-MM-dd')] ?? []).length;

            return {
                day,
                views: views,
                authors: authors.size,
                mentions
            };
        });
    }, [byDay, from, to]);
}
