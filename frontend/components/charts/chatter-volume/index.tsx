import React, { useMemo } from 'react';
import { PickD3Scale, scaleLinear, scaleTime } from '@visx/scale';
import { Group } from '@visx/group';
import { LinePath } from '@visx/shape';
import { GridColumns, GridRows } from '@visx/grid';
import { Day } from './useChatterVolumeData';
import { extent } from '@visx/vendor/d3-array';
import { curveNatural } from '@visx/curve';

const colors = {
    mentions: '#63ABFD',
    authors: '#E697FF',
    views: '#FFA5CB'
};

function ChatterLine({
    x,
    xScale,
    yScale,
    volume,
    days,
    color
}: {
    x: (day: Day) => number;
    xScale: PickD3Scale<'time', number>;
    yScale: PickD3Scale<'linear', number>;
    volume: (day: Day) => number;
    height: number;
    days: Array<Day>;
    color: string;
}) {
    return (
        <Group top={0} left={0}>
            <LinePath
                stroke={color}
                fill="none"
                strokeWidth={2}
                data={days}
                curve={curveNatural}
                x={(d) => xScale(x(d)) ?? 0}
                y={(d) => yScale(volume(d)) ?? 0}
            />
        </Group>
    );
}

interface Props {
    days: Array<Day>;
    from: Date;
    to: Date;
    width: number;
    height: number;
}

export default function ChatterVolumeChart({
    days,
    from,
    to,

    width = 100,
    height = 100
}: Props) {
    const x = ({ day }: Day) => day.valueOf();

    const xScale = useMemo(
        () =>
            scaleTime({
                domain: [from.valueOf(), to.valueOf()],
                range: [0, width]
            }),
        [from, to, width]
    );

    const mentionsScale = useMemo(
        () =>
            scaleLinear({
                domain: extent(days.map((day) => day.mentions)) as [number, number],
                range: [height - 16, 0]
            }),
        [days, height]
    );

    const authorsScale = useMemo(
        () =>
            scaleLinear({
                domain: extent(days.map((day) => day.authors)) as [number, number],
                range: [height - 16, 0]
            }),
        [days, height]
    );

    const viewsScale = useMemo(
        () =>
            scaleLinear({
                domain: extent(days.map((day) => day.views)) as [number, number],
                range: [height - 16, 0]
            }),
        [days, height]
    );

    return (
        <>
            <svg width={width} height={height}>
                <Group width={width} height={height} top={16}>
                    <ChatterLine
                        x={x}
                        xScale={xScale}
                        yScale={mentionsScale}
                        volume={(it) => it.mentions ?? 0}
                        days={days}
                        height={height}
                        color={colors.mentions}
                    />
                    <ChatterLine
                        x={x}
                        xScale={xScale}
                        yScale={authorsScale}
                        volume={(it) => it.authors ?? 0}
                        days={days}
                        height={height}
                        color={colors.authors}
                    />
                    <ChatterLine
                        x={x}
                        xScale={xScale}
                        yScale={viewsScale}
                        volume={(it) => it.views ?? 0}
                        days={days}
                        height={height}
                        color={colors.views}
                    />

                    <GridColumns
                        top={0}
                        scale={xScale}
                        height={height - 16}
                        strokeDasharray="1,3"
                        stroke="#000"
                        strokeOpacity={0.1}
                        pointerEvents="none"
                        numTicks={8}
                    />
                </Group>
            </svg>
        </>
    );
}
