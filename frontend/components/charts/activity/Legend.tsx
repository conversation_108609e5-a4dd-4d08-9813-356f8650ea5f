'use client';

import { LegendI<PERSON>, Legend<PERSON><PERSON>l, LegendOrdinal } from '@visx/legend';
import { PickD3Scale } from '@visx/scale';
import React from 'react';

export default function Legend({ scale }: { scale: PickD3Scale<'ordinal', any, any> }) {
    return (
        <LegendOrdinal key={Math.random()} scale={scale} labelFormat={(label) => `${label}`}>
            {(labels) => {
                return (
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            gap: 8,
                            justifyContent: 'center',
                            margin: '16px 0'
                        }}>
                        {labels.map((label, index) => (
                            <LegendItem
                                key={`legend-${index}`}
                                onClick={() => {
                                    console.log('Big click');
                                }}>
                                <div>
                                    <svg style={{ width: 6 }} viewBox="0 0 6 6" aria-hidden="true">
                                        <circle cx="3" cy="3" r="3" fill={scale(label)}></circle>
                                    </svg>
                                    <LegendLabel align="left" style={{ margin: 0 }}>
                                        {label.text}
                                    </LegendLabel>
                                </div>
                            </LegendItem>
                        ))}
                    </div>
                );
            }}
        </LegendOrdinal>
    );
}
