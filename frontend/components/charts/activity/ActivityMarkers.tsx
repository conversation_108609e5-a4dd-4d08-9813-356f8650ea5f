import React, { useMemo } from 'react';
import { Glyph } from '@visx/glyph';
import { Group } from '@visx/group';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { Activity } from '@quarterback/types';
import { formatWithTimeZone } from '@/util/date';

function ASXGlyph({ left, top, size }: { size: number; left: number; top: number }) {
    return (
        // We embed the SVG here rather than use the <image> tag to ensure markers render
        // inside PDFs. React PDFs SVG implementation is very buggy and doesn't support
        // many SVG features.
        <Group left={left} top={top}>
            <svg width={size} height={size} viewBox="0 0 16 16" fill="none">
                <rect width="16" height="16" rx="8" ry="8" fill="#0D73AE" />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M6.66316 3.2869L7.78203 4.77214L6.66316 6.31864V3.2869ZM5.18759 7.3128V3.25H6.42949V6.63764L5.56881 7.81586L5.18759 7.3128ZM3.6875 3.25V5.32421L4.95392 7.00578V3.25H3.6875ZM9.36809 3.2869L8.24908 4.77214L9.36809 6.31864V3.2869ZM10.8437 7.3128V3.25H9.60162V6.64989L10.4502 7.82825L10.8437 7.3128ZM12.3438 3.25V5.32421L11.0772 7.00578V3.25H12.3438ZM10.8437 8.78551V12.75H9.60162V7.06718L10.8437 8.78551ZM12.2944 10.7862V12.75H11.0772V9.10464L12.2944 10.7862ZM9.36809 6.74805V12.75L8.12619 11.0562V5.04198L9.36809 6.74805ZM5.18759 8.78551V12.75H6.42949V7.06718L5.18759 8.78551ZM3.73669 10.7862V12.75H4.95392V9.10464L3.73669 10.7862ZM6.66316 6.74805V12.75L7.90506 11.0562V5.04198L6.66316 6.74805Z"
                    fill="white"
                />
            </svg>
        </Group>
    );
}

export default function ActivityMarkers({
    xScale,
    top,
    activities
}: {
    xScale: AnyScaleBand;
    top: number;
    activities: Array<Activity>;
}) {
    const announcements = useMemo(() => {
        return activities.filter((it) => it.type === 'asx-announcement');
    }, [activities]);

    return (
        <Group top={top} left={64}>
            {announcements.map((activity, ix) => {
                const width = xScale.bandwidth();
                const spacing = (width - 16) / 2;
                const size = Math.min(Math.max(width, 8), 16);

                return (
                    <ASXGlyph
                        key={ix}
                        size={size}
                        left={
                            (xScale(formatWithTimeZone(activity.posted, 'yyyy-MM-dd')) ??
                                0) + spacing
                        }
                        top={0}
                    />
                );
            })}
        </Group>
    );
}
