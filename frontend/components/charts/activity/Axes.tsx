import { scaleLinear, scaleUtc } from '@visx/scale';
import React from 'react';
import { AxisBottom, AxisLeft, Orientation } from '@visx/axis';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { format, parse } from 'date-fns';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { extent } from '@visx/vendor/d3-array';

export default function Axes({
    xScale,
    height,
    quotes,
    yScalePadding,
    width
}: {
    xScale: AnyScaleBand;
    height: number;
    width: number;
    quotes: Array<TimeSeriesQuote>;
    yScalePadding: number;
}) {
    const COLOUR = '#94a3b8';

    const minYValue = Math.min(...quotes.map((quote) => quote.low)) * yScalePadding;
    const maxYValue = Math.max(...quotes.map((quote) => quote.high));

    const yScale = scaleLinear<number>({
        range: [height - 32, 0],
        round: false,
        domain: [minYValue, maxYValue]
    });

    const yScaleTradeVolume = scaleLinear<number>({
        range: [height - 32, 0],
        round: true,
        domain: extent(quotes, (quote) => quote.volume) as number[]
    });

    const tickFormat = (value: string) =>
        format(parse(value, 'yyyy-MM-dd', new Date()), 'MMM do');

    return (
        <>
            <AxisLeft
                top={32}
                scale={yScale}
                orientation={Orientation.left}
                tickFormat={(a) => `$${a.valueOf().toFixed(4)}`}
                left={48}
                tickStroke={COLOUR}
                stroke={COLOUR}
                tickLabelProps={{ fill: COLOUR }}
            />
            <AxisLeft
                top={32}
                scale={yScaleTradeVolume}
                orientation={Orientation.left}
                tickFormat={(a) => `${a}`}
                hideZero={true}
                left={width}
                tickStroke={COLOUR}
                stroke={COLOUR}
                tickLabelProps={{ fill: COLOUR }}
            />
            <AxisBottom
                left={48}
                orientation={Orientation.bottom}
                scale={xScale}
                tickFormat={tickFormat}
                top={height}
                tickStroke={COLOUR}
                stroke={COLOUR}
                rangePadding={{ end: 100 }}
                tickLabelProps={{
                    fill: COLOUR
                }}
            />
        </>
    );
}
