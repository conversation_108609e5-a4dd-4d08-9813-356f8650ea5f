import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import activityFormat from '@/util/activityFormat';
import { formatWithTimeZone } from '@/util/date';
import { Activity, ListedEntity, TwitterProfileSnapshot } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { localPoint } from '@visx/event';
import { Group } from '@visx/group';
import { AnyScaleBand } from '@visx/shape/lib/types';
import React, { useCallback, useMemo } from 'react';

export interface TooltipData {
    day: string;
    quote: TimeSeriesQuote;
    broadcasts: number;
    chatter: number;
    publications: number;
    announcements: Array<string>;
    followers: number | undefined;
}

export function FocusedColumn({
    height,
    xScale,
    focused
}: {
    height: number;
    focused: string | undefined;
    xScale: AnyScaleBand;
}) {
    const boxWidth = xScale.bandwidth();

    if (focused) {
        return (
            <Group top={32} left={64}>
                <rect
                    x={xScale(focused)}
                    y={0}
                    width={boxWidth}
                    height={height}
                    rx={4}
                    fill="#000"
                    opacity={0.04}
                />
            </Group>
        );
    }
}

export function TooltipTargets({
    showTooltip,
    hideTooltip,
    onClick,
    entity,
    quotes,
    activities,
    followers,
    height,
    xScale
}: {
    hideTooltip: () => void;
    showTooltip: (args: {
        tooltipData: TooltipData;
        tooltipLeft: number;
        tooltipTop: number;
    }) => void;
    onClick?: (datetime: string) => void;
    entity: ListedEntity | undefined;
    quotes: Array<TimeSeriesQuote>;
    activities: Array<Activity>;
    followers: { twitter: Array<TwitterProfileSnapshot> };
    height: number;
    xScale: AnyScaleBand;
} & React.PropsWithChildren) {
    const boxWidth = xScale.bandwidth();

    const activitiesByDay = useMemo((): Record<string, Array<Activity>> => {
        return groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'));
    }, [activities]);

    const followersByDay = useMemo(() => {
        return groupBy(followers.twitter, (it) =>
            formatWithTimeZone(it.at, 'yyyy-MM-dd')
        );
    }, [followers]);

    const handleTooltip = useCallback(
        function handleTooltip(
            event: React.TouchEvent<SVGRectElement> | React.MouseEvent<SVGRectElement>,
            quote: TimeSeriesQuote
        ) {
            const { y } = localPoint(event) || { x: 0, y: 0 };
            const acitivitiesByType = groupBy(
                activitiesByDay?.[quote.datetime] ?? [],
                (it) => activityFormat(it)
            );

            showTooltip({
                tooltipData: {
                    quote,
                    day: quote.datetime,
                    broadcasts: acitivitiesByType['Broadcast']?.length ?? 0,
                    chatter: acitivitiesByType['Chatter']?.length ?? 0,
                    publications: acitivitiesByType['Publication']?.length ?? 0,
                    announcements:
                        acitivitiesByType['Announcement']
                            ?.filter((it) => it.type === 'asx-announcement')
                            .map((it) => it.title) ?? [],

                    followers: followersByDay[quote.datetime]?.[0]?.followers
                },
                tooltipLeft: xScale(quote.datetime)!,
                tooltipTop: y
            });
        },
        [activitiesByDay, entity, followersByDay, showTooltip, xScale]
    );

    return (
        <>
            <Group top={32} left={64}>
                {quotes.map((quote) => (
                    <rect
                        key={quote.datetime}
                        x={xScale(quote.datetime)}
                        y={0}
                        width={boxWidth}
                        height={height}
                        rx={4}
                        fill="#000"
                        fillOpacity={0}
                        opacity={0}
                        style={{ cursor: 'pointer' }}
                        onMouseEnter={(e) => handleTooltip(e, quote)}
                        onMouseMove={(e) => handleTooltip(e, quote)}
                        onMouseLeave={hideTooltip}
                        onClick={() => onClick?.(quote.datetime)}
                    />
                ))}
            </Group>
        </>
    );
}

export default function Tooltip({ tooltipData }: { tooltipData: TooltipData }) {
    return (
        <div className="flex flex-col gap-y-1 text-sm">
            <span className="font-bold">
                {formatWithTimeZone(new Date(tooltipData.day), 'MMM d')}
            </span>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Open</span>
                <span className="text-right">${tooltipData.quote.open.toFixed(4)}</span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Close</span>
                <span className="text-right">${tooltipData.quote.close.toFixed(4)}</span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Trade volume</span>
                <span className="text-right">
                    {tooltipData.quote.volume.toLocaleString()}
                </span>
            </div>
            <div className="border-t my-1 border-gray-200" />
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Broadcasts</span>
                <span className="text-right">{tooltipData.broadcasts}</span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Chatter</span>
                <span className="text-right">{tooltipData.chatter}</span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Publications</span>
                <span className="text-right">{tooltipData.publications}</span>
            </div>
            {tooltipData.followers !== undefined && (
                <>
                    <div className="border-t my-1 border-gray-200" />
                    <div className="flex gap-x-3 items-center justify-between">
                        <span className="font-semibold">Followers</span>
                        <span className="text-right">{tooltipData.followers}</span>
                    </div>
                </>
            )}
            {tooltipData.announcements.length !== 0 && (
                <>
                    <div className="border-t my-1 border-gray-200" />
                    <span className="font-semibold">Announcements</span>
                    {tooltipData.announcements.map((announcement, index) => (
                        <span key={index}>{announcement}</span>
                    ))}
                </>
            )}
        </div>
    );
}
