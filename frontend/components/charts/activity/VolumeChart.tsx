import { Group } from '@visx/group';
import { Bar as BarChart } from '@visx/shape';
import { useMemo } from 'react';

import { formatWithTimeZone } from '@/util/date';
import { Activity } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { scaleLinear } from '@visx/scale';
import { AnyScaleBand } from '@visx/shape/lib/types';
import { extent } from '@visx/vendor/d3-array';
import activityFormat from '@/util/activityFormat';
import { useOrganisation } from '@/components/OrganisationProvider';

export default function VolumeChart({
    xScale,
    height,
    top,
    activities
}: {
    xScale: AnyScaleBand;
    width: number;
    height: number;
    top: number;
    activities: Array<Activity>;
}) {
    const organisation = useOrganisation();
    const activityVolume = useMemo(() => {
        return Object.entries(
            groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'))
        )
            .sort((a, b) => new Date(b[0]).valueOf() - new Date(a[0]).valueOf())
            .filter((it) => !!xScale(it[0]))
            .reduce(
                (acc, [day, activities]) => {
                    const activitiesByType = Object.entries(
                        groupBy(activities, (it) => activityFormat(it))
                    ).reduce(
                        (acc, [group, activities]) => ({
                            ...acc,
                            [group]: activities.length
                        }),
                        {} as Record<string, number>
                    );

                    acc.push({
                        day,
                        totalCount: activities.length,
                        countByType: { ...activitiesByType }
                    });
                    return acc;
                },
                [] as Array<{
                    day: string;
                    totalCount: number;
                    countByType: Record<string, number>;
                }>
            );
    }, [activities, organisation?.selected?.entity, xScale]);

    const activityColors: Record<string, string> = useMemo(() => {
        return {
            Publication: '#56B5A7',
            Chatter: '#4E7FEE',
            Broadcast: '#845DEE',
            Announcement: '#DFB649'
        };
    }, []);

    const yScale = scaleLinear({
        range: [height, 0],
        round: true,
        domain: extent([...activityVolume.map((it) => it.totalCount), 0]) as number[]
    });

    return (
        <>
            <Group top={top} left={64}>
                {activityVolume.map((d, i) => {
                    const width = xScale.bandwidth();
                    const constrainedWidth = Math.max(Math.min(20, width) - 2, 2);
                    const spacing = (width - constrainedWidth) / 2;
                    const radius = Math.max(constrainedWidth / 6, 2);
                    let barY = height;

                    return Object.entries(d.countByType).map(([type, count], j) => {
                        const barHeight = (yScale(0) ?? 0) - (yScale(count) ?? 0);
                        barY -= barHeight;

                        return (
                            <BarChart
                                key={`${i}-${j}`}
                                x={(xScale(d.day) ?? 0) + spacing}
                                y={barY}
                                width={constrainedWidth}
                                height={barHeight}
                                fill={activityColors[type]}
                                fillOpacity={0.7}
                                rx={radius}
                                ry={radius}
                            />
                        );
                    });
                })}
            </Group>
        </>
    );
}
