'use client';

import React, { useMemo } from 'react';

import { scaleBand } from '@visx/scale';

import { useTooltip, useTooltipInPortal } from '@visx/tooltip';

import VolumeChart from './VolumeChart';
import ActivityMarkers from './ActivityMarkers';
import PriceChart from './PriceChart';
import Axes from './Axes';
import AbnormalReturnsChart from './AbnormalReturnsChart';
import TradeVolumeChart from './TradeVolumeChart';
import { Activity, ListedEntity } from '@quarterback/types';
import { TwitterProfileSnapshot } from '@quarterback/types';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import Tooltip, {
    FocusedColumn,
    TooltipData,
    TooltipTargets
} from '@/components/charts/activity/Tooltip';

const DEFAULT_Y_AXIS_SCALE_PADDING = 0.8;

type Props = {
    entity: ListedEntity | undefined;
    timeSeries: Array<TimeSeriesQuote>;
    activities: Array<Activity>;
    followers: { twitter: Array<TwitterProfileSnapshot> };
    onClick?: (datetime: string) => void;
    width: number;
    height: number;
    style?: {
        price?: {
            yScalePadding?: number;
        };
    };
};

export default function ActivityChart({
    width: parentWidth,
    height: parentHeight,

    style,

    onClick,

    entity,
    timeSeries,
    activities,
    followers
}: Props) {
    const {
        tooltipOpen,
        tooltipLeft,
        tooltipTop,
        tooltipData,
        hideTooltip,
        showTooltip
    } = useTooltip<TooltipData>();

    const { containerRef, TooltipInPortal } = useTooltipInPortal({
        scroll: true
    });

    const width = Math.max(parentWidth, 100);
    const height = Math.max((parentHeight ?? 100) - 22 - 16 * 2, 100);

    const x = (quote: TimeSeriesQuote) => quote.datetime;

    const xScale = useMemo(
        () =>
            scaleBand({
                range: [0, width - 100],
                round: true,
                domain: timeSeries.map(x)
            }),
        [width, timeSeries]
    );

    return (
        <>
            <svg ref={containerRef} width={width} height={height}>
                <Axes
                    xScale={xScale}
                    yScalePadding={
                        style?.price?.yScalePadding ?? DEFAULT_Y_AXIS_SCALE_PADDING
                    }
                    height={height - 50}
                    quotes={timeSeries}
                    width={width}
                />
                <FocusedColumn
                    xScale={xScale}
                    focused={tooltipData?.day}
                    height={height - 50 - 32}
                />
                <VolumeChart
                    activities={activities}
                    xScale={xScale}
                    width={width}
                    height={90}
                    top={height - 50 - 90}
                />
                <TradeVolumeChart
                    xScale={xScale}
                    width={width}
                    height={height - 50}
                    top={0}
                    timeSeries={timeSeries}
                />
                <PriceChart
                    xScale={xScale}
                    yScalePadding={
                        style?.price?.yScalePadding ?? DEFAULT_Y_AXIS_SCALE_PADDING
                    }
                    quotes={timeSeries}
                    width={width}
                    height={height - 50}
                />
                <ActivityMarkers
                    xScale={xScale}
                    activities={activities}
                    top={height - 16}
                />
                <AbnormalReturnsChart />
                <TooltipTargets
                    showTooltip={showTooltip}
                    hideTooltip={hideTooltip}
                    entity={entity}
                    quotes={timeSeries}
                    activities={activities}
                    followers={followers}
                    height={height - 50 - 32}
                    xScale={xScale}
                    onClick={onClick}
                />
            </svg>
            {tooltipOpen && tooltipData && (
                <>
                    <TooltipInPortal
                        left={tooltipLeft! + xScale.bandwidth() + 32}
                        top={tooltipTop! - 4}>
                        <Tooltip tooltipData={tooltipData} />
                    </TooltipInPortal>
                </>
            )}
        </>
    );
}
