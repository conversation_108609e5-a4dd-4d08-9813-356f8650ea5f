import { Glyph } from '@visx/glyph';
import { Group } from '@visx/group';
import { AnyScaleBand } from '@visx/shape/lib/types';
import React from 'react';

function SentimentGlyph({ left, top }: { left: number; top: number }) {
    return (
        <Glyph left={left} top={top}>
            <path
                fill="none"
                stroke="#ff0000"
                strokeWidth={1.5}
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.182 15.182a4.5 4.5 0 0 1-6.364 0M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z"
            />
        </Glyph>
    );
}

export default function SentimentMarkers({
    xScale,
    width,
    top
}: {
    xScale: AnyScaleBand;
    width: number;
    top: number;
}) {
    const sentiment = [
        { datetime: '2024-03-15', sentiment: 0.8 },
        { datetime: '2024-03-18', sentiment: -0.1 }
    ];

    return (
        <Group top={top} left={64}>
            {sentiment.map((sentiment, ix) => {
                const width = xScale.bandwidth();
                const spacing = (width - 16) / 2;

                return (
                    <SentimentGlyph
                        key={ix}
                        left={(xScale(sentiment.datetime) ?? 0) + spacing}
                        top={20}
                    />
                );
            })}
        </Group>
    );
}
