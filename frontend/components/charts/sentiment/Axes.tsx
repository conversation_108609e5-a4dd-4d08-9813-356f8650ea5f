import { PickD3Scale } from '@visx/scale';
import React from 'react';
import { AxisBottom, AxisLeft, Orientation } from '@visx/axis';
import { formatWithTimeZone } from '@/util/date';

export default function Axes({
    xScale,
    yScale
}: {
    xScale: PickD3Scale<'time', number>;
    yScale: PickD3Scale<'linear', number>;
}) {
    const COLOUR = '#94a3b8';

    const tickFormat = (value: Date | { valueOf(): number }) => {
        if (value instanceof Date) {
            return formatWithTimeZone(value, 'MMM do');
        } else {
            return formatWithTimeZone(value.valueOf(), 'MMM do');
        }
    };

    return (
        <>
            <AxisLeft
                top={16}
                scale={yScale}
                orientation={Orientation.left}
                tickFormat={(a) => `${a.valueOf().toFixed(2)}`}
                left={36}
                tickStroke={COLOUR}
                stroke={COLOUR}
                tickLabelProps={{ fill: COLOUR }}
            />
            <AxisBottom
                left={36}
                orientation={Orientation.bottom}
                scale={xScale}
                tickFormat={tickFormat}
                top={yScale(0) + 16}
                tickStroke={COLOUR}
                stroke={COLOUR}
                numTicks={6}
                tickLabelProps={{
                    fill: COLOUR
                }}
            />
        </>
    );
}
