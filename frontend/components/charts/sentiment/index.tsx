import React, { useCallback, useMemo } from 'react';
import { withParentSize, WithParentSizeProvidedProps } from '@visx/responsive';
import { PickD3Scale, scaleLinear, scaleTime } from '@visx/scale';
import { Group } from '@visx/group';
import { Line, LinePath } from '@visx/shape';
import { bisector } from '@visx/vendor/d3-array';
import { Activity, ListedEntity } from '@quarterback/types';
import { groupBy, isDefined } from '@quarterback/util';
import { eachDayOfInterval, format as formatDate } from 'date-fns';
import { defaultStyles, TooltipWithBounds, useTooltip } from '@visx/tooltip';
import { localPoint } from '@visx/event';
import { sentimentScore } from '@/util/sentiment';
import Axes from '@/components/charts/sentiment/Axes';
import { GridColumns, GridRows } from '@visx/grid';
import source from '@/util/source';

const colors = ['#56B5A7', '#4E7FEE', '#845DEE', '#DFB649'];

function SentimentLine({
    x,
    xScale,
    yScale,
    sentiment,
    days,
    color
}: {
    x: (day: Day) => number;
    xScale: PickD3Scale<'time', number>;
    yScale: PickD3Scale<'linear', number>;
    sentiment: (day: Day) => number;
    height: number;
    days: Array<Day>;
    color: string;
}) {
    return (
        <Group top={0} left={0}>
            <LinePath
                stroke={color}
                strokeWidth={2}
                data={days}
                x={(d) => xScale(x(d)) ?? 0}
                y={(d) => yScale(sentiment(d)) ?? 0}
            />
        </Group>
    );
}

const tooltipStyles = {
    ...defaultStyles,
    border: '1px solid white',
    color: 'white'
};

const bisectDate = bisector<Day, Date>((d) => d.day).left;

interface Day {
    day: Date;
    sentiment: { [key: string]: number };
}

type TooltipData = Day;

const SentimentChart = withParentSize(function SentimentChart({
    days,
    formats,
    from,
    to,

    parentWidth = 100,
    parentHeight = 100
}: {
    days: Array<Day>;
    formats: Array<string>;
    from: Date;
    to: Date;
} & WithParentSizeProvidedProps) {
    const width = parentWidth;
    const height = parentHeight;

    const {
        showTooltip,
        hideTooltip,
        tooltipData,
        tooltipTop = 0,
        tooltipLeft = 0
    } = useTooltip<TooltipData>();

    const x = ({ day }: Day) => day.valueOf();

    const xScale = useMemo(
        () =>
            scaleTime({
                domain: [from.valueOf(), to.valueOf()],
                range: [0, width - 36]
            }),
        [from, to, width]
    );

    const sentimentScale = useMemo(() => {
        const sentiment = days
            .map((day) => day.sentiment)
            .flat()
            .filter((it) => Object.keys(it).length > 0)
            .map((it) => Object.values(it))
            .flat()
            .filter(isDefined)
            .sort();

        const [min, max] = [Math.min(...sentiment), Math.max(...sentiment)];

        return scaleLinear({
            domain: [min, max],
            range: [height - 16, 0]
        });
    }, [days, height]);

    const handleTooltip = useCallback(
        (event: React.TouchEvent<SVGRectElement> | React.MouseEvent<SVGRectElement>) => {
            const { x, y } = localPoint(event) || { x: 0, y: 0 };

            const inverted = xScale.invert(x - 36);

            const index = bisectDate(days, inverted, 1);

            const d0 = days[index - 1];
            const d1 = days[index];

            let d = d0;
            if (d1 && d1.day) {
                d =
                    inverted.valueOf() - d0.day.valueOf() >
                    d1.day.valueOf() - inverted.valueOf()
                        ? d1
                        : d0;
            }

            if (d) {
                showTooltip({
                    tooltipData: d,
                    tooltipLeft: xScale(d.day),
                    tooltipTop: y
                });
            }
        },
        [showTooltip, days, xScale]
    );

    return (
        <>
            <svg width={width} height={height}>
                <Axes xScale={xScale} yScale={sentimentScale} />

                <Group top={16} left={36} width={width - 36} height={height - 16}>
                    {formats.map((format, index) => (
                        <SentimentLine
                            key={format}
                            x={x}
                            xScale={xScale}
                            yScale={sentimentScale}
                            sentiment={(it) => it.sentiment[format] ?? 0}
                            days={days}
                            height={height}
                            color={colors[index]}
                        />
                    ))}
                    <GridRows
                        left={0}
                        scale={sentimentScale}
                        width={width - 36}
                        strokeDasharray="1,3"
                        stroke="#000"
                        strokeOpacity={0.1}
                        pointerEvents="none"
                        numTicks={7}
                    />
                    <GridColumns
                        top={0}
                        scale={xScale}
                        height={height - 16}
                        strokeDasharray="1,3"
                        stroke="#000"
                        strokeOpacity={0.1}
                        pointerEvents="none"
                        numTicks={8}
                    />
                    <rect
                        x={0}
                        y={0}
                        width={width - 36}
                        height={height - 16}
                        onTouchStart={handleTooltip}
                        onTouchMove={handleTooltip}
                        onMouseMove={handleTooltip}
                        onMouseLeave={() => hideTooltip()}
                        opacity={0}
                    />
                    {tooltipData && (
                        <g>
                            <Line
                                from={{ x: tooltipLeft, y: 0 }}
                                to={{ x: tooltipLeft, y: height - 16 }}
                                stroke="#cbd5e1"
                                strokeWidth={1}
                                pointerEvents="none"
                                strokeDasharray="5,2"
                            />
                        </g>
                    )}
                </Group>
            </svg>
            {tooltipData && (
                <div>
                    <TooltipWithBounds
                        key={Math.random()}
                        top={tooltipTop - 12}
                        left={tooltipLeft + 12 + 36}
                        style={tooltipStyles}>
                        <div className="text-gray-900 text-sm">
                            <span className="font-bold">{`${formatDate(tooltipData.day, 'yyyy/MM/dd')}`}</span>
                            <ul>
                                {formats.map((format, index) => (
                                    <li
                                        key={format}
                                        className="flex items-center justify-between">
                                        <span className="text-sm font-semibold">
                                            {format}
                                        </span>
                                        <span className="text-sm ml-2">
                                            {tooltipData.sentiment[format]
                                                ? tooltipData.sentiment[format].toFixed(2)
                                                : 0}
                                        </span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </TooltipWithBounds>
                </div>
            )}
        </>
    );
});

interface Props {
    activities: Array<Activity>;
    entity: ListedEntity | undefined;
    from: Date;
    to: Date;
}

export default function SentimentChartWrapper({ entity, activities, from, to }: Props) {
    const byDay = useMemo(
        () => groupBy(activities, (it) => formatDate(it.posted, 'yyyy-MM-dd')),
        [activities]
    );

    const formats = useMemo(() => {
        return Array.from(new Set(activities.map((it) => source(it)))).sort();
    }, [activities]);

    const days = useMemo(() => {
        return eachDayOfInterval({ start: from, end: to }).map((day) => {
            const grouped = groupBy(byDay[formatDate(day, 'yyyy-MM-dd')] ?? [], (it) =>
                source(it)
            );

            return {
                day,
                sentiment: Object.fromEntries(
                    Object.entries(grouped).map(([group, activities]) => {
                        const sentiment = activities
                            .map((it) => it.sentiment)
                            .filter(isDefined)
                            .reduce(
                                (agg, sentiment) => {
                                    return {
                                        count: agg.count + 1,
                                        total: agg.total + sentimentScore(sentiment)
                                    };
                                },
                                { count: 0, total: 0 }
                            );

                        return [
                            group,
                            sentiment.total ? sentiment.total / sentiment.count : 0
                        ];
                    })
                )
            };
        });
    }, [byDay, from, to]);

    return (
        <>
            <div className="flex-1 relative m-4">
                <div className="absolute top-0 left-0 right-0 bottom-0">
                    <SentimentChart days={days} formats={formats} from={from} to={to} />
                </div>
            </div>
            <div className="flex justify-center gap-x-4">
                {formats.map((format, index) => (
                    <div key={format} className="flex gap-x-2 items-center">
                        <div
                            className="size-3 rounded-full"
                            style={{ background: colors[index] }}
                        />
                        <span className="text-sm">{format}</span>
                    </div>
                ))}
            </div>
        </>
    );
}
