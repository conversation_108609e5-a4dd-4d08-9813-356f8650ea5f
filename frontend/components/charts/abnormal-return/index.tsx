import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { formatWithTimeZone } from '@/util/date';
import { localPoint } from '@visx/event';
import { Group } from '@visx/group';
import { PickD3Scale, scaleLinear, scaleTime } from '@visx/scale';
import { Line, LinePath } from '@visx/shape';
import { defaultStyles, TooltipWithBounds, useTooltip } from '@visx/tooltip';
import { bisector, extent } from '@visx/vendor/d3-array';
import React, { useCallback, useMemo } from 'react';

const VERTICAL_PADDING = 24;

function PriceLine({
    x,
    xScale,
    price,
    height,
    quotes
}: {
    x: (quote: { index: TimeSeriesQuote; stock: TimeSeriesQuote }) => number;
    xScale: PickD3Scale<'time', number>;
    price: (quote: { index: TimeSeriesQuote; stock: TimeSeriesQuote }) => number;
    height: number;
    quotes: Array<{ index: TimeSeriesQuote; stock: TimeSeriesQuote }>;
}) {
    const yScale = useMemo(
        () =>
            scaleLinear({
                domain: extent(quotes, (d) => price(d)) as number[],
                range: [height - VERTICAL_PADDING, VERTICAL_PADDING]
            }),
        [height, quotes, price]
    );

    return (
        <Group top={0} left={0}>
            <LinePath
                stroke="#63ABFD"
                strokeWidth={2}
                fill="none"
                data={quotes}
                x={(d) => xScale(x(d)) ?? 0}
                y={(d) => yScale(price(d)) ?? 0}
            />
        </Group>
    );
}

function AbnormalLine({
    x,
    xScale,
    yScale,
    abnormal,
    quotes
}: {
    x: (quote: { index: TimeSeriesQuote; stock: TimeSeriesQuote }) => number;
    xScale: PickD3Scale<'time', number>;
    yScale: PickD3Scale<'linear', number>;
    abnormal: (
        quote: { index: TimeSeriesQuote; stock: TimeSeriesQuote },
        prevQuote: { index: TimeSeriesQuote; stock: TimeSeriesQuote }
    ) => number;
    quotes: Array<{ index: TimeSeriesQuote; stock: TimeSeriesQuote }>;
}) {
    return (
        <Group top={0} left={0}>
            <LinePath
                stroke="#A155B9"
                strokeWidth={2}
                strokeDasharray="3,6"
                fill="none"
                data={quotes}
                x={(d) => xScale(x(d)) ?? 0}
                y={(d, i) => yScale(abnormal(d, quotes[i - 1])) ?? 0}
            />
        </Group>
    );
}

interface Props {
    quotes: Array<{ index: TimeSeriesQuote; stock: TimeSeriesQuote }>;
    risk: { slope: number; intercept: number; r2: number };
    width: number;
    height: number;
}

interface TooltipData {
    day: string;
    close: number;
    change: number;
    abnormal: number;
}

function Tooltip({ tooltipData }: { tooltipData: TooltipData }) {
    return (
        <div className="flex flex-col gap-y-1 text-sm">
            <span className="font-bold">
                {formatWithTimeZone(new Date(tooltipData.day), 'MMM d')}
            </span>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Share price</span>
                <span className="text-right">${tooltipData.close.toFixed(3)}</span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Change</span>
                <span className="text-right">
                    {(tooltipData.change * 100).toFixed(2)}%
                </span>
            </div>
            <div className="flex gap-x-3 items-center justify-between">
                <span className="font-semibold">Abnormal returns</span>
                <span className="text-right">
                    {(tooltipData.abnormal * 100).toFixed(2)}%
                </span>
            </div>
        </div>
    );
}

const bisectDate = bisector<{ index: TimeSeriesQuote; stock: TimeSeriesQuote }, Date>(
    (d) => new Date(d.stock.datetime)
).left;

export default function AbnormalReturnsChart({
    quotes,
    risk,
    width: parentWidth = 100,
    height: parentHeight = 100
}: Props) {
    const width = parentWidth;
    const height = parentHeight;

    const {
        tooltipOpen,
        tooltipLeft,
        tooltipTop,
        tooltipData,
        hideTooltip,
        showTooltip
    } = useTooltip<TooltipData>();

    const abnormal = useCallback(
        (
            currentQuote: { index: TimeSeriesQuote; stock: TimeSeriesQuote },
            prevQuote: { index: TimeSeriesQuote; stock: TimeSeriesQuote }
        ) => {
            const change =
                currentQuote && prevQuote
                    ? Math.log(currentQuote.stock.close / prevQuote.stock.close)
                    : 0;

            const indexChange =
                currentQuote && prevQuote
                    ? Math.log(currentQuote.index.close / prevQuote.index.close)
                    : 0;

            return change - (risk.intercept + risk.slope * indexChange);
        },
        [risk]
    );
    const price = (quote: { index: TimeSeriesQuote; stock: TimeSeriesQuote }) =>
        quote.stock.close;

    const x = (quote: { index: TimeSeriesQuote; stock: TimeSeriesQuote }) =>
        new Date(quote.stock.datetime).valueOf();
    const xScale = useMemo(
        () =>
            scaleTime({
                domain: extent(quotes, (d) => x(d)) as number[],
                range: [0, width]
            }),
        [quotes, width]
    );

    const abnormalScale = useMemo(
        () =>
            scaleLinear({
                domain: extent(quotes, (d, index) =>
                    abnormal(d, quotes[index - 1])
                ) as number[],
                range: [height - VERTICAL_PADDING, VERTICAL_PADDING]
            }),
        [abnormal, height, quotes]
    );

    const handleTooltip = useCallback(
        (event: React.TouchEvent<SVGRectElement> | React.MouseEvent<SVGRectElement>) => {
            const { x, y } = localPoint(event) || { x: 0, y: 0 };

            const inverted = xScale.invert(x);
            const index = bisectDate(quotes, inverted, 1);
            const d0 = quotes[index - 1];
            const d1 = quotes[index];

            let d = d0;
            if (d1 && d1.stock.datetime) {
                d =
                    inverted.valueOf() - new Date(d0.stock.datetime).valueOf() >
                    new Date(d1.stock.datetime).valueOf() - inverted.valueOf()
                        ? d1
                        : d0;
            }
            const dPrev = d === d1 ? d0 : d1;

            if (d && dPrev) {
                showTooltip({
                    tooltipData: {
                        day: d.stock.datetime,
                        abnormal: abnormal(
                            { stock: d.stock, index: d.index },
                            { stock: dPrev.stock, index: dPrev.index }
                        ),
                        close: d.stock.close,
                        change: Math.log(d.stock.close / dPrev.stock.close)
                    },
                    tooltipLeft: xScale(new Date(d.stock.datetime)),
                    tooltipTop: y
                });
            }
        },
        [quotes, abnormal, showTooltip, xScale]
    );

    return (
        <>
            <svg width={width} height={height}>
                <line x1={0} y1={0} x2={0} y2={height} stroke="#d1d5db" />
                <line
                    x1={0}
                    y1={abnormalScale(0)}
                    x2={width}
                    y2={abnormalScale(0)}
                    stroke="#d1d5db"
                />

                <AbnormalLine
                    x={x}
                    xScale={xScale}
                    yScale={abnormalScale}
                    abnormal={abnormal}
                    quotes={quotes}
                />
                <PriceLine
                    x={x}
                    xScale={xScale}
                    price={price}
                    quotes={quotes}
                    height={height}
                />

                <rect
                    x={0}
                    y={0}
                    width={width}
                    height={height}
                    onTouchStart={handleTooltip}
                    onTouchMove={handleTooltip}
                    onMouseMove={handleTooltip}
                    onMouseLeave={() => hideTooltip()}
                    opacity={0}
                />

                {tooltipData && (
                    <g>
                        <Line
                            from={{ x: tooltipLeft, y: 0 }}
                            to={{ x: tooltipLeft, y: height }}
                            stroke="#cbd5e1"
                            strokeWidth={1}
                            pointerEvents="none"
                            strokeDasharray="5,2"
                        />
                    </g>
                )}
            </svg>
            {tooltipOpen && tooltipData && (
                <TooltipWithBounds
                    key={Math.random()}
                    top={tooltipTop! - 24}
                    left={tooltipLeft! + 8}
                    style={defaultStyles}>
                    <Tooltip tooltipData={tooltipData} />
                </TooltipWithBounds>
            )}
        </>
    );
}
