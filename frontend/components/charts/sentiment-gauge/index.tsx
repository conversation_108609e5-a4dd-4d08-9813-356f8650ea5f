import React, { useEffect, useMemo } from 'react';
import Pie, { ProvidedProps, PieArcDatum } from '@visx/shape/lib/shapes/Pie';
import { Group } from '@visx/group';

export interface GroupedCount {
    count: number;
    color: string;
}

export default function SentimentGauge({
    groups,
    width = 100
}: {
    groups: Array<GroupedCount>;
    width: number;
}) {
    const radius = width / 2;
    const centerX = width / 2;

    const count = (d: GroupedCount) => d.count;

    return (
        <svg width={radius * 2} height={radius}>
            <Group top={radius} left={centerX}>
                <Pie
                    data={groups}
                    pieValue={count}
                    outerRadius={radius}
                    startAngle={(Math.PI / 2) * -1}
                    pieSort={null}
                    endAngle={Math.PI / 2}
                    padAngle={0.04}
                    innerRadius={radius / 2}
                    fillOpacity={1}
                    fill={(it) => it.data.color}
                />
            </Group>
        </svg>
    );
}
