import { useMemo } from 'react';
import { Activity, ListedEntity, Organisation } from '@quarterback/types';
import activityFormat from '@/util/activityFormat';
import source from '@/util/source';

const colors = [
    '#0ea5e9',
    '#3b82f6',
    '#6366f1',
    '#8b5cf6',
    '#a855f7',
    '#d946ef',
    '#ec4899',
    '#f43f5e',
    '#ef4444',
    '#f97316',
    '#f59e0b',
    '#eab308',
    '#84cc16',
    '#22c55e',
    '#10b981',
    '#14b8a6',
    '#06b6d4'
];

export interface GroupedCount {
    label: string;
    count: number;
    color: string;
}

export default function useActivityPieData(
    entity: ListedEntity | undefined,
    activities: Array<Activity>,
    groupBy: 'source' | 'format',
    max: number = 6
): Array<GroupedCount> {
    return useMemo(() => {
        const grouped = activities.reduce(
            (acc, activity) => {
                const label =
                    groupBy === 'source' ? source(activity) : activityFormat(activity);

                if (!acc[label]) {
                    acc[label] = 0;
                }
                acc[label]++;
                return acc;
            },
            {} as Record<string, number>
        );

        const counts = Object.keys(grouped)
            .map((label) => ({
                label,
                count: grouped[label]
            }))
            .sort((a, b) => b.count - a.count)
            .map((group, ix) => ({ ...group, color: colors[ix] }));

        return [
            ...counts.slice(0, max),
            ...(counts.slice(max).length > 0
                ? [
                      {
                          label: 'Other',
                          count: counts
                              .slice(max)
                              .reduce((sum, group) => sum + group.count, 0),
                          color: '#cbd5e1'
                      }
                  ]
                : [])
        ];
    }, [activities, groupBy, max]);
}
