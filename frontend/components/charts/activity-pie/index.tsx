import React, { useEffect, useMemo } from 'react';
import Pie, { ProvidedProps, PieArcDatum } from '@visx/shape/lib/shapes/Pie';
import { Activity, ListedEntity } from '@quarterback/types';
import { ParentSize, withParentSize, WithParentSizeProvidedProps } from '@visx/responsive';
import { Group } from '@visx/group';
import { animated, useTransition, interpolate } from '@react-spring/web';
import useActivityPieData, {
    GroupedCount
} from '@/components/charts/activity-pie/useActivityPieData';

type AnimatedStyles = { startAngle: number; endAngle: number; opacity: number };

const fromLeaveTransition = ({ endAngle }: PieArcDatum<any>) => ({
    startAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
    endAngle: endAngle > Math.PI ? 2 * Math.PI : 0,
    opacity: 0
});

const enterUpdateTransition = ({ startAngle, endAngle }: PieArcDatum<any>) => ({
    startAngle,
    endAngle,
    opacity: 1
});

type AnimatedPieProps<Datum> = ProvidedProps<Datum> & {
    animate?: boolean;
    getKey: (d: PieArcDatum<Datum>) => string;
    getColor: (d: PieArcDatum<Datum>) => string;
    onClickDatum: (d: PieArcDatum<Datum>) => void;
    delay?: number;
};

function AnimatedPie<Datum>({
    animate,
    arcs,
    path,
    getKey,
    getColor,
    onClickDatum
}: AnimatedPieProps<Datum>) {
    const transitions = useTransition<PieArcDatum<Datum>, AnimatedStyles>(arcs, {
        from: animate ? fromLeaveTransition : enterUpdateTransition,
        enter: enterUpdateTransition,
        update: enterUpdateTransition,
        leave: animate ? fromLeaveTransition : enterUpdateTransition,
        keys: getKey
    });
    return transitions((props, arc, { key }) => {
        return (
            <g key={key}>
                <animated.path
                    // compute interpolated path d attribute from intermediate angle values
                    d={interpolate([props.startAngle, props.endAngle], (startAngle, endAngle) =>
                        path({
                            ...arc,
                            startAngle,
                            endAngle
                        })
                    )}
                    fill={getColor(arc)}
                    onClick={() => onClickDatum(arc)}
                    onTouchStart={() => onClickDatum(arc)}
                />
            </g>
        );
    });
}

export const ActivityPie = function ActivityPie({
    groups,
    width: parentWidth = 50,
    height: parentHeight = 50
}: {
    groups: Array<GroupedCount>;
    width: number;
    height: number;
}) {
    const width = parentWidth;
    const height = parentHeight;

    const radius = Math.min(width, height) / 2;
    const centerY = height / 2;
    const centerX = width / 2;
    const donutThickness = 15;

    const count = (d: GroupedCount) => d.count;

    return (
        <svg width={width} height={height}>
            <Group top={centerY} left={centerX}>
                <Pie
                    data={groups}
                    pieValue={count}
                    outerRadius={radius}
                    innerRadius={radius - donutThickness}
                    cornerRadius={3}
                    padAngle={0.005}>
                    {(pie) => (
                        <AnimatedPie<GroupedCount>
                            {...pie}
                            animate={false}
                            getKey={(arc) => arc.data.label}
                            onClickDatum={() => {}}
                            getColor={(arc) => arc.data.color}
                        />
                    )}
                </Pie>
            </Group>
        </svg>
    );
};

interface Props {
    activities: Array<Activity>;
    entity: ListedEntity | undefined;
    groupBy: 'source' | 'format';
    max?: number;
}

export default function ActivityPieWrapper({ activities, entity, groupBy, max = 6 }: Props) {
    const groups = useActivityPieData(entity, activities, groupBy, max);

    return (
        <>
            <div className="h-56 p-4 relative">
                <ParentSize>
                    {({ width, height }) => (
                        <ActivityPie width={width} height={height} groups={groups} />
                    )}
                </ParentSize>
                <div className="absolute top-0 bottom-0 left-0 right-0 flex flex-col items-center justify-center">
                    <span className="text-4xl">{activities.length}</span>
                    <span className="text-gray-400 mt-1">Activities</span>
                </div>
            </div>
            <div className="grid grid-cols-2 gap-y-1">
                {groups.map((it) => (
                    <div key={it.label} className="text-sm flex items-center gap-x-2">
                        <div
                            className={'w-3 h-3 rounded-full flex-shrink-0'}
                            style={{ background: it.color }}
                        />
                        <span className="truncate">{it.label}</span>
                    </div>
                ))}
            </div>
        </>
    );
}
