import React, { useEffect } from 'react';
import * as Sen<PERSON> from '@sentry/nextjs';
import { useUser } from '@/api/hooks/useUser';
import posthog from 'posthog-js';

export default function TrackedIdentityProvider({ children }: React.PropsWithChildren) {
    const { data: user } = useUser();

    useEffect(() => {
        if (user) {
            const name = `${user.metadata.first_name} ${user.metadata.last_name}`;

            Sentry.setUser({
                id: user.id,
                email: user.emails[0],
                username: name
            });

            posthog.identify(user.id, { email: user.emails[0], name });
        } else {
            Sentry.setUser(null);
        }
    }, [user]);

    return children;
}
