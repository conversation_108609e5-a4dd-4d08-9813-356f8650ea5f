import { Text, View } from '@react-pdf/renderer';
import SummaryStatistic from '@/components/reports/board-report/SummaryStatistic';
import React, { useMemo } from 'react';
import { Activity, ListedEntity } from '@quarterback/types';
import useActivityPieData from '@/components/charts/activity-pie/useActivityPieData';
import { useReactPdfSvg } from '@/components/pdf/useReactPdfSvg';
import ReportActivityPie from '@/components/reports/board-report/ReportActivityPie';
import { isDefined } from '@quarterback/util';
import useChatterVolumeData from '@/components/charts/chatter-volume/useChatterVolumeData';
import ChatterVolumeChart from '@/components/charts/chatter-volume';
import Legend from '@/components/reports/board-report/Legend';

interface Props {
    entity: ListedEntity | undefined;
    chatter: Array<Activity>;
    from: Date;
    to: Date;
}

export default function Chatter({ entity, chatter, from, to }: Props) {
    const groups = useActivityPieData(entity, chatter, 'source', 6);
    const days = useChatterVolumeData(chatter, from, to);

    const chatterVolumeChartDom = useReactPdfSvg(
        () => (
            <ChatterVolumeChart
                days={days}
                from={from}
                to={to}
                width={216}
                height={100}
            />
        ),
        [days, from, to]
    );

    const activitySourcePieDom = useReactPdfSvg(
        () => <ReportActivityPie width={120} height={120} groups={groups} />,
        [chatter, groups]
    );

    const authors = useMemo(() => {
        return new Set(
            chatter
                .map((activity) => {
                    switch (activity.type) {
                        case 'hotcopper':
                        case 'tweet':
                            return activity.author?.userId;
                        case 'reddit':
                            return activity.author?.userId;
                        case 'linkedIn':
                            return activity.author?.name;
                        case 'news':
                            return activity.source.url;
                        default:
                            return undefined;
                    }
                })
                .filter(isDefined)
        ).size;
    }, [chatter]);

    const views = useMemo(() => {
        const hotcopper = Object.values(
            chatter.reduce(
                (threadViews, activity) => {
                    if (activity.type === 'hotcopper' && activity.thread.thread) {
                        return {
                            ...threadViews,
                            [activity.thread.thread]:
                                activity.thread.views &&
                                activity.thread.views >
                                    (threadViews[activity.thread.thread] ?? 0)
                                    ? activity.thread.views
                                    : threadViews[activity.thread.thread]
                        };
                    } else {
                        return threadViews;
                    }
                },
                {} as Record<string, number>
            )
        ).reduce((sum, threadViews) => sum + threadViews, 0);

        const tweets = chatter.reduce((sum, activity) => {
            if (activity.type === 'tweet') {
                sum += activity.views;
            }

            return sum;
        }, 0);

        return tweets + hotcopper;
    }, [chatter]);

    return (
        <View
            style={{
                paddingLeft: 16,
                paddingRight: 16,
                marginTop: 8,
                display: 'flex',
                flexDirection: 'row',
                columnGap: 32
            }}>
            <View style={{ flex: 1, flexShrink: 0 }}>
                <View
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        columnGap: 16
                    }}>
                    <SummaryStatistic label="Mentions" value={`${chatter.length}`} />
                    <SummaryStatistic label="Authors" value={`${authors}`} />
                    <SummaryStatistic label="Views" value={`${views.toLocaleString()}`} />
                </View>
                {chatterVolumeChartDom}
                <Legend
                    entries={[
                        { label: 'Mentions', color: '#63ABFD' },
                        { label: 'Authors', color: '#E697FF' },
                        { label: 'Views', color: '#FFA5CB' }
                    ]}
                />
            </View>
            <View
                style={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    columnGap: 12,
                    flexShrink: 0
                }}>
                <View
                    style={{
                        position: 'relative',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                    }}>
                    <View>{activitySourcePieDom}</View>
                </View>
                <View>
                    {groups.map((group) => (
                        <View
                            key={group.label}
                            style={{
                                display: 'flex',
                                flexDirection: 'row',
                                alignItems: 'center',
                                columnGap: 4,
                                rowGap: 2
                            }}>
                            <View
                                style={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: 1000,
                                    backgroundColor: group.color
                                }}
                            />
                            <Text style={{ fontSize: 10 }}>{group.label}</Text>
                        </View>
                    ))}
                </View>
            </View>
        </View>
    );
}
