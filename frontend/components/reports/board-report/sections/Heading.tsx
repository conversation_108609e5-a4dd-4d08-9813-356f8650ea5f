import { formatWithTimeZone } from '@/util/date';
import { Image, Text, View } from '@react-pdf/renderer';

interface Props {
    from: Date;
    to: Date;
}

const DATE_FORMAT = 'd MMM yyyy';

export default function Heading({ from, to }: Props) {
    return (
        <View
            style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center'
            }}>
            <Image src="/reports/logo-full.png" style={{ height: 24, width: 126 }} />
            <Text style={{ fontSize: 12, fontFamily: 'Inter', fontWeight: 'normal' }}>
                {formatWithTimeZone(from, DATE_FORMAT)} to{' '}
                {formatWithTimeZone(to, DATE_FORMAT)}
            </Text>
        </View>
    );
}
