import { Style } from '@react-pdf/types';
import React, { useMemo } from 'react';
import { Path, Svg, Text, View } from '@react-pdf/renderer';

export default function SummaryStatistic({
    label,
    value,
    change,
    style
}: {
    label: string;
    value: string;
    change?: number;
    style?: Style | Style[];
}) {
    const changeStyle = useMemo((): Style => {
        if (change === undefined) {
            return {};
        } else if (change === 0) {
            // bg-gray-50 text-gray-600
            return { backgroundColor: '#f9fafb', color: '#4b5563' };
        } else if (change < 0) {
            return { backgroundColor: '#fee2e2', color: '#991b1b' };
        } else {
            // bg-green-50 text-green-700
            return { backgroundColor: '#f0fdf4', color: '#15803d' };
        }
    }, [change]);

    const changeIcon = useMemo(() => {
        if (change === undefined || change === 0) {
            return null;
        } else if (change < 0) {
            return (
                <Svg
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="#991b1b"
                    style={{ width: 8, height: 8 }}>
                    <Path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"
                    />
                </Svg>
            );
        } else if (change > 0) {
            return (
                <Svg
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="#15803d"
                    style={{ width: 8, height: 8 }}>
                    <Path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"
                    />
                </Svg>
            );
        }
    }, [change]);

    return (
        <View style={style}>
            <View
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>
                <Text style={{ fontSize: 24 }}>{value}</Text>
            </View>
            <View
                style={{
                    marginTop: 3,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>
                <Text style={{ fontSize: 12, color: 'gray' }}>{label}</Text>
                {change !== undefined ? (
                    <View
                        style={{
                            ...changeStyle,
                            paddingLeft: 4,
                            paddingRight: 4,
                            paddingTop: 1,
                            paddingBottom: 1,
                            borderRadius: 1000,
                            marginLeft: 2,
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center'
                        }}>
                        {changeIcon}

                        <Text
                            style={{
                                fontSize: 10,
                                marginLeft: 2
                            }}>
                            {Math.abs(change).toFixed(2)}%
                        </Text>
                    </View>
                ) : null}
            </View>
        </View>
    );
}
