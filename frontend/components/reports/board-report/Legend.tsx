import { Text, View } from '@react-pdf/renderer';
import React, { Fragment } from 'react';

interface LegendEntry {
    label: string;
    color: string;
}

export default function Legend({ entries }: { entries: LegendEntry[] }) {
    return (
        <View
            style={{
                marginTop: 8,
                fontSize: 10,
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center'
            }}>
            {entries.map((entry) => (
                <Fragment key={entry.label}>
                    <View
                        style={{
                            width: 8,
                            height: 8,
                            borderRadius: 1000,
                            backgroundColor: entry.color,
                            marginRight: 2
                        }}
                    />
                    <Text>{entry.label}</Text>
                    <View style={{ width: 8 }} />
                </Fragment>
            ))}
        </View>
    );
}
