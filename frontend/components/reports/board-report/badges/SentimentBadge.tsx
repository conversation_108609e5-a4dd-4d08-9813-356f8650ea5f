import { Path, Svg, Text, View } from '@react-pdf/renderer';
import { sentimentScore } from '@/util/sentiment';
import React from 'react';
import { Sentiment } from '@quarterback/types';

function PositiveIcon() {
    return (
        <Svg
            width={12}
            height={12}
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor">
            <Path
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M15.182 15.182a4.5 4.5 0 0 1-6.364 0M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z"
            />
        </Svg>
    );
}

function NegativeIcon() {
    return (
        <Svg
            width={12}
            height={12}
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor">
            <Path
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M15.182 16.318A4.486 4.486 0 0 0 12.016 15a4.486 4.486 0 0 0-3.198 1.318M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z"
            />
        </Svg>
    );
}

export default function SentimentBadge({ sentiment }: { sentiment: Sentiment }) {
    const score = sentimentScore(sentiment);

    return (
        <View
            style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end',
                columnGap: 2
            }}>
            {score < 0 ? <NegativeIcon /> : <PositiveIcon />}
            <Text
                style={{
                    fontSize: 10,
                    fontFamily: 'Inter',
                    fontWeight: 'normal',
                    textAlign: 'right'
                }}>
                {score.toFixed(2)}
            </Text>
        </View>
    );
}
