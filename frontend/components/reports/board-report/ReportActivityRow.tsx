import ReportActivityIcon from '@/components/reports/board-report/ReportActivityIcon';
import { formatWithTimeZone } from '@/util/date';
import useActivitySummary from '@/util/useActivitySummary';
import { Activity } from '@quarterback/types';
import { Text, View } from '@react-pdf/renderer';
import React from 'react';

export default function ReportActivityRow({
    activity,
    badge
}: {
    activity: Activity;
    badge?: React.ReactNode;
}) {
    const { title, description } = useActivitySummary(activity);

    return (
        <View
            wrap={false}
            style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                columnGap: 8,
                borderTop: '1px solid #f3f4f6',
                paddingVertical: 4
            }}>
            <ReportActivityIcon
                activity={activity}
                style={{ width: 20, height: 20, borderRadius: '50%' }}
            />
            <View style={{ flex: 1 }}>
                {title ? (
                    <Text style={{ fontSize: 11, marginBottom: 4 }}>{title}</Text>
                ) : null}
                {description ? (
                    <Text
                        style={{
                            fontSize: 10,
                            color: 'gray',
                            maxLines: title ? 1 : 2,
                            textOverflow: 'ellipsis'
                        }}>
                        {description.replace('\n', ' ')}
                    </Text>
                ) : null}
            </View>
            <View
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-end'
                }}>
                <Text style={{ fontSize: 10, color: 'gray', marginBottom: 4 }}>
                    {formatWithTimeZone(activity.posted, 'MMM d')}
                </Text>
                {badge && badge}
            </View>
        </View>
    );
}
