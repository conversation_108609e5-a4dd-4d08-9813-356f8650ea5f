import { Activity } from '@quarterback/types';
import { Style } from '@react-pdf/types';
import { Image } from '@react-pdf/renderer';
import React from 'react';

export default function ReportActivityIcon({
    activity,
    style
}: {
    activity: Activity;
    style: Style;
}) {
    if (activity.type === 'tweet') {
        return <Image src="/reports/x.png" style={style} />;
    } else if (activity.type === 'asx-announcement') {
        return <Image src="/reports/asx.png" style={style} />;
    } else if (activity.type === 'hotcopper') {
        return <Image src="/reports/hotcopper.png" style={style} />;
    } else {
        return <Image src="/reports/news.png" style={style} />;
    }
}
