import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { formatWithTimeZone } from '@/util/date';
import useActivitySummary from '@/util/useActivitySummary';
import { Activity, Risk } from '@quarterback/types';
import { Path, Svg, Text, View } from '@react-pdf/renderer';
import { useMemo } from 'react';

export default function ReportAnnouncementRow({
    activity,
    chatter,
    risk,
    quote,
    index
}: {
    activity: Activity;
    risk: Risk | undefined;
    quote: TimeSeriesQuote | undefined;
    index: TimeSeriesQuote | undefined;
    chatter: Array<Activity>;
}) {
    const { title, description } = useActivitySummary(activity);

    const change = quote ? quote.close / quote.open - 1 : 0;

    const changeColor = useMemo(() => {
        if (change === 0) {
            return '#6b7280';
        } else if (change < 0) {
            return '#991b1b';
        } else {
            return '#15803d';
        }
    }, [change]);

    const abnormal = useMemo(() => {
        if (quote && index && risk) {
            const change = Math.log(quote.close / quote.open);
            const indexChange = Math.log(index.close / index.open);

            return change - (risk.intercept + risk.slope * indexChange);
        } else {
            return undefined;
        }
    }, [quote, risk, index]);

    const changeIcon = useMemo(() => {
        if (change === undefined || change === 0) {
            return null;
        } else if (change < 0) {
            return (
                <Svg
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="#991b1b"
                    style={{ width: 8, height: 8 }}>
                    <Path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"
                    />
                </Svg>
            );
        } else if (change > 0) {
            return (
                <Svg
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="#15803d"
                    style={{ width: 8, height: 8 }}>
                    <Path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"
                    />
                </Svg>
            );
        }
    }, [change]);

    return (
        <View
            wrap={false}
            style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                columnGap: 8,
                borderTop: '1px solid #f3f4f6',
                paddingVertical: 4
            }}>
            <View style={{ flex: 1 }}>
                {title ? <Text style={{ fontSize: 11 }}>{title}</Text> : null}
            </View>
            <View
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-end',
                    columnGap: 4
                }}>
                <View
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        width: 46
                    }}>
                    <Text
                        style={{
                            fontFamily: 'Inter',
                            fontWeight: 'normal',
                            fontSize: 10,
                            textAlign: 'right'
                        }}>
                        {chatter.length}
                    </Text>
                </View>
                <View
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        columnGap: 4,
                        width: 56
                    }}>
                    <Text
                        style={{
                            fontFamily: 'Inter',
                            fontWeight: 'normal',
                            fontSize: 10,
                            textAlign: 'right'
                        }}>
                        {Math.abs((abnormal ?? 0) * 100).toFixed(2)}%
                    </Text>
                    <Text
                        style={{
                            fontFamily: 'Inter',
                            fontWeight: 'normal',
                            fontSize: 8,
                            color: 'gray'
                        }}>
                        AR
                    </Text>
                </View>
                <View
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        width: 46
                    }}>
                    {changeIcon}
                    <Text
                        style={{
                            fontFamily: 'Inter',
                            fontWeight: 'normal',
                            fontSize: 10,
                            textAlign: 'right',
                            color: changeColor
                        }}>
                        {(change * 100).toFixed(2)}%
                    </Text>
                </View>
                <View style={{ width: 32 }}>
                    <Text
                        style={{
                            fontFamily: 'Inter',
                            fontWeight: 'normal',
                            fontSize: 10,
                            textAlign: 'right',
                            color: 'gray'
                        }}>
                        {formatWithTimeZone(activity.posted, 'MMM d')}
                    </Text>
                </View>
            </View>
        </View>
    );
}
