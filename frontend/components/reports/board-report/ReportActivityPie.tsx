import React, { useMemo } from 'react';
import { ActivityPie } from '@/components/charts/activity-pie';
import { GroupedCount } from '@/components/charts/activity-pie/useActivityPieData';

interface Props {
    width: number;
    height: number;
    groups: Array<GroupedCount>;
}

export default function ReportActivityPie({ groups, width, height }: Props) {
    return <ActivityPie width={width} height={height} groups={groups} />;
}
