import useActivities from '@/api/hooks/useActivities';
import useFollowers from '@/api/hooks/useFollowers';
import useRisk from '@/api/hooks/useRisk';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import { ListedEntity, Organisation } from '@quarterback/types';
import { useMemo } from 'react';

export default function useBoardReportData(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined,
    from: Date,
    to: Date
) {
    const { data: risk, isLoading: riskLoading } = useRisk(entity, {
        symbol: 'AXJO',
        exchange: 'ASX'
    });

    const { data: timeSeries, isLoading: timeSeriesLoading } = useTimeSeries(
        entity,
        from,
        to
    );
    const { data: indexSeries, isLoading: indexSeriesLoading } = useTimeSeries(
        { symbol: 'AXJO', exchange: 'ASX' },
        from,
        to
    );

    const { data: followers, isLoading: followersLoading } = useFollowers(
        entity,
        from,
        to
    );

    const { data: activities, isLoading: activitiesLoading } = useActivities(
        organisation,
        entity,
        from,
        to
    );

    const loading = useMemo(
        () =>
            riskLoading ||
            timeSeriesLoading ||
            indexSeriesLoading ||
            followersLoading ||
            activitiesLoading,
        [
            activitiesLoading,
            followersLoading,
            indexSeriesLoading,
            riskLoading,
            timeSeriesLoading
        ]
    );

    return useMemo(
        () => ({
            organisation,
            entity,
            timeSeries,
            indexSeries,
            risk,
            activities: activities ?? [],
            followers:
                +(followers?.twitter?.[0]?.followers ?? 0) +
                +(followers?.linkedIn?.[0]?.followers ?? 0),
            loading
        }),
        [
            entity,
            activities,
            followers?.twitter,
            followers?.linkedIn,
            indexSeries,
            loading,
            organisation,
            risk,
            timeSeries
        ]
    );
}
