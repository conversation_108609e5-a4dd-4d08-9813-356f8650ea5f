import { Text, View } from '@react-pdf/renderer';
import React from 'react';
import { styleSheet } from '@/components/reports/board-report/index';
import { Style } from '@react-pdf/types';

interface Props {
    title?: string;
    caption?: string;
    style?: Style;
}

export default function Section({
    title,
    caption,
    children,
    style
}: Props & React.PropsWithChildren) {
    return (
        <View style={[{ paddingHorizontal: 42, marginTop: 16 }, style ?? {}]}>
            {title && <Text style={styleSheet.heading}>{title}</Text>}
            {caption && <Text style={styleSheet.description}>{caption}</Text>}
            <View style={{ marginTop: 8 }}>{children}</View>
        </View>
    );
}
