import { Text, View } from '@react-pdf/renderer';
import React from 'react';

export default function HorizontalBarChart({ entries }: { entries: Array<[string, number]> }) {
    const max = Math.max(...entries.map((it) => it[1]));

    return (
        <View style={{ display: 'flex', flexDirection: 'column', rowGap: 2 }}>
            {entries.map(([label, count]) => (
                <View
                    key={label}
                    style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                    <View
                        style={{
                            flex: 1,
                            position: 'relative',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-start',
                            justifyContent: 'center'
                        }}>
                        <View
                            style={{
                                backgroundColor: '#a5b4fc',
                                borderRadius: 4,
                                overflow: 'hidden',
                                width: `${((count / max) * 100).toFixed(2)}%`,
                                height: 18
                            }}
                        />
                        <View style={{ position: 'absolute' }}>
                            <Text
                                key={label}
                                style={{
                                    fontFamily: 'Inter',
                                    fontWeight: 'normal',
                                    fontSize: 10,
                                    paddingHorizontal: 4,
                                    maxLines: 1,
                                    textOverflow: 'ellipsis'
                                }}>
                                {label}
                            </Text>
                        </View>
                    </View>
                    <View style={{ width: 28, textAlign: 'center' }}>
                        <Text
                            style={{
                                fontFamily: 'Inter',
                                fontWeight: 'normal',
                                fontSize: 10
                            }}>
                            {count}
                        </Text>
                    </View>
                </View>
            ))}
        </View>
    );
}
