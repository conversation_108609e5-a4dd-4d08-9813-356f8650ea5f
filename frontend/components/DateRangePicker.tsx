import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import React, { useMemo, useState } from 'react';
import { DateRange, DayPicker } from 'react-day-picker';
import {
    endOfQuarter,
    format,
    startOfDay,
    startOfQuarter,
    sub,
    subQuarters
} from 'date-fns';
import {
    CalendarDaysIcon,
    CalendarIcon,
    ChevronUpDownIcon
} from '@heroicons/react/24/outline';
import { ArrowRightIcon } from '@heroicons/react/16/solid';

import 'react-day-picker/dist/style.css';
import classNames from 'classnames';

export default function DateRangePicker({
    id,
    range,
    setRange,
    className,
    required
}: {
    id?: string;
    range: DateRange;
    setRange: (range: DateRange) => void;
    className?: string;
    required?: boolean;
}) {
    const start = useMemo(() => {
        return range?.from ? format(range.from, 'd MMM, yyyy') : undefined;
    }, [range]);

    const end = useMemo(() => {
        return range?.to ? format(range.to, 'd MMM, yyyy') : undefined;
    }, [range]);

    function handleSelect(newRange: DateRange | undefined) {
        setRange({
            from: newRange?.from ?? range?.from,
            to: newRange?.to ?? range?.to
        });
    }

    function clear() {
        setRange({
            from: undefined,
            to: undefined
        });
    }

    return (
        <Popover className="relative">
            <PopoverButton
                id={id}
                className={classNames(
                    className,
                    'relative w-full cursor-default rounded-md min-h-9 bg-white py-1.5 pl-9 pr-10 text-left text-gray-900 ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6'
                )}>
                <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2">
                    <CalendarDaysIcon className="size-5 text-gray-400" />
                </span>
                <div className="flex flex-row items-center font-medium text-gray-600 gap-x-2">
                    {start || end ? (
                        <>
                            <span className="truncate">{start}</span>
                            <ArrowRightIcon className="size-4" />
                            <span className="truncate">{end}</span>
                        </>
                    ) : (
                        <span className="truncate">Select a date range</span>
                    )}
                </div>
                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                    <ChevronUpDownIcon
                        aria-hidden="true"
                        className="h-5 w-5 text-gray-400"
                    />
                </span>
            </PopoverButton>
            <PopoverPanel
                anchor={{
                    to: 'bottom start',
                    gap: '0.5rem',
                    padding: '0.5rem'
                }}
                className="flex flex-col bg-white z-50 rounded-xl py-4 px-6 shadow-md gap-y-4">
                <div
                    className={classNames('flex flex-row items-center', {
                        'justify-between': !required,
                        'justify-end': required
                    })}>
                    {!required && (
                        <span
                            className="text-sm text-indigo-600 font-medium cursor-pointer hover:underline"
                            onClick={clear}>
                            Clear
                        </span>
                    )}
                    <div className="flex justify-end items-center gap-x-2">
                        <button
                            type="button"
                            onClick={() =>
                                setRange({
                                    from: sub(startOfDay(new Date()), { days: 30 }),
                                    to: startOfDay(new Date())
                                })
                            }
                            className="rounded-md bg-indigo-50 px-2.5 py-1.5 text-sm font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100">
                            Past 30 days
                        </button>
                        <button
                            type="button"
                            onClick={() =>
                                setRange({
                                    from: startOfQuarter(new Date()),
                                    to: startOfDay(new Date())
                                })
                            }
                            className="rounded-md bg-indigo-50 px-2.5 py-1.5 text-sm font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100">
                            Current quarter
                        </button>
                        <button
                            type="button"
                            onClick={() =>
                                setRange({
                                    from: subQuarters(startOfQuarter(new Date()), 1),
                                    to: startOfDay(
                                        subQuarters(endOfQuarter(new Date()), 1)
                                    )
                                })
                            }
                            className="rounded-md bg-indigo-50 px-2.5 py-1.5 text-sm font-semibold text-indigo-600 shadow-sm hover:bg-indigo-100">
                            Last quarter
                        </button>
                    </div>
                </div>

                <div className="border-gray-100 border-b" />
                <DayPicker
                    numberOfMonths={2}
                    pagedNavigation
                    defaultMonth={range.to}
                    classNames={{
                        months: 'flex gap-x-4',
                        caption_label: 'leading-none',
                        selected: '', // This somehow fixes the calendar style
                        range_middle: 'bg-indigo-50',
                        range_start: 'rounded-l-lg bg-indigo-600 text-gray-50',
                        range_end: 'rounded-r-lg bg-indigo-600 text-gray-50',
                        today: '!font-semibold'
                    }}
                    disabled={{ after: new Date() }}
                    mode="range"
                    selected={range}
                    onSelect={handleSelect}
                />
            </PopoverPanel>
        </Popover>
    );
}
