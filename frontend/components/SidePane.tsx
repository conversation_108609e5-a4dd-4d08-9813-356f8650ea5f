import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react';
import { ChevronRightIcon, PencilIcon } from '@heroicons/react/24/outline';
import { Fragment } from 'react';

function SidePane({
    breadCrumbs,
    open,
    confirmButtonText = 'Confirm',
    cancelButtonText = 'Cancel',
    isDisabledConfirm = false,
    isDsabledCancel = false,
    onConfirm = () => {},
    onCancel = () => {},
    onClose = () => {},
    children
}: {
    open: boolean;
    confirmButtonText?: string;
    cancelButtonText?: string;
    isDisabledConfirm?: boolean;
    isDsabledCancel?: boolean;
    onClose?: (arg?: any) => void;
    onConfirm?: (arg?: any) => void;
    onCancel?: (arg?: any) => void;
    breadCrumbs?: Array<string> | undefined;
} & React.PropsWithChildren) {
    return (
        <Dialog className="relative z-30" open={open} onClose={onClose}>
            <DialogBackdrop
                transition
                className="fixed z-10 inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-500 ease-in-out data-[closed]:opacity-0"
            />

            <div className="fixed inset-0 overflow-hidden z-30">
                <div className="absolute inset-0 overflow-hidden">
                    <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                        <DialogPanel
                            transition
                            className="pointer-events-auto w-screen max-w-md transform transition duration-500 ease-in-out data-[closed]:translate-x-full sm:duration-700">
                            <div className="flex h-full flex-col  bg-white py-6 shadow-xl px-4 sm:px-6">
                                {breadCrumbs?.length ? (
                                    <div className="flex items-center gap-2">
                                        {breadCrumbs.map((item, index) => (
                                            <Fragment key={index}>
                                                {index === 0 ? (
                                                    <span className="flex items-center space-x-1 text-indigo-500 hover:text-indigo-600 bg-indigo-100 rounded-lg px-2 py-2">
                                                        <PencilIcon
                                                            className="h-3 w-3"
                                                            aria-hidden="true"
                                                        />
                                                        <span className="text-sm font-medium">
                                                            {item}
                                                        </span>
                                                    </span>
                                                ) : (
                                                    <span className="text-sm font-medium text-gray-500 hover:text-gray-700 rounded-lg py-2">
                                                        {item}
                                                    </span>
                                                )}

                                                {index !== breadCrumbs.length - 1 && (
                                                    <ChevronRightIcon
                                                        className="h-4 w-4  text-gray-500"
                                                        strokeWidth={3}
                                                    />
                                                )}
                                            </Fragment>
                                        ))}
                                    </div>
                                ) : null}
                                <div className="h-0 flex-1 overflow-y-auto mt-4">
                                    {children}
                                </div>
                                <div className="flex flex-shrink-0 justify-end gap-x-6 mt-4">
                                    <button
                                        type="button"
                                        className="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400 disabled:bg-gray-200 disabled:pointer-events-none"
                                        onClick={onCancel}
                                        disabled={isDsabledCancel}>
                                        {cancelButtonText}
                                    </button>
                                    <button
                                        type="button"
                                        className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:bg-indigo-200 disabled:pointer-events-none"
                                        onClick={onConfirm}
                                        disabled={isDisabledConfirm}>
                                        {confirmButtonText}
                                    </button>
                                </div>
                            </div>
                        </DialogPanel>
                    </div>
                </div>
            </div>
        </Dialog>
    );
}

export default SidePane;
