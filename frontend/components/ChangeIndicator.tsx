import React from 'react';
import classNames from 'classnames';
import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/16/solid';

interface Props {
    value: number;
    label: string;
}

export default function ChangeIndicator({ value, label }: Props) {
    return (
        <span
            className={classNames(
                'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium flex-column ',
                {
                    'text-red-700 bg-red-100': value < 0,
                    'text-gray-600 bg-gray-100': value === 0,
                    'text-green-700 bg-green-100': value > 0
                }
            )}>
            {label}
        </span>
    );
}
