import { DependencyList, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { createRoot } from 'react-dom/client';
import { convertToReactPdfSvg } from './convertToReactPdf';

export function useReactPdfSvg(renderFn: () => ReactNode, dependencies: DependencyList) {
    const [svgPdfDom, setSvgPdfDom] = useState<ReactNode>(null);

    const fn = useCallback(renderFn, dependencies);

    useEffect(() => {
        const dom = document.createElement('div');

        const root = createRoot(dom);

        root.render(fn());

        /*
        Unbelievably this pattern of using `requestIdleCallback` is a React core team recommendation
        see: https://github.com/reactwg/react-18/discussions/5
        */
        // Even more unbelievably Safari doesn't support requestIdleCallback.
        function callback() {
            const visXDom = dom.querySelector('svg');

            if (visXDom) {
                setSvgPdfDom(convertToReactPdfSvg(visXDom));
            }

            root.unmount();
        }

        if (window.requestIdleCallback !== undefined) {
            requestIdleCallback(callback);
        } else {
            setTimeout(callback, 1);
        }
    }, [fn]);

    return svgPdfDom;
}
