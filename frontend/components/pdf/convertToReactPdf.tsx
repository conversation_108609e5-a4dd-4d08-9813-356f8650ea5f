import { ReactNode } from 'react';

import {
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Defs,
    G,
    <PERSON>,
    Path,
    Rect,
    Svg,
    Text as ReactPdfText,
    Tspan
} from '@react-pdf/renderer';
import { SVGPresentationAttributes } from '@react-pdf/types';
import { LinearGradient } from '@visx/gradient';

function extractPresentationAttrs(node: Node): SVGPresentationAttributes {
    if (node instanceof Element) {
        const attributes = node.attributes;

        const undefinedSafeProp = (name: string, key: string = name) => {
            const attr = attributes.getNamedItem(key)?.value;

            if (attr === null || attr === undefined || attr === '') return {};

            return { [name]: attr };
        };

        return {
            ...undefinedSafeProp('fill'),

            ...undefinedSafeProp('color'),
            ...undefinedSafeProp('stroke'),

            ...undefinedSafeProp('transform'),
            ...undefinedSafeProp('strokeDasharray', 'stroke-dasharray'),

            ...undefinedSafeProp('opacity'),

            ...undefinedSafeProp('strokeWidth', 'stroke-width'),
            ...undefinedSafeProp('fillOpacity', 'fill-opacity'),

            ...undefinedSafeProp('fillRule', 'fill-rule'),
            ...undefinedSafeProp('strokeOpacity', 'stroke-opacity'),

            ...undefinedSafeProp('textAnchor', 'text-anchor'),

            ...undefinedSafeProp('strokeLineCap', 'stroke-line-cap'),
            ...undefinedSafeProp('strokeLinejoin', 'stroke-line-join'),

            ...undefinedSafeProp('visibility'),

            ...undefinedSafeProp('clipPath', 'clip-path'),

            ...undefinedSafeProp('dominantBaseline', 'dominant-baseline')
        };
    }

    return {};
}

export function convertToReactPdfSvg(dom: SVGSVGElement): ReactNode {
    function convertSvgChildNode(node: ChildNode): ReactNode | ReactNode[] {
        const attributes = node instanceof Element ? node.attributes : undefined;

        const undefinedSafeProp = (name: string, key: string = name) => {
            const attr = attributes?.getNamedItem(key)?.value;

            if (attr === null || attr === undefined || attr === '') return {};

            return { [name]: attr };
        };

        switch (node.nodeName) {
            case 'g': {
                return (
                    <G {...extractPresentationAttrs(node)}>
                        {Array.from(node.childNodes).map((node) => convertSvgChildNode(node))}
                    </G>
                );
            }
            case 'line': {
                return (
                    //@ts-ignore
                    <Line
                        {...undefinedSafeProp('x1')}
                        {...undefinedSafeProp('y1')}
                        {...undefinedSafeProp('x2')}
                        {...undefinedSafeProp('y2')}
                        {...extractPresentationAttrs(node)}
                    />
                );
            }
            case 'rect': {
                return (
                    //@ts-ignore
                    <Rect
                        {...undefinedSafeProp('x')}
                        {...undefinedSafeProp('y')}
                        {...undefinedSafeProp('width')}
                        {...undefinedSafeProp('height')}
                        {...undefinedSafeProp('rx')}
                        {...undefinedSafeProp('ry')}
                        {...extractPresentationAttrs(node)}
                    />
                );
            }
            case 'circle': {
                return (
                    //@ts-ignore
                    <Circle
                        {...undefinedSafeProp('r')}
                        {...undefinedSafeProp('cy')}
                        {...undefinedSafeProp('cx')}
                        {...extractPresentationAttrs(node)}
                    />
                );
            }
            case 'path': {
                //@ts-ignore
                return <Path {...undefinedSafeProp('d')} {...extractPresentationAttrs(node)} />;
            }
            case 'image': {
                return [];
            }
            // case 'defs': {
            //     return (
            //         <Defs>
            //             {Array.from(node.childNodes).map((node) => convertSvgChildNode(node))}
            //         </Defs>
            //     );
            // }
            // case 'clipPath': {
            //     return (
            //         <ClipPath>
            //             {Array.from(node.childNodes).map((node) => convertSvgChildNode(node))}
            //         </ClipPath>
            //     );
            // }
            /* case 'linearGradient': {
                const attributes = (node as SVGLinearGradientElement).attributes;

                return (
                    <LinearGradient
                        id={attributes.getNamedItem('id')!.value}
                        x1={attributes.getNamedItem('x1')?.value}
                        y1={attributes.getNamedItem('y1')?.value}
                        x2={attributes.getNamedItem('x2')?.value}
                        y2={attributes.getNamedItem('y2')?.value}>
                        {Array.from(node.childNodes).map((node) => convertSvgChildNode(node))}
                    </LinearGradient>
                );
            }
            case "stop": {
                const attributes = (node as SVGStopElement).attributes

                return <Stop
                    offset={attributes.getNamedItem("offset")!.value}
                    stopColor={attributes.getNamedItem("stop-color")!.value}
                    stopOpacity={attributes.getNamedItem("stop-opacity")?.value}
                />
            }*/
            case 'svg':
                return Array.from(node.childNodes).map((node) => convertSvgChildNode(node));
            case 'text': {
                return (
                    <ReactPdfText
                        style={{
                            fontSize: '10px'
                        }}
                        {...undefinedSafeProp('x')}
                        {...undefinedSafeProp('y')}
                        {...extractPresentationAttrs(node)}>
                        {Array.from(node.childNodes).map((node) => convertSvgChildNode(node))}
                    </ReactPdfText>
                );
            }
            case 'tspan': {
                return (
                    <Tspan
                        {...undefinedSafeProp('x')}
                        {...undefinedSafeProp('y')}
                        {...extractPresentationAttrs(node)}>
                        {Array.from(node.childNodes).map((node) => convertSvgChildNode(node))}
                    </Tspan>
                );
            }
            case '#text': {
                return (node as Text).wholeText;
            }
        }

        return [];
    }

    return (
        <Svg
            width={dom.attributes.getNamedItem('width')?.value}
            height={dom.attributes.getNamedItem('height')?.value}>
            {Array.from(dom.childNodes).map((node) => convertSvgChildNode(node))}
        </Svg>
    );
}
