import { ReactNode } from "react"

function convertType(type: any): string {
    const stringed = typeof type === "symbol"
        //@ts-ignore
        ? type.description
        : type

    switch (stringed) {
        case "DEFS": return "Defs"
        case "LINE": return "Line"
        case "RECT": return "Rect"
        case "PATH": return "Path"
        case "SVG": return "Svg"
        case "TSPAN": return "Tspan"
        case "TEXT": return "Text"
        case "react.fragment": return ""
    }

    return stringed
}

function serializePropValue(value: any): string {
    if (typeof value === "string") return `"${value.replaceAll("\"", "\\\"")}"`

    return `${value}`
}

const PRETTY_PRINT_INDENT_CHARS = "  "

function prettyPrintReactNodeInternal(node: ReactNode, depth: number, out: string[]) {
    const indent = PRETTY_PRINT_INDENT_CHARS.repeat(depth)

    if (node && typeof node === "object") {
        if ("props" in node) {
            const { props } = node 
            const type = convertType(node.type)

            const tagProps = Object.keys(props).flatMap(k => {
                if (k === "children") return []
                if (props[k] === undefined) return []

                return [{k, v: props[k]}]
            })

            out.push("\n", indent, `<${type}`)

            if (tagProps.length > 10) {
                const propIndent = `${PRETTY_PRINT_INDENT_CHARS}${indent}`

                tagProps.forEach(({ k, v }) => {
                    out.push("\n", propIndent, k, "=", serializePropValue(v))
                })

                out.push("\n", indent)
            } else if (tagProps.length > 0) {
                tagProps.forEach(({ k, v }) => {
                    out.push(" ", k, "=", serializePropValue(v))
                })
            }

            if ("children" in props && props.children && (!Array.isArray(props.children) || props.children.length > 0)) {
                out.push(">")

                prettyPrintReactNodeInternal(props.children, depth + 1, out)

                out.push("\n", indent, `</${type}>`)
            } else {
                out.push(" />")
            }
        } else if (Array.isArray(node)) {
            node.forEach(child => prettyPrintReactNodeInternal(child, depth, out))
        }
    } else if (typeof node === "string") {
        out.push("\n", indent, `${node}`)
    }
}

/* 
This function prints react-pdf SVG JSX so that problematic SVGs can be debugged in the REPL at https://react-pdf.org/repl

The react-pdf implementation of SVG turns out to be pretty buggy. Internal errors
are frequent on valid SVGs - particularly the complex generated ones from visx.
Using the repl allows us to figure out what's tripping react-pdf up so we can
workaround the bugs */
export function prettyPrintReactNode(node: ReactNode): string {
    const out: string[] = []

    prettyPrintReactNodeInternal(node, 0, out)

    return out.join("")
}
