import React, { useMemo } from 'react';
import classNames from 'classnames';
import activityFormat from '@/util/activityFormat';
import { Activity, Activity2 } from '@quarterback/types';

export function ActivityFormatIndicator({
    activity,
}: {
    activity: Activity | Activity2;
}) {
    const format = useMemo(() => activityFormat(activity), [activity]);

    function getActivityFormatChipColor() {
        let bgColor: string;
        let textColor: string;

        switch (format) {
            case 'Publication':
                bgColor = 'bg-red-100';
                textColor = 'text-red-600';
                break;
            case 'Announcement':
                bgColor = 'bg-blue-100';
                textColor = 'text-blue-600';
                break;
            case 'Broadcast':
                bgColor = 'bg-lime-100';
                textColor = 'text-lime-700';
                break;
            case 'Chatter':
                bgColor = 'bg-yellow-100';
                textColor = 'text-yellow-700';
                break;
            default:
                bgColor = 'bg-gray-200';
                textColor = 'text-gray-700';
        }

        return (
            <span
                className={classNames(
                    bgColor,
                    textColor,
                    'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ring-gray-300'
                )}
            >
                {format}
            </span>
        );
    }

    return <>{getActivityFormatChipColor()}</>;
}

export default ActivityFormatIndicator;
