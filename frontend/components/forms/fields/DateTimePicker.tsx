import {
    Listbox,
    ListboxButton,
    ListboxOption,
    ListboxOptions,
    Popover,
    PopoverButton,
    PopoverPanel
} from '@headlessui/react';
import { CalendarDaysIcon, ChevronUpDownIcon } from '@heroicons/react/24/outline';
import { DateTimeFieldProps } from '@quarterback/types';
import { getTimeZones } from '@vvo/tzdb';
import classNames from 'classnames';
import { formatInTimeZone } from 'date-fns-tz';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { createDate } from '@/util/date';

export interface DateTimePickerProps extends DateTimeFieldProps {
    label: string;
    required?: boolean;
}

export function DateTimePicker({
    label,
    value,
    onChange,
    onBlur,
    error,
    required = false,
    timezone = 'Australia/Sydney',
    onTimezoneChange
}: DateTimePickerProps) {
    const defaultDate = useMemo(() => {
        if (value) return value;
        return createDate();
    }, [value]);

    const [selectedDate, setSelectedDate] = useState<Date | undefined>(defaultDate);
    const [selectedHour, setSelectedHour] = useState<string>(
        defaultDate ? formatInTimeZone(defaultDate, timezone, 'HH') : '00'
    );
    const [selectedMinute, setSelectedMinute] = useState<string>(
        defaultDate ? formatInTimeZone(defaultDate, timezone, 'mm') : '00'
    );
    const [selectedTimezone, setSelectedTimezone] = useState<string>(timezone);

    useEffect(() => {
        if (value) {
            setSelectedDate(value);
            setSelectedHour(formatInTimeZone(value, selectedTimezone, 'HH'));
            setSelectedMinute(formatInTimeZone(value, selectedTimezone, 'mm'));
        }
    }, [value, selectedTimezone]);

    const formattedDate = useMemo(() => {
        if (!selectedDate) return 'Select a date';
        return formatInTimeZone(selectedDate, selectedTimezone, 'd MMM, yyyy');
    }, [selectedDate, selectedTimezone]);

    // Get timezone options from @vvo/tzdb
    const timezoneOptions = useMemo(() => {
        return getTimeZones().map((tz) => ({
            value: tz.name,
            label: `${tz.name} (${tz.abbreviation})`
        }));
    }, []);

    const selectedTimezoneOption = useMemo(() => {
        return (
            timezoneOptions.find((tz) => tz.value === selectedTimezone) ||
            timezoneOptions[0]
        );
    }, [selectedTimezone, timezoneOptions]);

    const updateDateTime = useCallback(
        (date: Date | undefined, hour: string, minute: string, tz: string) => {
            if (!date) return;

            const newDate = createDate({
                date,
                hour,
                minute,
                second: '00',
                timezone: tz
            });

            onChange(newDate);
        },
        [onChange]
    );

    // Handle timezone change
    const handleTimezoneChange = useCallback(
        (newTimezone: string) => {
            setSelectedTimezone(newTimezone);
            if (onTimezoneChange) {
                onTimezoneChange(newTimezone);
            }
            if (selectedDate) {
                updateDateTime(selectedDate, selectedHour, selectedMinute, newTimezone);
            }
        },
        [onTimezoneChange, selectedDate, selectedHour, selectedMinute, updateDateTime]
    );

    // Handle hour change
    const handleHourChange = useCallback(
        (hour: string) => {
            setSelectedHour(hour);
            if (selectedDate) {
                updateDateTime(selectedDate, hour, selectedMinute, selectedTimezone);
            }
        },
        [selectedDate, selectedMinute, selectedTimezone, updateDateTime]
    );

    // Handle minute change
    const handleMinuteChange = useCallback(
        (minute: string) => {
            setSelectedMinute(minute);
            if (selectedDate) {
                updateDateTime(selectedDate, selectedHour, minute, selectedTimezone);
            }
        },
        [selectedDate, selectedHour, selectedTimezone, updateDateTime]
    );

    // Generate hour options (00-23)
    const hourOptions = useMemo(() => {
        return Array.from({ length: 24 }, (_, i) => {
            const hour = i.toString().padStart(2, '0');
            return { value: hour, label: hour };
        });
    }, []);

    // Generate minute options (00-59)
    const minuteOptions = useMemo(() => {
        return Array.from({ length: 60 }, (_, i) => {
            const minute = i.toString().padStart(2, '0');
            return { value: minute, label: minute };
        });
    }, []);

    // Handle day selection
    const handleDaySelect = useCallback(
        (day: Date | undefined) => {
            if (day) {
                setSelectedDate(day);
                updateDateTime(day, selectedHour, selectedMinute, selectedTimezone);
            }
        },
        [selectedHour, selectedMinute, selectedTimezone, updateDateTime]
    );

    // Handle blur event
    const handleBlur = useCallback(() => {
        if (onBlur) {
            onBlur();
        }
    }, [onBlur]);

    return (
        <div>
            <label className="block text-sm font-medium text-gray-700">
                {label}
                {required && <span className="text-indigo-500">*</span>}
            </label>
            <div className="mt-1 flex space-x-2">
                {/* Date Picker */}
                <Popover className="relative flex-grow">
                    <PopoverButton
                        className={classNames(
                            'relative w-full cursor-default rounded-md bg-white py-1.5 pl-9 pr-10 text-left text-gray-900 ring-1 ring-inset sm:text-sm sm:leading-6',
                            error
                                ? 'border-red-300 text-red-900 ring-red-300 focus:ring-red-500'
                                : 'ring-gray-300 focus:ring-indigo-600'
                        )}
                        onBlur={handleBlur}>
                        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2">
                            <CalendarDaysIcon className="h-5 w-5 text-gray-400" />
                        </span>
                        <span className="block truncate">{formattedDate}</span>
                        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon
                                className="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                            />
                        </span>
                    </PopoverButton>
                    <PopoverPanel
                        anchor={{
                            to: 'bottom start',
                            gap: '0.5rem',
                            padding: '0.5rem'
                        }}
                        className="absolute z-10 mt-1 bg-white rounded-md shadow-lg p-4">
                        <DayPicker
                            mode="single"
                            selected={selectedDate}
                            onSelect={handleDaySelect}
                            defaultMonth={selectedDate || new Date()}
                            classNames={{
                                caption_label: 'leading-none',
                                selected: 'bg-indigo-600 text-white rounded-md',
                                today: '!font-semibold'
                            }}
                        />
                    </PopoverPanel>
                </Popover>

                {/* Time Picker */}
                <div className="flex space-x-1 w-32">
                    {/* Hour Selector */}
                    <Listbox value={selectedHour} onChange={handleHourChange}>
                        <div className="relative mt-0 w-1/2">
                            <ListboxButton
                                className={classNames(
                                    'relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-8 text-left text-gray-900 ring-1 ring-inset sm:text-sm sm:leading-6',
                                    error
                                        ? 'border-red-300 text-red-900 ring-red-300 focus:ring-red-500'
                                        : 'ring-gray-300 focus:ring-indigo-600'
                                )}
                                onBlur={handleBlur}>
                                <span className="block truncate">{selectedHour}</span>
                                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <ChevronUpDownIcon
                                        className="h-5 w-5 text-gray-400"
                                        aria-hidden="true"
                                    />
                                </span>
                            </ListboxButton>
                            <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-y-auto overflow-x-hidden rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                {hourOptions.map((hour) => (
                                    <ListboxOption
                                        key={hour.value}
                                        value={hour.value}
                                        className={({ focus }) =>
                                            classNames(
                                                'relative cursor-default select-none py-2 pl-3 pr-9',
                                                focus
                                                    ? 'bg-indigo-600 text-white'
                                                    : 'text-gray-900'
                                            )
                                        }>
                                        {({ selected }) => (
                                            <>
                                                <span
                                                    className={classNames(
                                                        'block',
                                                        selected
                                                            ? 'font-semibold'
                                                            : 'font-normal'
                                                    )}>
                                                    {hour.label}
                                                </span>
                                            </>
                                        )}
                                    </ListboxOption>
                                ))}
                            </ListboxOptions>
                        </div>
                    </Listbox>

                    {/* Minute Selector */}
                    <Listbox value={selectedMinute} onChange={handleMinuteChange}>
                        <div className="relative mt-0 w-1/2">
                            <ListboxButton
                                className={classNames(
                                    'relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-8 text-left text-gray-900 ring-1 ring-inset sm:text-sm sm:leading-6',
                                    error
                                        ? 'border-red-300 text-red-900 ring-red-300 focus:ring-red-500'
                                        : 'ring-gray-300 focus:ring-indigo-600'
                                )}
                                onBlur={handleBlur}>
                                <span className="block truncate">{selectedMinute}</span>
                                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <ChevronUpDownIcon
                                        className="h-5 w-5 text-gray-400"
                                        aria-hidden="true"
                                    />
                                </span>
                            </ListboxButton>
                            <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-y-auto overflow-x-hidden rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                {minuteOptions.map((minute) => (
                                    <ListboxOption
                                        key={minute.value}
                                        value={minute.value}
                                        className={({ focus }) =>
                                            classNames(
                                                'relative cursor-default select-none py-2 pl-3 pr-9',
                                                focus
                                                    ? 'bg-indigo-600 text-white'
                                                    : 'text-gray-900'
                                            )
                                        }>
                                        {({ selected }) => (
                                            <span
                                                className={classNames(
                                                    'block',
                                                    selected
                                                        ? 'font-semibold'
                                                        : 'font-normal'
                                                )}>
                                                {minute.label}
                                            </span>
                                        )}
                                    </ListboxOption>
                                ))}
                            </ListboxOptions>
                        </div>
                    </Listbox>
                </div>
            </div>

            {/* Timezone Selector */}
            <div className="mt-2">
                <Listbox value={selectedTimezone} onChange={handleTimezoneChange}>
                    <div className="relative mt-0">
                        <ListboxButton
                            className={classNames(
                                'relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 ring-1 ring-inset sm:text-sm sm:leading-6',
                                error
                                    ? 'border-red-300 text-red-900 ring-red-300 focus:ring-red-500'
                                    : 'ring-gray-300 focus:ring-indigo-600'
                            )}
                            onBlur={handleBlur}>
                            <span className="block">{selectedTimezoneOption.label}</span>
                            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                <ChevronUpDownIcon
                                    className="h-5 w-5 text-gray-400"
                                    aria-hidden="true"
                                />
                            </span>
                        </ListboxButton>
                        <ListboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            {timezoneOptions.map((timezone) => (
                                <ListboxOption
                                    key={timezone.value}
                                    value={timezone.value}
                                    className={({ focus }) =>
                                        classNames(
                                            'relative cursor-default select-none py-2 pl-3 pr-9',
                                            focus
                                                ? 'bg-indigo-600 text-white'
                                                : 'text-gray-900'
                                        )
                                    }>
                                    {({ selected }) => (
                                        <>
                                            <span
                                                className={classNames(
                                                    'block',
                                                    selected
                                                        ? 'font-semibold'
                                                        : 'font-normal'
                                                )}>
                                                {timezone.label}
                                            </span>
                                        </>
                                    )}
                                </ListboxOption>
                            ))}
                        </ListboxOptions>
                    </div>
                </Listbox>
            </div>

            {/* Error message */}
            {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </div>
    );
}
