import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions
} from '@headlessui/react';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/react/24/outline';
import { SelectFieldProps } from '@quarterback/types';
import classNames from 'classnames';

export function SelectField({
    label,
    value,
    onChange,
    options,
    error,
    required = false
}: SelectFieldProps) {
    return (
        <Combobox
            as="div"
            value={value}
            onChange={(value) => {
                onChange(value ?? '');
            }}>
            <label className="block text-sm font-medium text-gray-700">
                {label}
                {required && <span className="text-red-500">*</span>}
            </label>
            <div className="relative mt-1">
                <ComboboxInput
                    className={classNames(
                        'w-full rounded-md py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset sm:text-sm sm:leading-6',
                        error
                            ? 'border-red-300 bg-red-50 text-red-900 ring-red-300 focus:ring-2 focus:ring-inset focus:ring-red-500'
                            : 'border-0 bg-white ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600'
                    )}
                    onChange={(event) => onChange(event.target.value)}
                    displayValue={(value: string) =>
                        options?.find((it) => it.value === value)?.label ?? ''
                    }
                />
                <ComboboxButton className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                    <ChevronUpDownIcon
                        className="h-5 w-5 text-gray-400"
                        aria-hidden="true"
                    />
                </ComboboxButton>

                {options.length > 0 && (
                    <ComboboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                        {options.map((option) => (
                            <ComboboxOption
                                key={option.value}
                                value={option.value}
                                className={({ focus }) =>
                                    classNames(
                                        'relative cursor-default select-none py-2 pl-3 pr-9',
                                        {
                                            'bg-indigo-600 text-white': focus,
                                            'text-gray-900': !focus
                                        }
                                    )
                                }>
                                {({ focus, selected }) => (
                                    <>
                                        <span
                                            className={classNames(
                                                'block truncate',
                                                selected && 'font-semibold'
                                            )}>
                                            {option.label}
                                        </span>

                                        {selected && (
                                            <span
                                                className={classNames(
                                                    'absolute inset-y-0 right-0 flex items-center pr-4',
                                                    focus
                                                        ? 'text-white'
                                                        : 'text-indigo-600'
                                                )}>
                                                <CheckIcon
                                                    className="h-5 w-5"
                                                    aria-hidden="true"
                                                />
                                            </span>
                                        )}
                                    </>
                                )}
                            </ComboboxOption>
                        ))}
                    </ComboboxOptions>
                )}
            </div>
            {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </Combobox>
    );
}
