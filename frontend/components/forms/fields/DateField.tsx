import { DateTimeFieldProps } from '@quarterback/types';
import classNames from 'classnames';
import { ChangeEvent } from 'react';

export function DateField({
    label,
    value,
    onChange,
    error,
    required = false
}: DateTimeFieldProps) {
    const dateString = value ? value.toISOString().slice(0, 16) : '';

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.value) {
            onChange(new Date(e.target.value));
        }
    };

    return (
        <div>
            <label className="block text-sm font-medium text-gray-700">
                {label}
                {required && <span className="text-red-500">*</span>}
            </label>
            <input
                type="datetime-local"
                className={classNames(
                    'block w-full rounded-md sm:text-sm',
                    error
                        ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
                )}
                value={dateString}
                onChange={handleChange}
                required={required}
            />
            {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
    );
}
