import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions
} from '@headlessui/react';
import {
    CheckIcon,
    ChevronUpDownIcon,
    MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { SelectFieldProps } from '@quarterback/types';
import classNames from 'classnames';
import { useCallback, useRef, useState } from 'react';

// Extend the SelectFieldProps to include options with key property
export interface SearchableSelectFieldProps extends Omit<SelectFieldProps, 'options'> {
    options: Array<{
        value: string;
        label: string;
        key?: string;
    }>;
    onSearch?: (searchTerm: string) => void;
    onLoadMore?: () => void;
    hasMore?: boolean;
    isLoading?: boolean;
    searchPlaceholder?: string;
    actionButton?: {
        label: string;
        onClick: () => void;
    };
}

export function SearchableSelectField({
    label,
    value,
    onChange,
    options,
    error,
    required = false,
    onSearch,
    onLoadMore,
    hasMore = false,
    isLoading = false,
    searchPlaceholder = 'Search...',
    actionButton
}: SearchableSelectFieldProps) {
    // We'll use Headless UI's built-in open state management
    const [searchTerm, setSearchTerm] = useState('');
    const optionsRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const searchDebounceRef = useRef<NodeJS.Timeout | null>(null);

    const handleSearchChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const term = e.target.value;
            setSearchTerm(term);

            // Debounce search to avoid too many requests
            if (searchDebounceRef.current) {
                clearTimeout(searchDebounceRef.current);
            }

            searchDebounceRef.current = setTimeout(() => {
                onSearch?.(term);
            }, 300);
        },
        [onSearch]
    );

    // We'll handle the scroll event directly on the ComboboxOptions element

    // Focus the search input when dropdown opens
    const focusSearchInput = useCallback(() => {
        if (searchInputRef.current && onSearch) {
            // Small delay to ensure the dropdown is rendered
            setTimeout(() => {
                searchInputRef.current?.focus();
            }, 50);
        }
    }, [onSearch]);

    // Handle option selection
    const handleOptionSelect = useCallback(
        (selectedValue: string) => {
            onChange(selectedValue ?? '');
            // Reset search term when an option is selected
            setSearchTerm('');
        },
        [onChange]
    );

    return (
        <div>
            <div className="flex justify-between items-center">
                <label className="block text-sm font-medium text-gray-700">
                    {label}
                    {required && <span className="text-red-500">*</span>}
                </label>
                {actionButton && (
                    <button
                        type="button"
                        onClick={actionButton.onClick}
                        className="text-sm text-indigo-600 hover:text-indigo-500">
                        {actionButton.label}
                    </button>
                )}
            </div>
            <Combobox value={value} onChange={handleOptionSelect}>
                {({ open }) => (
                    <div className="relative mt-1">
                        <ComboboxInput
                            className={classNames(
                                'w-full rounded-md py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset sm:text-sm sm:leading-6',
                                error
                                    ? 'border-red-300 bg-red-50 text-red-900 ring-red-300 focus:ring-2 focus:ring-inset focus:ring-red-500'
                                    : 'border-0 bg-white ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600'
                            )}
                            onChange={(event) => onChange(event.target.value)}
                            displayValue={(value: string) =>
                                options?.find((it) => it.value === value)?.label ?? ''
                            }
                        />
                        <ComboboxButton className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                            <ChevronUpDownIcon
                                className="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                            />
                        </ComboboxButton>

                        {open && (
                            <div
                                className="absolute z-10 mt-1 w-full rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                                onMouseEnter={() => {
                                    // Focus the search input when the dropdown is opened
                                    if (onSearch) {
                                        focusSearchInput();
                                    }
                                }}>
                                {onSearch && (
                                    <div className="relative p-2 border-b">
                                        <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                            <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
                                        </div>
                                        <input
                                            ref={searchInputRef}
                                            type="text"
                                            className="w-full pl-10 pr-3 py-1.5 text-sm border-0 rounded-md bg-gray-50 focus:ring-1 focus:ring-indigo-500 focus:outline-none"
                                            placeholder={searchPlaceholder}
                                            value={searchTerm}
                                            onChange={handleSearchChange}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                e.preventDefault();
                                            }}
                                            onKeyDown={(e) => {
                                                // Prevent Escape key from closing the dropdown
                                                if (e.key === 'Escape') {
                                                    e.stopPropagation();
                                                }
                                            }}
                                            autoComplete="off"
                                        />
                                    </div>
                                )}

                                <ComboboxOptions
                                    className="max-h-60 overflow-auto py-1 text-base sm:text-sm"
                                    ref={optionsRef}
                                    onScroll={(e) => {
                                        const target = e.currentTarget;
                                        if (!target || !hasMore || isLoading) return;

                                        // Check if we've scrolled to the bottom (with a small buffer)
                                        if (
                                            target.scrollHeight - target.scrollTop <=
                                            target.clientHeight + 50
                                        ) {
                                            onLoadMore?.();
                                        }
                                    }}>
                                    {options.length === 0 && !isLoading ? (
                                        <div className="py-2 px-3 text-sm text-gray-500">
                                            No results found
                                        </div>
                                    ) : (
                                        options.map((option) => (
                                            <ComboboxOption
                                                key={option.key || option.value}
                                                value={option.value}
                                                className={({ focus }) =>
                                                    classNames(
                                                        'relative cursor-default select-none py-2 pl-3 pr-9',
                                                        {
                                                            'bg-indigo-600 text-white':
                                                                focus,
                                                            'text-gray-900': !focus
                                                        }
                                                    )
                                                }>
                                                {({ focus, selected }) => (
                                                    <>
                                                        <span
                                                            className={classNames(
                                                                'block truncate',
                                                                selected &&
                                                                    'font-semibold'
                                                            )}>
                                                            {option.label}
                                                        </span>

                                                        {selected && (
                                                            <span
                                                                className={classNames(
                                                                    'absolute inset-y-0 right-0 flex items-center pr-4',
                                                                    focus
                                                                        ? 'text-white'
                                                                        : 'text-indigo-600'
                                                                )}>
                                                                <CheckIcon
                                                                    className="h-5 w-5"
                                                                    aria-hidden="true"
                                                                />
                                                            </span>
                                                        )}
                                                    </>
                                                )}
                                            </ComboboxOption>
                                        ))
                                    )}

                                    {isLoading && (
                                        <div className="py-2 px-3 text-center text-sm text-gray-500 flex items-center justify-center">
                                            <svg
                                                className="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-500"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24">
                                                <circle
                                                    className="opacity-25"
                                                    cx="12"
                                                    cy="12"
                                                    r="10"
                                                    stroke="currentColor"
                                                    strokeWidth="4"></circle>
                                                <path
                                                    className="opacity-75"
                                                    fill="currentColor"
                                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Loading...
                                        </div>
                                    )}

                                    {!isLoading && hasMore && (
                                        <div className="py-2 px-3 text-center text-sm text-gray-500">
                                            Scroll for more...
                                        </div>
                                    )}
                                </ComboboxOptions>
                            </div>
                        )}
                    </div>
                )}
            </Combobox>
            {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </div>
    );
}
