import React, { useCallback, useState } from 'react';
import { DocumentTextIcon, XMarkIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { ActivityFile } from '@quarterback/types';

export interface FileUploadProps {
    files: File[];
    setFiles: React.Dispatch<React.SetStateAction<File[]>>;
    uploadedFiles?: ActivityFile[];
    onRemoveUploadedFile?: (fileId: string) => void;
    maxSize?: number;
    accept?: string;
    multiple?: boolean;
}

export function FileUpload({
    files,
    setFiles,
    uploadedFiles = [],
    onRemoveUploadedFile,
    maxSize = 10,
    accept = 'image/*,text/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation,application/vnd.ms-access,application/vnd.openxmlformats-officedocument.presentationml.slideshow,application/vnd.openxmlformats-officedocument.presentationml.template,application/vnd.openxmlformats-officedocument.wordprocessingml.template,application/vnd.openxmlformats-officedocument.spreadsheetml.template,application/vnd.oasis.opendocument.text,application/vnd.oasis.opendocument.spreadsheet,application/vnd.oasis.opendocument.presentation,application/rtf,text/csv,application/csv,application/x-csv,application/vnd.canva.presentation,application/epub+zip,application/x-iwork-keynote-sffkey,application/x-iwork-pages-sffpages,application/x-iwork-numbers-sffnumbers,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.accdb,.txt,.csv,.rtf,.odt,.ods,.odp,.pdf,.epub,.key,.pages,.numbers',
    multiple = true
}: FileUploadProps) {
    const [over, setOver] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const validateFile = useCallback(
        (file: File): boolean => {
            if (file.size > maxSize * 1024 * 1024) {
                setError(`File size exceeds ${maxSize}MB limit`);
                return false;
            }

            // Check file type if accept is specified
            if (accept && accept !== '*') {
                const acceptTypes = accept.split(',');
                const fileType = file.type;

                // Check if the file type matches any of the accepted types
                const isAccepted = acceptTypes.some((type) => {
                    if (type.includes('*')) {
                        const typePrefix = type.split('/')[0];
                        return fileType.startsWith(`${typePrefix}/`);
                    }
                    return type === fileType;
                });

                if (!isAccepted) {
                    setError('File type not accepted');
                    return false;
                }
            }

            setError(null);
            return true;
        },
        [maxSize, accept]
    );

    const handleFileInputChange = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>) => {
            const selectedFiles = Array.from(event.target.files || []);
            // Filter out files that are already in the files state (by name and size)
            const newFiles = selectedFiles.filter(
                (file) => !files.some((f) => f.name === file.name && f.size === file.size)
            );
            const validFiles = newFiles.filter(validateFile);
            if (multiple) {
                setFiles((prevFiles) => [...prevFiles, ...validFiles]);
            } else if (validFiles.length > 0) {
                setFiles([validFiles[0]]);
            }
        },
        [setFiles, validateFile, multiple, files]
    );

    const handleDrop = useCallback(
        (event: React.DragEvent) => {
            event.preventDefault();
            setOver(false);
            const droppedFiles = Array.from(event.dataTransfer.files || []);
            // Filter out files that are already in the files state (by name and size)
            const newFiles = droppedFiles.filter(
                (file) => !files.some((f) => f.name === file.name && f.size === file.size)
            );
            const validFiles = newFiles.filter(validateFile);
            if (multiple) {
                setFiles((prevFiles) => [...prevFiles, ...validFiles]);
            } else if (validFiles.length > 0) {
                setFiles([validFiles[0]]);
            }
        },
        [setFiles, validateFile, multiple, files]
    );

    const handleDragOver = useCallback((event: React.DragEvent) => {
        event.preventDefault();
        setOver(true);
    }, []);

    const handleDragLeave = useCallback(() => {
        setOver(false);
    }, []);

    const handleRemoveFile = useCallback(
        (index: number) => {
            setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
        },
        [setFiles]
    );

    return (
        <div className="space-y-4">
            <div
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                className={classNames(
                    'flex justify-center rounded-lg border border-dashed px-6 py-8',
                    {
                        'border-gray-900/25': !over,
                        'border-gray-900/50': over,
                        'border-red-500': error
                    }
                )}>
                <div className="text-center">
                    <DocumentTextIcon
                        aria-hidden="true"
                        className="mx-auto h-12 w-12 text-gray-300"
                    />
                    <div className="mt-4 flex text-sm/6 text-gray-600">
                        <label
                            htmlFor="file-upload"
                            className="relative cursor-pointer rounded-md bg-white font-semibold text-indigo-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-600 focus-within:ring-offset-2 hover:text-indigo-500">
                            <span>Upload an image or file</span>
                            <input
                                id="file-upload"
                                name="file-upload"
                                type="file"
                                className="sr-only"
                                accept={accept}
                                multiple={multiple}
                                onChange={handleFileInputChange}
                            />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs/5 text-gray-600">
                        PNG, JPG, GIF up to {maxSize}MB
                    </p>
                    {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
                </div>
            </div>

            {/* Display selected files */}
            {files.length > 0 && (
                <div>
                    <h4 className="text-sm font-medium text-gray-700">Selected Files</h4>
                    <ul className="mt-1 space-y-1">
                        {files.map((file, index) => (
                            <li
                                key={`${file.name}-${index}`}
                                className="flex items-center justify-between rounded-md bg-gray-50 px-3 py-2 text-sm">
                                <span className="truncate">{file.name}</span>
                                <button
                                    type="button"
                                    onClick={() => handleRemoveFile(index)}
                                    className="ml-2 text-gray-500 hover:text-gray-700">
                                    <XMarkIcon className="h-4 w-4" />
                                </button>
                            </li>
                        ))}
                    </ul>
                </div>
            )}

            {uploadedFiles.length > 0 && (
                <div>
                    <h4 className="text-sm font-medium text-gray-700">Uploaded Files</h4>
                    <ul className="mt-1 space-y-1">
                        {uploadedFiles.map((file) => (
                            <li
                                key={file.storagePath}
                                className="flex items-center justify-between rounded-md bg-gray-50 px-3 py-2 text-sm">
                                <span className="truncate">{file.fileName}</span>
                                {onRemoveUploadedFile && (
                                    <button
                                        type="button"
                                        onClick={() =>
                                            onRemoveUploadedFile(file.storagePath!)
                                        }
                                        className="ml-2 text-gray-500 hover:text-gray-700">
                                        <XMarkIcon className="h-4 w-4" />
                                    </button>
                                )}
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
}
