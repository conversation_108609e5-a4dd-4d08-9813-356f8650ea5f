import { CheckboxFieldProps } from '@quarterback/types';
import classNames from 'classnames';

export function CheckboxField({
    label,
    value,
    onChange,
    error,
    required = false
}: CheckboxFieldProps) {
    return (
        <div className="relative flex items-start">
            <div className="flex h-6 items-center">
                <input
                    type="checkbox"
                    className={classNames(
                        'h-4 w-4 rounded',
                        error
                            ? 'border-red-300 text-red-600 focus:ring-red-500'
                            : 'border-gray-300 text-indigo-600 focus:ring-indigo-500'
                    )}
                    checked={value}
                    onChange={(e) => onChange(e.target.checked)}
                    required={required}
                />
            </div>
            <div className="ml-3 text-sm leading-6">
                <label className="font-medium text-gray-700">
                    {label}
                    {required && <span className="text-indigo-500">*</span>}
                </label>
                {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
            </div>
        </div>
    );
}
