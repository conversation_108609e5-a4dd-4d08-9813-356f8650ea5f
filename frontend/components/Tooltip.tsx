import React, { JSX, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { Transition } from '@headlessui/react';

interface Props {
    text: string | JSX.Element;
    children: JSX.Element;
    offset?: number;
    leftOffset?: number;
}

export default function Tooltip({ children, text, offset = 26, leftOffset = 0 }: Props) {
    const ref = useRef<HTMLElement | null>(null);

    const [isOpen, setIsOpen] = useState(false);
    const [coords, setCoords] = useState({ top: 0, left: 0 });

    useEffect(() => {
        function handleScroll() {
            setIsOpen(false);
        }

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [setIsOpen]);

    const handleMouseEnter = () => {
        if (ref.current) {
            const rect = ref.current.getBoundingClientRect();

            setCoords({
                top: rect.top + window.scrollY,
                left: rect.left + rect.width / 2
            });

            setIsOpen(true);
        }
    };

    const handleMouseLeave = () => {
        setIsOpen(false);
    };

    return (
        <>
            {React.cloneElement(children as React.ReactElement, {
                ref,
                onMouseEnter: handleMouseEnter,
                onMouseLeave: handleMouseLeave,
                onMouseMove: handleMouseEnter
            })}
            {isOpen &&
                createPortal(
                    <Transition
                        show={isOpen}
                        enter="transition-opacity duration-200"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="transition-opacity duration-150"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0">
                        <div
                            className="absolute z-10 px-2 py-1 text-sm text-white bg-gray-900/85 rounded-md transform -translate-x-1/2 text-center max-w-72"
                            style={{
                                top: coords.top + offset,
                                left: coords.left + leftOffset
                            }}>
                            {text}
                        </div>
                    </Transition>,
                    document.body
                )}
        </>
    );
}
