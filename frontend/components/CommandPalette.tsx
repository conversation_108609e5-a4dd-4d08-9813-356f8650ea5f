'use client';

import React, { createContext, useContext, useMemo, useRef } from 'react';
import {
    Combobox,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions,
    Dialog,
    DialogPanel,
    DialogBackdrop
} from '@headlessui/react';
import { MagnifyingGlassIcon } from '@heroicons/react/20/solid';
import {
    BoltIcon,
    ClockIcon,
    LightBulbIcon,
    SparklesIcon
} from '@heroicons/react/24/outline';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import useActivities from '@/api/hooks/useActivities';
import { useOrganisation } from '@/components/OrganisationProvider';
import { sub } from 'date-fns';
import { isDefined } from '@quarterback/util';

const CommandPaletteContext = createContext({
    open: () => {
        console.log('Original click?');
    }
});

export function useCommandPalette() {
    return useContext(CommandPaletteContext);
}

interface CommandPaletteOption {
    name: string;
    type?: 'query' | 'suggestion' | 'announcement';
}

export default function CommandPalette({ children }: React.PropsWithChildren) {
    const router = useRouter();
    const organisation = useOrganisation();

    const [query, setQuery] = useState('');
    const [open, setOpen] = useState(false);

    const announcementRange = useRef({
        from: sub(new Date(), { months: 6 }),
        to: new Date()
    });

    const { data: announcementActivities } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        announcementRange.current.from,
        announcementRange.current.to,
        { source: 'asx', limit: 5 }
    );

    const [recent, setRecent] = useState<Array<CommandPaletteOption>>([]);
    const suggestions: Array<CommandPaletteOption> = [
        { name: 'Rumors' },
        { name: 'Overvalued' },
        { name: 'Negative press' }
    ];

    const announcements: Array<CommandPaletteOption> = useMemo(() => {
        return (announcementActivities ?? [])
            .map((it) => {
                if (it.type === 'asx-announcement') {
                    return {
                        name: it.title
                    };
                } else {
                    return undefined;
                }
            })
            .filter(isDefined);
    }, [announcementActivities]);

    const filteredRecent =
        query === ''
            ? recent
            : recent.filter((it) => {
                  return it.name.toLowerCase().includes(query.toLowerCase());
              });
    const filteredSuggestions =
        query === ''
            ? suggestions
            : suggestions.filter((it) => {
                  return it.name.toLowerCase().includes(query.toLowerCase());
              });
    const filteredAnnouncements =
        query === ''
            ? announcements
            : announcements.filter((it) => {
                  return it.name.toLowerCase().includes(query.toLowerCase());
              });

    useEffect(() => {
        if (open) {
            setRecent(
                (
                    JSON.parse(
                        localStorage.getItem('recent-searches') ?? '[]'
                    ) as Array<string>
                ).map((it) => ({ name: it }))
            );
        }
    }, [open]);

    useEffect(() => {
        function handleKeydown(event: KeyboardEvent) {
            if (event.key === 'k' && event.metaKey) {
                setOpen(true);
            }
        }

        window.addEventListener('keydown', handleKeydown);

        return () => {
            window.removeEventListener('keydown', handleKeydown);
        };
    }, []);

    const context = useMemo(() => {
        return {
            open: () => {
                setOpen(true);
            }
        };
    }, [setOpen]);

    return (
        <CommandPaletteContext.Provider value={context}>
            {children}
            <Dialog
                className="relative z-20"
                open={open}
                onClose={() => {
                    setOpen(false);
                    setQuery('');
                }}>
                <DialogBackdrop
                    transition
                    className="fixed inset-0 bg-gray-500 bg-opacity-25 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
                />

                <div className="fixed inset-0 z-30 w-screen overflow-y-auto p-4 sm:p-6 md:p-20">
                    <DialogPanel
                        transition
                        className="mx-auto max-w-2xl transform divide-y divide-gray-500 divide-opacity-10 overflow-hidden rounded-xl bg-white bg-opacity-80 shadow-2xl ring-1 ring-black ring-opacity-5 backdrop-blur backdrop-filter transition-all data-[closed]:scale-95 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in">
                        <Combobox
                            onChange={(item: CommandPaletteOption) => {
                                if (item) {
                                    if (item.type === 'query') {
                                        localStorage.setItem(
                                            'recent-searches',
                                            JSON.stringify(
                                                [
                                                    item.name,
                                                    ...recent.map((it) => it.name)
                                                ].slice(0, 3)
                                            )
                                        );
                                    }

                                    router.push(
                                        `/investors/search?query=${encodeURIComponent(item.name)}`
                                    );

                                    setOpen(false);
                                    setQuery('');
                                }
                            }}>
                            <div className="relative">
                                <MagnifyingGlassIcon
                                    className="pointer-events-none absolute left-4 top-3.5 h-5 w-5 text-gray-900 text-opacity-40"
                                    aria-hidden="true"
                                />
                                <ComboboxInput
                                    autoFocus
                                    className="h-12 w-full border-0 bg-transparent pl-11 pr-4 text-gray-900 focus:ring-0 sm:text-sm"
                                    placeholder="Search..."
                                    onChange={(event) => {
                                        setQuery(event.target.value);
                                    }}
                                    onBlur={() => setQuery('')}
                                />
                            </div>

                            <ComboboxOptions
                                static
                                as="ul"
                                className="max-h-[32rem] scroll-py-2 divide-y divide-gray-500 divide-opacity-10 overflow-y-auto">
                                {query !== '' ? (
                                    <li className="p-2">
                                        <ul className="text-sm text-gray-700">
                                            <ComboboxOption
                                                as="li"
                                                value={{ name: query, type: 'query' }}
                                                className="group flex cursor-default select-none items-center rounded-md px-3 py-2 data-[focus]:bg-gray-900 data-[focus]:bg-opacity-5 data-[focus]:text-gray-900">
                                                <MagnifyingGlassIcon
                                                    className="h-6 w-6 flex-none text-gray-900 text-opacity-40 group-data-[focus]:text-opacity-100"
                                                    aria-hidden="true"
                                                />
                                                <span className="ml-3 flex-auto truncate">
                                                    &ldquo;{query}&rdquo;
                                                </span>
                                                <span className="ml-3 hidden flex-none text-gray-500 group-data-[focus]:inline">
                                                    Search...
                                                </span>
                                            </ComboboxOption>
                                        </ul>
                                    </li>
                                ) : undefined}
                                {filteredRecent.length > 0 && (
                                    <li className="p-2">
                                        {query === '' && (
                                            <h2 className="mb-2 mt-4 px-3 text-xs font-semibold text-gray-900">
                                                Recent searches
                                            </h2>
                                        )}
                                        <ul className="text-sm text-gray-700">
                                            {filteredRecent.map((it) => (
                                                <ComboboxOption
                                                    key={it.name}
                                                    as="li"
                                                    value={it}
                                                    className="group flex cursor-default select-none items-center rounded-md px-3 py-2 data-[focus]:bg-gray-900 data-[focus]:bg-opacity-5 data-[focus]:text-gray-900">
                                                    <ClockIcon
                                                        className="h-6 w-6 flex-none text-gray-900 text-opacity-40 group-data-[focus]:text-opacity-100"
                                                        aria-hidden="true"
                                                    />
                                                    <span className="ml-3 flex-auto truncate">
                                                        {it.name}
                                                    </span>
                                                    <span className="ml-3 hidden flex-none text-gray-500 group-data-[focus]:inline">
                                                        Search...
                                                    </span>
                                                </ComboboxOption>
                                            ))}
                                        </ul>
                                    </li>
                                )}
                                {filteredSuggestions.length > 0 && (
                                    <li className="p-2">
                                        {query === '' && (
                                            <h2 className="mb-2 mt-4 px-3 text-xs font-semibold text-gray-900">
                                                Suggestions
                                            </h2>
                                        )}
                                        <ul className="text-sm text-gray-700">
                                            {filteredSuggestions.map((it) => (
                                                <ComboboxOption
                                                    key={it.name}
                                                    as="li"
                                                    value={it}
                                                    className="group flex cursor-default select-none items-center rounded-md px-3 py-2 data-[focus]:bg-gray-900 data-[focus]:bg-opacity-5 data-[focus]:text-gray-900">
                                                    <BoltIcon className="h-6 w-6 flex-none text-gray-900 text-opacity-40 group-data-[focus]:text-opacity-100 rounded-full" />
                                                    <span className="ml-3 flex-auto truncate">
                                                        {it.name}
                                                    </span>
                                                    <span className="ml-3 hidden flex-none text-gray-500 group-data-[focus]:inline">
                                                        Search...
                                                    </span>
                                                </ComboboxOption>
                                            ))}
                                        </ul>
                                    </li>
                                )}

                                {filteredAnnouncements.length > 0 && (
                                    <li className="p-2">
                                        {query === '' && (
                                            <h2 className="mb-2 mt-4 px-3 text-xs font-semibold text-gray-900">
                                                Recent announcements
                                            </h2>
                                        )}
                                        <ul className="text-sm text-gray-700">
                                            {filteredAnnouncements.map((announcement) => (
                                                <ComboboxOption
                                                    as="li"
                                                    key={announcement.name}
                                                    value={announcement}
                                                    className="group flex cursor-default select-none items-center rounded-md px-3 py-2 data-[focus]:bg-gray-900 data-[focus]:bg-opacity-5 data-[focus]:text-gray-900">
                                                    <img
                                                        src="/sources/asx.svg"
                                                        className="h-6 w-6 flex-none text-gray-900 text-opacity-40 group-data-[focus]:text-opacity-100 rounded-full"
                                                    />
                                                    <span className="ml-3 flex-auto truncate">
                                                        {announcement.name}
                                                    </span>
                                                    <span className="ml-3 hidden flex-none text-gray-500 group-data-[focus]:inline">
                                                        Search...
                                                    </span>
                                                </ComboboxOption>
                                            ))}
                                        </ul>
                                    </li>
                                )}

                                {/*{query === '' && (*/}
                                {/*    <li className="p-2">*/}
                                {/*        <h2 className="sr-only">Quick actions</h2>*/}
                                {/*        <ul className="text-sm text-gray-700">*/}
                                {/*            {quickActions.map((action) => (*/}
                                {/*                <ComboboxOption*/}
                                {/*                    as="li"*/}
                                {/*                    key={action.shortcut}*/}
                                {/*                    value={action}*/}
                                {/*                    className="group flex cursor-default select-none items-center rounded-md px-3 py-2 data-[focus]:bg-gray-900 data-[focus]:bg-opacity-5 data-[focus]:text-gray-900">*/}
                                {/*                    <action.icon*/}
                                {/*                        className="h-6 w-6 flex-none text-gray-900 text-opacity-40 group-data-[focus]:text-opacity-100"*/}
                                {/*                        aria-hidden="true"*/}
                                {/*                    />*/}
                                {/*                    <span className="ml-3 flex-auto truncate">*/}
                                {/*                        {action.name}*/}
                                {/*                    </span>*/}
                                {/*                    <span className="ml-3 flex-none text-xs font-semibold text-gray-500">*/}
                                {/*                        <kbd className="font-sans">⌘</kbd>*/}
                                {/*                        <kbd className="font-sans">*/}
                                {/*                            {action.shortcut}*/}
                                {/*                        </kbd>*/}
                                {/*                    </span>*/}
                                {/*                </ComboboxOption>*/}
                                {/*            ))}*/}
                                {/*        </ul>*/}
                                {/*    </li>*/}
                                {/*)}*/}
                            </ComboboxOptions>

                            {/*{query !== '' && filteredProjects.length === 0 && (*/}
                            {/*    <div className="px-6 py-14 text-center sm:px-14">*/}
                            {/*        <FolderIcon*/}
                            {/*            className="mx-auto h-6 w-6 text-gray-900 text-opacity-40"*/}
                            {/*            aria-hidden="true"*/}
                            {/*        />*/}
                            {/*        <p className="mt-4 text-sm text-gray-900">*/}
                            {/*            We couldn&apos;t find any projects with that term.*/}
                            {/*            Please try again.*/}
                            {/*        </p>*/}
                            {/*    </div>*/}
                            {/*)}*/}
                        </Combobox>
                    </DialogPanel>
                </div>
            </Dialog>
        </CommandPaletteContext.Provider>
    );
}
