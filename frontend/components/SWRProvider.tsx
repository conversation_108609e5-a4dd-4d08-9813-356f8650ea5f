'use client';

import React from 'react';
import { SWRConfig, Cache, State } from 'swr';

function localStorageProvider(): Cache {
    const LEGACY_KEYS = ['swr-cache', 'swr-cache-2', 'swr-timestamps-v3', 'swr-cache-v3'];

    const CACHE_KEY = 'swr-cache-v4';
    const TIMESTAMPS_KEY = 'swr-timestamps-v4';

    if (typeof window !== 'undefined') {
        LEGACY_KEYS.forEach((key) => localStorage.removeItem(key));

        const cache = new Map<string, State<any> | undefined>(
            JSON.parse(localStorage.getItem(CACHE_KEY) || '[]')
        );

        const timestamps = new Map<string, number>(
            JSON.parse(localStorage.getItem(TIMESTAMPS_KEY) || '[]')
        );

        window.addEventListener('beforeunload', () => {
            const recent = new Map(
                Array.from(timestamps.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 30)
            );

            localStorage.removeItem(CACHE_KEY);
            localStorage.removeItem(TIMESTAMPS_KEY);

            localStorage.setItem(
                CACHE_KEY,
                JSON.stringify(
                    Array.from(cache.entries()).filter(([key, _]) => recent.has(key))
                )
            );
            localStorage.setItem(
                TIMESTAMPS_KEY,
                JSON.stringify(Array.from(recent.entries()))
            );
        });

        return {
            keys(): IterableIterator<string> {
                return cache.keys();
            },
            get(key: string): State<any> | undefined {
                return cache.get(key);
            },
            set(key: string, value: State<any> | undefined): void {
                cache.set(key, value);
                timestamps.set(key, new Date().valueOf());
            },
            delete(key: string): void {
                cache.delete(key);
                timestamps.delete(key);
            }
        };
    } else {
        return new Map();
    }
}

export const SWRProvider = ({ children }: {} & React.PropsWithChildren) => {
    return <SWRConfig value={{ provider: localStorageProvider }}>{children}</SWRConfig>;
};
