import { formatInTimeZone, FormatOptionsWithTZ, fromZonedTime } from 'date-fns-tz';

export function formatWithTimeZone(
    date: Date | string | number,
    format: string = 'd MMMM, yyyy HH:mm zzz',
    timezone: string = 'Australia/Sydney',
    options?: FormatOptionsWithTZ
): string {
    if (!date) return '';

    return formatInTimeZone(date, timezone, format, options);
}

export function createDate({
    date = new Date(),
    hour,
    minute,
    second,
    timezone = 'Australia/Sydney'
}: {
    date?: Date;
    hour?: number | string;
    minute?: number | string;
    second?: number | string;
    timezone?: string;
} = {}): Date {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    // Use provided values or extract from the date
    const hours =
        hour !== undefined
            ? typeof hour === 'string'
                ? parseInt(hour, 10)
                : hour
            : date.getHours();

    const minutes =
        minute !== undefined
            ? typeof minute === 'string'
                ? parseInt(minute, 10)
                : minute
            : date.getMinutes();

    const seconds =
        second !== undefined
            ? typeof second === 'string'
                ? parseInt(second, 10)
                : second
            : date.getSeconds();

    // Format as ISO string for the local date
    const localDateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

    // Convert to a date considering the specified timezone
    return fromZonedTime(localDateString, timezone);
}
