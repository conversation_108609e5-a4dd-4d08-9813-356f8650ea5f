import {
    ArrowTrendingUpIcon,
    BellIcon,
    ChatBubbleBottomCenterIcon,
    DocumentTextIcon,
    MegaphoneIcon,
    NewspaperIcon,
    Squares2X2Icon
} from '@heroicons/react/24/outline';
import InitialColors from './InitialColors';

export type SidebarSections = Array<{
    name: string;
    icon?: React.ComponentType<{ className: string | undefined }> | null;
    href?: string;
    sidebar?: boolean;
    items: Array<SectionItem>;
}>;

export type SectionItem = {
    name: string;
    href?: string;
    icon?: React.ComponentType<{ className: string | undefined }> | null;
    badge?: string;
    color?: string;
};

export const pages: SidebarSections = [
    {
        name: 'Dashboard',
        href: '/investors/dashboard',
        icon: Squares2X2Icon,
        sidebar: true,
        items: []
    },
    {
        name: 'Notifications',
        href: '/alerts/rules',
        icon: BellIcon,
        sidebar: true,
        items: []
    },
    {
        name: 'Insights',
        icon: ArrowTrendingUpIcon,
        sidebar: true,
        items: [
            {
                name: 'Chatter',
                href: '#',
                icon: ChatBubbleBottomCenterIcon
            },
            {
                name: 'Broadcasts',
                href: '#',
                icon: MegaphoneIcon
            },
            {
                name: 'Media',
                href: '#',
                icon: NewspaperIcon
            }
        ]
    },
    {
        name: 'Reports',
        href: '/investors/reports',
        icon: DocumentTextIcon,
        sidebar: true,
        items: []
    },
    {
        name: 'Data',
        sidebar: true,
        href: '#',
        items: [
            {
                name: 'Activities',
                href: '/investors/activities',
                color: InitialColors.ACTIVITY
            },
            {
                name: 'People',
                href: '#',
                color: InitialColors.PEOPLE
            },
            {
                name: 'Media',
                href: '#',
                color: InitialColors.MEDIA
            },
            {
                name: 'Announcements',
                href: '#',
                color: InitialColors.ANNOUNCEMENT
            }
        ]
    },
    {
        name: 'account',
        href: '/account',
        icon: null,
        items: []
    }
];

export const flatPages = pages.reduce((acc, it) => {
    return acc.concat(it as SectionItem, [...it.items]);
}, [] as Array<SectionItem>);
