import { Activity } from '@quarterback/types';

export default function activityFormat(activity: Activity): string {
    switch (activity.type) {
        case 'news':
            return 'Publication';
        case 'asx-announcement':
            return 'Announcement';
        default:
            if ('isBroadcast' in activity) {
                return activity.isBroadcast ? 'Broadcast' : 'Chatter';
            }

            return 'Chatter';
    }
}
