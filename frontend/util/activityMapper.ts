import { Activity2 } from '@quarterback/types';
import { ActivityFormData } from '@quarterback/types';
import { parseISO } from 'date-fns';

/**
 * Converts form data to Activity2 format for API submission
 */
export function mapFormDataToActivity2(
    formData: ActivityFormData,
    { symbol, exchange }: { symbol?: string; exchange?: string }
): Activity2 {
    let postedDate: Date | undefined;
    if (formData.posted) {
        postedDate = parseISO(formData.posted);
    }

    const activity: Partial<Activity2> = {
        title: formData.title,
        body: formData.body,
        url: formData.url,
        image: formData.image?.length ? formData.image : undefined,
        posted: postedDate,
        likes: formData?.likes ? parseInt(formData.likes, 10) : undefined,
        symbol,
        exchange
    };

    if (formData.files) {
        activity.files = formData.files;
    }

    // Add author if provided
    if (formData.author) {
        activity.author = formData.author;
    }

    // Set isBroadcast based on format
    activity.isBroadcast = formData.format === 'broadcast';

    // Add source-specific fields
    switch (formData.source) {
        case 'https://x.com':
            activity.tweet = {};

            if ((formData as any).views) {
                activity.views = parseInt((formData as any).views, 10) || 0;
            }
            break;

        case 'https://hotcopper.com.au':
            const hcData = formData as any;
            activity.hotcopper = {
                sentiment: hcData.sentiment,
                disclosure: hcData.disclosure
            };
            if (hcData.thread) {
                activity.hotcopper.thread = {
                    id: hcData.thread,
                    views: hcData?.threadViews ?? 0
                };
            }
            break;

        case 'https://linkedin.com':
            const liData = formData as any;
            activity.linkedIn = {};
            if (liData.comments) activity.comments = parseInt(liData.comments, 10) || 0;
            break;

        case 'https://reddit.com':
            const redditData = formData as any;
            activity.redditPost = {
                subreddit: redditData.subreddit
            };
            if (redditData.score)
                activity.redditPost.score = parseInt(redditData.score, 10) || 0;
            if (redditData.ratio)
                activity.redditPost.ratio = parseFloat(redditData.ratio) || 0;
            break;

        case 'news':
            activity.news = {
                image: formData.image,
                source: {
                    name: (formData as any).newsSource.name,
                    domain: (formData as any).newsSource.url
                }
            };
            break;

        case 'https://asx.com.au':
            const asxData = formData as any;
            activity.asx = {
                sensitive: asxData.sensitive || false
            };
            break;
        case 'https://advfn.com':
            activity.advfn = {};
            break;
        case 'https://podcasts.apple.com':
            activity.applePodcast = {};
            break;
        case 'https://audible.com':
            activity.audible = {};
            break;
        case 'https://aussiestockforums.com':
            activity.aussiestockforums = {};
            break;
        case 'https://castbox.fm':
            activity.castbox = {};
            break;
        case 'https://clubhouse.com':
            activity.clubhouse = {};
            break;
        case 'https://iheart.com':
            activity.iheartradio = {};
            break;
        case 'https://investorhub.com':
            activity.investorhub = {};
            break;
        case 'https://medium.com':
            activity.medium = {};
            break;
        case 'https://pinterest.com':
            activity.pinterest = {};
            break;
        case 'https://quora.com':
            activity.quora = {};
            break;
        case 'https://slack.com':
            activity.slack = {};
            break;
        case 'https://snapchat.com':
            activity.snapchat = {};
            break;
        case 'https://spotify.com':
            activity.spotify = {};
            break;
        case 'https://stocktwits.com':
            activity.stocktwits = {};
            break;
        case 'https://strawman.com':
            activity.strawman = {};
            break;
        case 'https://tradingqna.com':
            activity.tradingqna = {};
            break;
        case 'https://tumblr.com':
            activity.tumblr = {};
            break;
        case 'https://vimeo.com':
            activity.vimeo = {};
            break;
        case 'https://wechat.com':
            activity.wechat = {};
            break;
        case 'https://whatsapp.com':
            activity.whatsapp = {};
            break;
        case 'https://forums.whirlpool.net.au':
            activity.whirlpoolfinance = {};
            break;
        case 'https://youtube.com':
            activity.youtube = {};
            break;
        case 'https://bogleheads.org':
            activity.bogleheads = {};
            break;
        case 'https://discord.com':
            activity.discord = {};
            break;
        case 'https://telegram.org':
            activity.telegram = {};
            break;
        case 'https://facebook.com':
            activity.facebook = {};
            break;
        case 'https://instagram.com':
            activity.instagram = {};
            break;
        case 'https://tiktok.com':
            activity.tiktok = {};
            break;
    }

    return activity as Activity2;
}

/**
 * Converts Activity2 to form data format for editing
 */
export function mapActivity2ToFormData(activity: Activity2): ActivityFormData {
    // Determine format based on isBroadcast flag
    const format = activity.isBroadcast ? 'broadcast' : 'chatter';

    // Determine source based on activity properties
    let source = '';
    if (activity.tweet) source = 'https://x.com';
    else if (activity.hotcopper) source = 'https://hotcopper.com.au';
    else if (activity.linkedIn) source = 'https://linkedin.com';
    else if (activity.redditPost) source = 'https://reddit.com';
    else if (activity.news) source = 'news';
    else if (activity.asx) source = 'https://asx.com.au';

    // Start with common fields
    const baseFormData: any = {
        format,
        source,
        title: activity.title || '',
        body: activity.body || '',
        url: activity.url || '',
        image: activity.image || '',
        posted: activity.posted ? activity.posted.toISOString() : ''
    };

    // Add author fields if available
    if (activity.author) {
        baseFormData.author = activity.author;
    }

    // Add source-specific fields
    switch (source) {
        case 'https://x.com':
            baseFormData.likes = activity.likes?.toString() || '';
            break;

        case 'https://hotcopper.com.au':
            if (activity.hotcopper) {
                baseFormData.disclosure = activity.hotcopper.disclosure || '';
                baseFormData.sentiment = activity.hotcopper.sentiment || '';
                baseFormData.thread = activity.hotcopper.thread?.id || '';
                baseFormData.threadViews =
                    activity.hotcopper.thread?.views?.toString() || '';
            }
            break;

        case 'https://linkedin.com':
            baseFormData.likes = activity.likes?.toString() || '';
            baseFormData.comments = activity.comments?.toString() || '';
            break;

        case 'https://reddit.com':
            if (activity.redditPost) {
                baseFormData.subreddit = activity.redditPost.subreddit || '';
                baseFormData.score = activity.redditPost.score?.toString() || '';
                baseFormData.ratio = activity.redditPost.ratio?.toString() || '';
            }
            break;

        case 'news':
            if (activity.news?.source) {
                baseFormData.newsSource = {
                    name: activity.news.source.name || '',
                    url: activity.news.source.domain || ''
                };
                baseFormData.image = activity.news.image || '';
            }
            break;

        case 'https://asx.com.au':
            if (activity.asx) {
                baseFormData.sensitive = activity.asx.sensitive || false;
            }
            break;
    }

    return baseFormData as ActivityFormData;
}
