import api from '../../fetchers/api';
import useAPI from '../util/useAPI';
import { ListedEntity } from '@quarterback/types';
import { useMemo } from 'react';

export default function useEntities() {
    const { data, error, isLoading } = useAPI<Array<ListedEntity>>(
        api,
        `/admin/entities`
    );

    return useMemo(
        () => ({
            data,
            isLoading,
            error
        }),
        [data, isLoading, error]
    );
}
