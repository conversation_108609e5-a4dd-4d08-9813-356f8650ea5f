import { usePostMutation } from '@/api/hooks/util/useMutation';
import { ListedEntity } from '@quarterback/types';
import { useCallback, useMemo } from 'react';
import { useSWRConfig } from 'swr';

export default function useNewsCatcherQuery() {
    const { mutate, cache } = useSWRConfig();

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if ([/^\/admin\/newscatcher.*$/].some((it) => it.test(key))) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    const mutation = usePostMutation<any, any>('/admin/newscatcher');

    return useMemo(
        () => ({
            async trigger(...args: Parameters<typeof mutation.trigger>) {
                await mutation.trigger(...args);
                await invalidate();
            }
        }),
        [invalidate, mutation]
    );
}
