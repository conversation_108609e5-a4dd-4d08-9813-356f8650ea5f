import { usePostMutation } from '@/api/hooks/util/useMutation';
import { ListedEntity } from '@quarterback/types';
import { useCallback, useMemo } from 'react';
import { useSWRConfig } from 'swr';

export default function useEntitiesMutation() {
    const { mutate, cache } = useSWRConfig();

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if ([/^\/admin\/entities.*$/].some((it) => it.test(key))) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    const mutation = usePostMutation<Array<ListedEntity>, Array<ListedEntity>>(
        '/admin/entities'
    );

    return useMemo(
        () => ({
            async trigger(...args: Parameters<typeof mutation.trigger>) {
                await mutation.trigger(...args);
                await invalidate();
            }
        }),
        [invalidate, mutation]
    );
}
