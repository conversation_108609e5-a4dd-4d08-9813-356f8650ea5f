import { Activity, ListedEntity, Organisation } from '@quarterback/types';
import z from 'zod';

import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { add } from 'date-fns';
import { useMemo } from 'react';

const ActivityFilters = z.object({
    read: z.boolean().optional(),
    source: z.enum(['asx']).optional(),
    limit: z.number().optional(),
    thread: z.string().optional(),
    archived: z.boolean().optional()
});
type ActivityFilters = z.infer<typeof ActivityFilters>;

export default function useActivities(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined,
    from: Date,
    to: Date,
    filters: ActivityFilters = {}
) {
    const params = new URLSearchParams({
        from: from.toISOString(),
        to: add(to, { days: 1 }).toISOString(),
        ...(entity && {
            symbol: entity.symbol,
            exchange: entity.exchange
        }),
        ...('read' in filters ? { read: JSON.stringify(filters.read) } : {}),
        ...('thread' in filters ? { thread: filters.thread } : {}),
        ...('source' in filters ? { source: filters.source } : {}),
        ...(filters.archived ? { archived: 'true' } : {})
    });

    const { data, error, isLoading } = useAPI<Array<Activity>>(
        api,
        entity && organisation
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/activities`
            : null,
        params
    );

    return useMemo(
        () => ({
            data: data ? z.array(Activity).parse(data) : undefined,
            isLoading,
            error
        }),
        [data, isLoading, error]
    );
}
