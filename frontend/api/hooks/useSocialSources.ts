import api from '../fetchers/api';
import useAPI from './util/useAPI';

// Define the shape of a social source from the API
export interface SocialSource {
    url: string;
    name: string;
    logo: string | null;
}

export default function useSocialSources() {
    const { data, error, isLoading } = useAPI<Array<SocialSource>>(api, '/v1/sources/social');

    return {
        sources: data || [],
        isLoading,
        error
    };
}
