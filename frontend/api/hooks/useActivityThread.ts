import { useOrganisation } from '@/components/OrganisationProvider';
import { Activity } from '@quarterback/types';
import { sub } from 'date-fns';
import { useCallback, useMemo } from 'react';
import useActivities from './useActivities';

export default function useActivityThread(threadId: string | undefined) {
    const organisation = useOrganisation();

    const memoizedParams = useMemo(
        () => ({
            organisation: threadId ? organisation?.selected?.organisation : undefined,
            entity: threadId ? organisation?.selected?.entity : undefined,
            startDate: sub(new Date(), { years: 10 }),
            endDate: new Date(),
            options: {
                thread: threadId
            }
        }),
        [threadId, organisation]
    );

    const { data: activities = [] } = useActivities(
        memoizedParams.organisation,
        memoizedParams.entity,
        memoizedParams.startDate,
        memoizedParams.endDate,
        memoizedParams.options
    );

    const sorter = useCallback((a: Activity, b: Activity) => {
        if (a.type === 'hotcopper' && b.type === 'hotcopper') {
            return a.post - b.post;
        }

        return a.posted.valueOf() - b.posted.valueOf();
    }, []);

    return useMemo(() => {
        return activities.sort(sorter);
    }, [activities, sorter]);
}
