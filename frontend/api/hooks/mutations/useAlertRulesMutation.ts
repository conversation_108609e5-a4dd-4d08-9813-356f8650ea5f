import { useDeleteMutation, usePostMutation } from '@/api/hooks/util/useMutation';
import z from 'zod';
import { Alert, AlertConfig, ListedEntity, Organisation } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback, useMemo } from 'react';

const AlertConfigBody = Alert.omit({
    organisation: true,
    symbol: true,
    exchange: true
}).array();

type BodySchema = z.infer<typeof AlertConfigBody>;

export default function useAlertRulesMutation(
    organisation: Pick<Organisation, 'id'> | undefined,
    entity: Pick<ListedEntity, 'symbol' | 'exchange'> | undefined
) {
    const { mutate, cache } = useSWRConfig();

    const create = usePostMutation<Array<AlertConfig>, BodySchema>(
        organisation && entity
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/alerts/rules`
            : null
    );

    const uncreate = useDeleteMutation(
        organisation && entity
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/alerts/rules`
            : null
    );

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if ([/^\/v1\/organisations\/.*\/.*\/alerts.*$/].some((it) => it.test(key))) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return useMemo(
        () => ({
            async create(...args: Parameters<typeof create.trigger>) {
                await create.trigger(...args);
                await invalidate();
            },
            async delete(...args: Parameters<typeof uncreate.trigger>) {
                await uncreate.trigger(...args);
                await invalidate();
            }
        }),
        [create, invalidate, uncreate]
    );
}
