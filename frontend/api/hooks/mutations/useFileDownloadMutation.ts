import { ActivityFile } from '@quarterback/types';
import api from '../../fetchers/api';
import { log } from '@quarterback/util/gcp';

/**
 * Hook for handling file downloads
 *
 * This hook provides a function to get a signed download URL for a file
 * and optionally trigger the download
 */
export default function useFileDownloadMutation() {
    /**
     * Get a signed download URL for a file
     * @param fileId The storage path of the file to download
     * @returns The file metadata including the signed URL
     */
    const getDownloadUrl = async (fileId: string): Promise<ActivityFile | null> => {
        try {
            // Use the same API fetcher that's used throughout the codebase
            const fileData = await api(`/v1/files/download?fileId=${fileId}`);
            return fileData;
        } catch (error) {
            log('ERROR', 'Error getting download URL:', { error });
            return null;
        }
    };

    /**
     * Download a file using its storage path
     * @param fileId The storage path of the file to download
     * @returns The file metadata including the signed URL, or null if the download failed
     */
    const downloadFile = async (fileId: string): Promise<ActivityFile | null> => {
        try {
            const fileData = await getDownloadUrl(fileId);

            if (!fileData || !fileData.signedUrl) {
                throw new Error('Failed to get download URL');
            }

            // Create a temporary link and trigger the download
            const link = document.createElement('a');
            link.href = fileData.signedUrl;
            link.download = fileData.fileName || 'download';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            return fileData;
        } catch (error) {
            log('ERROR', 'Error downloading file:', { error });
            return null;
        }
    };

    return {
        getDownloadUrl,
        downloadFile
    };
}
