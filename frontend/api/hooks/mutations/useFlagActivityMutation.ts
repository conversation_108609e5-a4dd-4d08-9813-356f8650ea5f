import { useDeleteMutation, usePostMutation } from '@/api/hooks/util/useMutation';
import z from 'zod';
import { Organisation } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback, useMemo } from 'react';

const ActivityFlagged = z.object({
    organisation: z.string().optional(),
    activity: z.string().optional()
});
type ActivityFlagged = z.infer<typeof ActivityFlagged>;

export default function useFlagActivityMutation(organisation: Organisation | undefined) {
    const { mutate, cache } = useSWRConfig();

    const flag = usePostMutation<Array<ActivityFlagged>, Array<ActivityFlagged>>(
        organisation ? `/v1/organisations/${organisation.id}/flagged` : null
    );
    const unflag = useDeleteMutation<Array<ActivityFlagged>>(
        organisation ? `/v1/organisations/${organisation.id}/flagged` : null
    );

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/organisations\/.*\/activities.*$/,
                    /^\/v1\/search.*$/,
                    /^\$inf\$\/v1\/organisations\/.*\/activities.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return useMemo(
        () => ({
            async flag(...args: Parameters<typeof flag.trigger>) {
                await flag.trigger(...args);
                await invalidate();
            },
            async unflag(...args: Parameters<typeof unflag.trigger>) {
                await unflag.trigger(...args);
                await invalidate();
            }
        }),
        [flag, unflag, invalidate]
    );
}
