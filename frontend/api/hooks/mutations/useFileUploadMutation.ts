import { usePostMutation } from '@/api/hooks/util/useMutation';
import { ActivityFile, ActivityFileUploadRequest } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback } from 'react';
import { log } from '@quarterback/util/gcp';

/**
 * Hook for handling file uploads
 *
 * This hook provides functions to:
 * 1. Request a signed URL for uploading a file
 * 2. Upload a file to the signed URL
 */
export default function useFileUploadMutation() {
    const { mutate, cache } = useSWRConfig();

    // Request a signed URL for uploading a file
    const requestSignedUrl = usePostMutation<ActivityFile, ActivityFileUploadRequest>(
        '/v1/files/upload'
    );

    // Upload a file to the signed URL
    const uploadFile = async (file: File, signedUrl: string): Promise<boolean> => {
        try {
            const response = await fetch(signedUrl, {
                method: 'PUT',
                headers: {
                    'Content-Type': file.type
                },
                body: file
            });

            return response.ok;
        } catch (error) {
            console.error('Error uploading file:', error);
            return false;
        }
    };

    // Invalidate cache after file operations
    const invalidateCache = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if ([/^\/v1\/files.*$/].some((it) => it.test(key))) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return {
        /**
         * Request a signed URL and upload a file
         * @param file The file to upload
         * @returns The file metadata including the signed URL
         */
        uploadFile: async (file: File): Promise<ActivityFile | null> => {
            try {
                // 1. Request a signed URL
                const fileData = await requestSignedUrl.trigger({
                    fileName: file.name,
                    fileType: file.type,
                    fileSize: file.size
                });

                // 2. Upload the file to the signed URL
                const uploadSuccess = await uploadFile(file, fileData.signedUrl!);

                if (!uploadSuccess) {
                    throw new Error('Failed to upload file to storage');
                }

                await invalidateCache();
                return fileData;
            } catch (error) {
                log('ERROR', 'Error in file upload process:', { error });
                return null;
            }
        },
        isLoading: requestSignedUrl.isMutating
    };
}
