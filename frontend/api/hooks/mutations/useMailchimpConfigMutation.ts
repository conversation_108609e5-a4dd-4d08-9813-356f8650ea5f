import { usePostMutation } from '@/api/hooks/util/useMutation';
import z from 'zod';
import { ListedEntity, Organisation } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback, useMemo } from 'react';

const MailChimpConfiguration = z.object({
    token: z.string(),
    audience: z.string()
});
type MailChimpConfiguration = z.infer<typeof MailChimpConfiguration>;

export default function useMailchimpConfigMutation(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined
) {
    const { mutate, cache } = useSWRConfig();

    const set = usePostMutation<Array<MailChimpConfiguration>, Array<MailChimpConfiguration>>(
        organisation && entity
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/mailchimp`
            : null
    );

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if ([/^\/v1\/organisations\/.*\/.*\/mailchimp.*$/].some((it) => it.test(key))) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return useMemo(
        () => ({
            async set(...args: Parameters<typeof set.trigger>) {
                await set.trigger(...args);
                await invalidate();
            }
        }),
        [set, invalidate]
    );
}
