import { ActivityFile } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback } from 'react';
import { extractFilePathFromUrl } from '@/utils/fileUtils';

/**
 * Hook for handling activity files operations
 *
 * This hook provides functions to:
 * 1. Associate uploaded files with an activity
 */

// NEEDS FIXING -- DELETE THIS FILE
export default function useActivityFilesMutation() {
    const { mutate, cache } = useSWRConfig();

    // Invalidate cache after file operations
    const invalidateCache = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/files.*$/,
                    /^\/v1\/organisations\/.*\/activities.*$/,
                    /^\$inf\$\/v1\/organisations\/.*\/activities.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    /**
     * Prepare file data for submission with activity
     * @param uploadedFiles Array of uploaded file responses
     * @returns Array of activity file objects ready for submission
     */
    const prepareFilesForSubmission = (
        uploadedFiles: ActivityFile[]
    ): Partial<ActivityFile>[] => {
        return uploadedFiles.map((file) => ({
            fileName: file.fileName,
            fileSize: file.fileSize,
            fileType: file.fileType,
            storagePath: extractFilePathFromUrl(file.signedUrl!)
        }));
    };

    return {
        /**
         * Prepare files for submission with an activity
         * @param uploadedFiles Array of uploaded file responses
         * @returns Array of activity file objects ready for submission
         */
        prepareFilesForSubmission,

        /**
         * Invalidate the cache after file operations
         */
        invalidateCache
    };
}
