import { usePostMutation } from '@/api/hooks/util/useMutation';
import { Author } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback } from 'react';

interface CreateAuthorParams {
    userId: string;
    name: string;
    source: string;
    url?: string | null;
    image?: string | null;
    followers?: number | null;
    following?: number | null;
}

export default function useAuthorMutation() {
    const { mutate, cache } = useSWRConfig();

    const createAuthor = usePostMutation<Author, CreateAuthorParams>('/v1/authors');

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/authors.*$/,
                    /^\$inf\$\/v1\/authors.*$/,
                    /^\/admin\/authors.*$/,
                    /^\$inf\$\/admin\/authors.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return {
        createAuthor: async (params: CreateAuthorParams) => {
            const result = await createAuthor.trigger(params);
            await invalidate();
            return result;
        },
        isLoading: createAuthor.isMutating
    };
}
