import { useDeleteMutation, usePostMutation } from '@/api/hooks/util/useMutation';
import { ActivityRead } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback, useMemo } from 'react';

export default function useActivityReadMutation() {
    const { mutate, cache } = useSWRConfig();

    const read = usePostMutation<Array<ActivityRead>, Array<ActivityRead>>(`/v1/user/read`);
    const unread = useDeleteMutation<Array<ActivityRead>>('/v1/user/read');

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/organisations\/.*\/activities.*$/,
                    /^\$inf\$\/v1\/organisations\/.*\/activities.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return useMemo(
        () => ({
            async read(...args: Parameters<typeof read.trigger>) {
                await read.trigger(...args);
                await invalidate();
            },
            async unread(...args: Parameters<typeof unread.trigger>) {
                await unread.trigger(...args);
                await invalidate();
            }
        }),
        [read, unread, invalidate]
    );
}
