import { useDeleteMutation, usePostMutation } from '@/api/hooks/util/useMutation';
import z from 'zod';
import { ActivityArchived, Organisation } from '@quarterback/types';
import { useSWRConfig } from 'swr';
import { useCallback, useMemo } from 'react';

export default function useArchiveActivityMutation(organisation: Organisation | undefined) {
    const { mutate, cache } = useSWRConfig();

    const archive = usePostMutation<Array<ActivityArchived>, Array<ActivityArchived>>(
        organisation ? `/v1/organisations/${organisation.id}/archived` : null
    );
    const unarchive = useDeleteMutation<Array<ActivityArchived>>(
        organisation ? `/v1/organisations/${organisation.id}/archived` : null
    );

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/organisations\/.*\/activities.*$/,
                    /^\$inf\$\/v1\/organisations\/.*\/activities.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    return useMemo(
        () => ({
            async archive(...args: Parameters<typeof archive.trigger>) {
                await archive.trigger(...args);
                await invalidate();
            },
            async unarchive(...args: Parameters<typeof unarchive.trigger>) {
                await unarchive.trigger(...args);
                await invalidate();
            }
        }),
        [archive, unarchive, invalidate]
    );
}
