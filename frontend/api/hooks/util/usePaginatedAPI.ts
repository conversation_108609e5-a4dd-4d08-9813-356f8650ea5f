import { useCallback, useEffect, useState } from 'react';
import useSWRInfinite, { SWRInfiniteKeyLoader } from 'swr/infinite';
import { Arguments } from 'swr';
import api from '@/api/fetchers/api';

export default function usePaginatedAPI<Data>(getKey: SWRInfiniteKeyLoader<Data, string | null>) {
    const [isHydrated, setIsHydrated] = useState(false);
    useEffect(() => setIsHydrated(true), []);

    const getKeyIfHydrated = useCallback(
        (pageIndex: number, previousPageData: Data | null) => {
            if (isHydrated) return getKey(pageIndex, previousPageData);
            else return null;
        },
        [isHydrated, getKey]
    );

    return useSWRInfinite<Data>(getKeyIfHydrated, api, { revalidateAll: true });
}
