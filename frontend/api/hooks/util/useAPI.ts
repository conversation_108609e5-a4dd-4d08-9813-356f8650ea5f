import { useEffect, useState } from 'react';
import useS<PERSON>, { Fetcher, SWRConfiguration } from 'swr';

export default function useAPI<T>(
    fetcher: Fetcher<T>,
    path: string | null,
    params: URLSearchParams | undefined = undefined,
    config?: SWRConfiguration | undefined
) {
    const [isHydrated, setIsHydrated] = useState(false);
    useEffect(() => setIsHydrated(true), []);

    return useSWR<T>(
        isHydrated && path ? `${path}?${params?.toString() ?? ''}` : null,
        fetcher,
        config
    );
}
