import useSWRMutation from 'swr/mutation';

import { deleteMutator, postMutator } from '../../fetchers/mutate';

export function useDeleteMutation<Response>(path: string | null) {
    return useSWRMutation<Response, any, any, URLSearchParams>(path, deleteMutator);
}

export function usePostMutation<Response, Body>(path: string | null) {
    return useSWRMutation<Response, any, any, Body>(path, postMutator<Body, Response>);
}
