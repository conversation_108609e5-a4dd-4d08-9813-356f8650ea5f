import { useCallback, useEffect, useMemo, useState } from 'react';
import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { NewsSource } from './useNewsSources';

interface PaginatedResponse {
    sources: NewsSource[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
    };
}

interface UsePaginatedNewsSourcesOptions {
    initialLimit?: number;
    prefetch?: boolean;
}

export default function usePaginatedNewsSources(
    options: UsePaginatedNewsSourcesOptions = {}
) {
    const { initialLimit = 10, prefetch = true } = options;

    const [searchTerm, setSearchTerm] = useState('');
    const [limit, setLimit] = useState(initialLimit);
    const [offset, setOffset] = useState(0);
    const [allSources, setAllSources] = useState<NewsSource[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [isSearching, setIsSearching] = useState(false);

    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    if (searchTerm) {
        params.append('search', searchTerm);
    }

    const { data, error, isLoading, mutate } = useAPI<PaginatedResponse | NewsSource[]>(
        api,
        '/v1/sources/news',
        params,
        {
            revalidateOnFocus: false,
            revalidateIfStale: false,
            dedupingInterval: 10000,
            revalidateOnMount: prefetch
        }
    );

    useEffect(() => {
        if (data) {
            try {
                if (Array.isArray(data)) {
                    if (offset === 0 || isSearching) {
                        setAllSources(data);
                    } else {
                        setAllSources((prev) => [...prev, ...data]);
                    }

                    setTotalCount(data.length);
                } else {
                    const sources = data.sources || [];
                    if (offset === 0 || isSearching) {
                        setAllSources(sources);
                    } else {
                        setAllSources((prev) => [...prev, ...sources]);
                    }

                    const total = data.pagination?.total ?? sources.length;
                    setTotalCount(total);
                }

                setIsSearching(false);
            } catch (err) {
                console.error('Error processing news sources data:', err);
                // Fallback to empty array if there's an error
                if (offset === 0 || isSearching) {
                    setAllSources([]);
                }
                setTotalCount(0);
                setIsSearching(false);
            }
        }
    }, [data, offset, isSearching]);

    useEffect(() => {
        if (prefetch) {
            mutate();
        }
    }, [prefetch, mutate]);

    const handleSearch = useCallback((term: string) => {
        setSearchTerm(term);
        setOffset(0);
        setIsSearching(true);
    }, []);

    // Load more data
    const loadMore = useCallback(() => {
        if (!isLoading && allSources.length < totalCount) {
            setOffset((prev) => prev + limit);
        }
    }, [isLoading, allSources.length, totalCount, limit]);

    // Determine if there are more items to load
    const hasMore = useMemo(() => {
        // If we're using the old API format (array response), we don't have pagination
        if (Array.isArray(data)) {
            return false;
        }

        // If we have pagination data, use it to determine if there are more items
        if (data?.pagination) {
            return allSources.length < (data.pagination.total || 0);
        }

        // Default fallback - if we have sources and they match our current limit, assume there might be more
        return allSources.length >= limit && !isLoading;
    }, [data, allSources.length, limit, isLoading]);

    return {
        sources: allSources,
        isLoading,
        error,
        totalCount,
        hasMore,
        searchTerm,
        handleSearch,
        loadMore,
        refresh: mutate
    };
}
