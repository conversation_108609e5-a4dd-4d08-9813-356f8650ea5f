import { Author } from '@quarterback/types';
import api from '../fetchers/api';
import useAPI from './util/useAPI';

interface AuthorsResponse {
    authors: Author[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
    };
}

interface UseAuthorsOptions {
    sourceFilter?: string;
    limit?: number;
}

export default function useAuthors(options: UseAuthorsOptions = {}) {
    const { sourceFilter, limit = 100 } = options;

    const params = new URLSearchParams({
        limit: limit.toString(),
        offset: '0'
    });

    if (sourceFilter) {
        params.append('source', sourceFilter);
    }

    const { data, error, isLoading } = useAPI<AuthorsResponse>(
        api,
        '/v1/authors',
        params
    );

    return {
        authors: data?.authors || [],
        isLoading,
        error,
        totalCount: data?.pagination.total || 0
    };
}
