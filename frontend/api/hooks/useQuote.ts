import z from 'zod';

import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { useMemo } from 'react';

import { Quote } from '@quarterback/types';

export default function useQuote(
    entity: { symbol: string; exchange: string } | undefined
): {
    isLoading: boolean;
    error: any;
    data: Quote | undefined;
} {
    const params = new URLSearchParams(
        entity
            ? {
                  symbol: entity.symbol,
                  exchange: entity.exchange
              }
            : {}
    );

    const { data, error, isLoading } = useAPI<Quote>(
        api,
        entity ? `/v1/quote` : null,
        params
    );

    return useMemo(
        () => ({
            data: data && !error ? Quote.parse(data) : undefined,
            isLoading,
            error
        }),
        [data, error, isLoading]
    );
}
