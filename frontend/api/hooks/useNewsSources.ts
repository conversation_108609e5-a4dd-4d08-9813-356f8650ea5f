import api from '../fetchers/api';
import useAPI from './util/useAPI';

// Define the shape of a news source from the API
export interface NewsSource {
    url: string;
    name: string;
    logo: string | null;
}

export default function useNewsSources() {
    const { data, error, isLoading } = useAPI<Array<NewsSource>>(api, '/v1/sources/news');

    return {
        sources: data || [],
        isLoading,
        error
    };
}
