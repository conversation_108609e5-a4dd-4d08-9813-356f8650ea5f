import { Activity } from '@quarterback/types';
import { useMemo } from 'react';

export function isChatter(activity: Activity): boolean {
    return 'isBroadcast' in activity && !activity.isBroadcast;
}

export default function useChatter(activities: Array<Activity> | undefined) {
    return useMemo(() => {
        return (activities ?? []).filter((activity) => isChatter(activity));
    }, [activities]);
}
