import useAPI from '@/api/hooks/util/useAPI';
import { Activity, ListedEntity, Organisation } from '@quarterback/types';
import api from '@/api/fetchers/api';
import { useMemo } from 'react';
import z from 'zod';

const MailChimpConfiguration = z.object({
    token: z.string(),
    audience: z.string()
});
type MailChimpConfiguration = z.infer<typeof MailChimpConfiguration>;

export default function useMailchimpConfig(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined
) {
    const { data, error, isLoading } = useAPI<Array<MailChimpConfiguration>>(
        api,
        entity && organisation
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/mailchimp`
            : null
    );

    return useMemo(
        () => ({
            data: data,
            isLoading,
            error
        }),
        [data, isLoading, error]
    );
}
