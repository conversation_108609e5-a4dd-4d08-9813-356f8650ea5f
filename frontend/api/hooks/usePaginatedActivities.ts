import { Activity, ListedEntity, Organisation } from '@quarterback/types';
import z from 'zod';

import { useMemo } from 'react';
import usePaginatedAPI from '@/api/hooks/util/usePaginatedAPI';
import { SWRInfiniteKeyLoader } from 'swr/infinite';

const PAGE_SIZE = 100;

export const PaginatedActivitiesFilters = z.object({
    read: z.boolean().optional(),
    flagged: z.boolean().optional(),
    archived: z.boolean().optional()
});
export type PaginatedActivitiesFilters = z.infer<typeof PaginatedActivitiesFilters>;

export function makeKeyLoader<Data>(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined,
    options: PaginatedActivitiesFilters
): SWRInfiniteKeyLoader<Data, string | null> {
    return (pageIndex: number, previousPageData: any) => {
        if (!entity || !organisation) {
            return null;
        } else if (previousPageData && !previousPageData.length) {
            return null; // Reached the end
        } else {
            const params = new URLSearchParams({
                limit: `${PAGE_SIZE}`,
                ...(previousPageData && {
                    to: Activity.parse(
                        previousPageData[previousPageData.length - 1]
                    ).posted.toISOString()
                }),
                ...('read' in options ? { read: JSON.stringify(options.read) } : {}),
                ...('flagged' in options ? { flagged: JSON.stringify(options.flagged) } : {}),
                ...('archived' in options ? { archived: JSON.stringify(options.archived) } : {})
            });

            return `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/activities?${params.toString()}`;
        }
    };
}

export default function usePaginatedActivities(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined,
    options: PaginatedActivitiesFilters = {}
) {
    const keyLoader = useMemo(
        () => makeKeyLoader(organisation, entity, options),
        [organisation, entity, options]
    );

    const { data, ...rest } = usePaginatedAPI<Array<Activity>>(keyLoader);

    return useMemo(
        () => ({
            data: data?.flatMap((it) => z.array(Activity).parse(it)),
            hasReachedEnd:
                data?.[0]?.length === 0 || (data && data[data.length - 1]?.length < PAGE_SIZE),
            ...rest
        }),
        [data, rest]
    );
}
