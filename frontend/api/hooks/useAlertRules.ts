import { AlertConfig, Alert, ListedEntity, Organisation } from '@quarterback/types';

import api from '../fetchers/api';
import useAPI from './util/useAPI';

export default function useAlertRules(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined
) {
    const { data, error, isLoading } = useAPI<Array<AlertConfig>>(
        api,
        organisation && entity
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/alerts/rules`
            : null
    );

    return {
        data: data ? data.map((it) => Alert.parse(it)) : undefined,
        isLoading,
        error
    };
}
