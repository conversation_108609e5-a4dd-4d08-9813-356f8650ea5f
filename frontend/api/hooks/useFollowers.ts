import z from 'zod';
import { useOrganisation } from '@/components/OrganisationProvider';
import useAPI from '@/api/hooks/util/useAPI';
import { Activity, Followers, ListedEntity } from '@quarterback/types';
import api from '@/api/fetchers/api';
import { add } from 'date-fns';
import { useMemo } from 'react';

export default function useFollowers(entity: ListedEntity | undefined, from: Date, to: Date) {
    const params = new URLSearchParams({
        from: from.toISOString(),
        to: add(to, { days: 1 }).toISOString(),
        ...(entity && {
            symbol: entity.symbol,
            exchange: entity.exchange
        })
    });

    const { data, error, isLoading } = useAPI<Array<Activity>>(
        api,
        entity ? `/v1/followers` : null,
        params
    );

    return useMemo(
        () => ({
            data: data ? Followers.parse(data) : undefined,
            isLoading,
            error
        }),
        [data, isLoading, error]
    );
}
