import { useCallback, useEffect, useMemo, useState } from 'react';
import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { SocialSource } from './useSocialSources';

interface PaginatedResponse {
    sources: SocialSource[];
    pagination: {
        total: number;
        limit: number;
        offset: number;
    };
}

interface UsePaginatedSocialSourcesOptions {
    initialLimit?: number;
    prefetch?: boolean;
}

export default function usePaginatedSocialSources(
    options: UsePaginatedSocialSourcesOptions = {}
) {
    const { initialLimit = 10, prefetch = true } = options;

    const [searchTerm, setSearchTerm] = useState('');
    const [limit, setLimit] = useState(initialLimit);
    const [offset, setOffset] = useState(0);
    const [allSources, setAllSources] = useState<SocialSource[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [isSearching, setIsSearching] = useState(false);

    // Build query params
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    if (searchTerm) {
        params.append('search', searchTerm);
    }

    // Fetch data with the current params
    const { data, error, isLoading, mutate } = useAPI<PaginatedResponse | SocialSource[]>(
        api,
        '/v1/sources/social',
        params,
        {
            revalidateOnFocus: false,
            revalidateIfStale: false,
            dedupingInterval: 10000,
            revalidateOnMount: prefetch
        }
    );

    useEffect(() => {
        if (data) {
            try {
                if (Array.isArray(data)) {
                    if (offset === 0 || isSearching) {
                        setAllSources(data);
                    } else {
                        setAllSources((prev) => [...prev, ...data]);
                    }

                    setTotalCount(data.length);
                } else {
                    const sources = data.sources || [];
                    if (offset === 0 || isSearching) {
                        setAllSources(sources);
                    } else {
                        setAllSources((prev) => [...prev, ...sources]);
                    }

                    const total = data.pagination?.total ?? sources.length;
                    setTotalCount(total);
                }

                setIsSearching(false);
            } catch (err) {
                console.error('Error processing news sources data:', err);
                if (offset === 0 || isSearching) {
                    setAllSources([]);
                }
                setTotalCount(0);
                setIsSearching(false);
            }
        }
    }, [data, offset, isSearching]);

    // Prefetch initial data
    useEffect(() => {
        if (prefetch) {
            mutate();
        }
    }, [prefetch, mutate]);

    // Handle search
    const handleSearch = useCallback((term: string) => {
        setSearchTerm(term);
        setOffset(0);
        setIsSearching(true);
    }, []);

    // Load more data
    const loadMore = useCallback(() => {
        if (!isLoading && allSources.length < totalCount) {
            setOffset((prev) => prev + limit);
        }
    }, [isLoading, allSources.length, totalCount, limit]);

    // Determine if there are more items to load
    const hasMore = useMemo(() => {
        if (Array.isArray(data)) {
            return false;
        }

        if (data?.pagination) {
            return allSources.length < (data.pagination.total || 0);
        }

        return allSources.length >= limit && !isLoading;
    }, [data, allSources.length, limit, isLoading]);

    return {
        sources: allSources,
        isLoading,
        error,
        totalCount,
        hasMore,
        searchTerm,
        handleSearch,
        loadMore,
        refresh: mutate
    };
}
