import { ActivityFile } from '@quarterback/types';
import api from '../fetchers/api';
import useAPI from './util/useAPI';

export function useActivityFiles(activityId: string | undefined) {
    const url = activityId ? `/v1/files?activity=${activityId}` : null;

    const params = new URLSearchParams({
        activity: activityId || ''
    });

    const { data, error, isLoading, mutate } = useAPI<ActivityFile[]>(
        api,
        activityId ? `/v1/files` : null,
        params,
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            shouldRetryOnError: false
        }
    );

    return {
        files: data || [],
        isLoading,
        isError: error,
        mutate
    };
}
