import { Activity2, ListedEntity, Organisation } from '@quarterback/types';

import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { useMemo } from 'react';
import z, { number } from 'zod';

const SearchResult = z.object({
    activity: Activity2,
    similarity: z.number()
});
type SearchResult = z.infer<typeof SearchResult>;

const SearchResults = z.array(SearchResult);
type SearchResults = z.infer<typeof SearchResults>;

export default function useSearch(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined,
    from: Date | undefined,
    to: Date | undefined,
    query: string
) {
    const params = new URLSearchParams({
        query,
        ...(entity && {
            symbol: entity.symbol,
            exchange: entity.exchange
        }),
        ...(organisation && {
            organisation: organisation.id
        }),
        ...(from && { from: from.toISOString() }),
        ...(to && { to: to.toISOString() })
    });

    const { data, error, isValidating, isLoading } = useAPI<SearchResults>(
        api,
        organisation && entity && query.length >= 3 ? `/v1/search` : null,
        params,
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            shouldRetryOnError: false
        }
    );

    return useMemo(() => {
        return {
            data: data && !error ? data : undefined,
            isLoading,
            isValidating,
            error
        };
    }, [data, error, isValidating, isLoading]);
}
