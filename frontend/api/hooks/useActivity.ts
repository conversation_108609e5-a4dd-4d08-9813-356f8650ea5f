import { Activity, ListedEntity, Organisation } from '@quarterback/types';

import api from '../fetchers/api';
import useAPI from './util/useAPI';

export default function useActivity(
    organisation: Organisation | undefined,
    entity: ListedEntity | undefined,
    id: string | undefined
) {
    const { data, error, isLoading } = useAPI<Activity>(
        api,
        organisation && entity && id
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/activities/${id}`
            : null
    );

    return {
        data: data ? Activity.parse(data) : undefined,
        isLoading,
        error
    };
}
