import { add, format } from 'date-fns';
import z from 'zod';

import api from '../fetchers/api';
import useAPI from './util/useAPI';
import { useMemo } from 'react';
import * as Sentry from '@sentry/nextjs';

export const TimeSeriesQuote = z.object({
    datetime: z.string(),
    open: z.coerce.number(),
    high: z.coerce.number(),
    low: z.coerce.number(),
    close: z.coerce.number(),
    volume: z.coerce.number()
});
export type TimeSeriesQuote = z.infer<typeof TimeSeriesQuote>;

export const TimeSeries = z.object({
    meta: z.object({
        symbol: z.string(),
        interval: z.string(),
        currency: z.string(),
        exchange_timezone: z.string(),
        exchange: z.string(),
        mic_code: z.string(),
        type: z.string()
    }),
    values: z.array(TimeSeriesQuote),
    status: z.enum(['ok'])
});

export type TimeSeries = z.infer<typeof TimeSeries>;

export default function useTimeSeries(
    entity: { symbol: string; exchange: string } | undefined,
    start: Date,
    end: Date,
    limit: number | undefined = undefined
): { isLoading: boolean; error: any; data: TimeSeries | undefined } {
    const params = new URLSearchParams({
        ...(entity ? { symbol: entity.symbol, exchange: entity.exchange } : {}),
        ...(limit ? { limit: `${limit}` } : {}),
        start: format(start, 'yyyy-MM-dd'),
        end: format(add(end, { days: 1 }), 'yyyy-MM-dd'),
        interval: '1day'
    });

    const { data, error, isLoading } = useAPI(api, entity ? `/v1/time-series` : null, params);

    return useMemo(() => {
        if (data && !error) {
            try {
                const series = TimeSeries.parse(data);

                return {
                    data: {
                        ...series,
                        // For some fucking reason our fucking market data vendor returns duplicate results
                        // despite interval being set to 1day, so we need to deduplicate
                        values: series.values.filter((value, index, array) => {
                            return (
                                index === array.findIndex((it) => it.datetime === value.datetime)
                            );
                        })
                    },
                    isLoading,
                    error
                };
            } catch (error) {
                Sentry.addBreadcrumb({ type: 'http', message: data });
                Sentry.captureException(error);

                return {
                    data: undefined,
                    isLoading,
                    error
                };
            }
        } else {
            return {
                data: undefined,
                isLoading,
                error
            };
        }
    }, [data, error, isLoading]);
}
