import useAPI from '@/api/hooks/util/useAPI';
import { Activity, ListedEntity, Organisation } from '@quarterback/types';
import api from '@/api/fetchers/api';
import { useOrganisation } from '@/components/OrganisationProvider';
import z from 'zod';
import { useMemo } from 'react';

export const ActivitiesCountOptions = z.object({
    read: z.boolean().optional(),
    provenance: z.enum(['broadcast', 'chatter']).optional()
});
export type ActivitiesCountOptions = z.infer<typeof ActivitiesCountOptions>;

export default function useActivitiesCount(
    organisation: Organisation | undefined | null,
    entity: ListedEntity | undefined | null,
    options: ActivitiesCountOptions = {}
) {
    const params = useMemo(
        () =>
            new URLSearchParams({
                ...(options.read !== undefined ? { read: JSON.stringify(options.read) } : {}),
                ...(options.provenance !== undefined ? { provenance: options.provenance } : {})
            }),
        [options]
    );

    return useAPI<number>(
        api,
        organisation && entity
            ? `/v1/organisations/${organisation.id}/${entity.symbol}:${entity.exchange}/activities/count`
            : null,
        params
    );
}
