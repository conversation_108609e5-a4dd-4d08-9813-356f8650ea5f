import { format, sub } from 'date-fns';
import useTimeSeries from './useTimeSeries';
import { useMemo } from 'react';
import { Risk } from '@quarterback/types';

function linearRegression(x: number[], y: number[]): Risk {
    const n = y.length;
    if (n === 0) return { slope: 0, intercept: 0, r2: 0 };

    let sum_x = 0;
    let sum_y = 0;
    let sum_xy = 0;
    let sum_xx = 0;
    let sum_yy = 0;

    for (let i = 0; i < Math.min(x.length, y.length); i++) {
        sum_x += x[i];
        sum_y += y[i];
        sum_xy += x[i] * y[i];
        sum_xx += x[i] * x[i];
        sum_yy += y[i] * y[i];
    }

    const denominator = n * sum_xx - sum_x * sum_x;
    if (denominator === 0) return { slope: 0, intercept: 0, r2: 0 }; // Handle division by zero

    const slope = (n * sum_xy - sum_x * sum_y) / denominator;
    const intercept = (sum_y - slope * sum_x) / n;

    const r2_denominator = Math.sqrt(
        (n * sum_xx - sum_x * sum_x) * (n * sum_yy - sum_y * sum_y)
    );
    const r2 =
        r2_denominator !== 0
            ? Math.pow((n * sum_xy - sum_x * sum_y) / r2_denominator, 2)
            : 0;

    return { slope, intercept, r2 };
}

export default function useRisk(
    stock: { symbol: string; exchange: string } | undefined,
    index: { symbol: string; exchange: string } | undefined
): { data: Risk | undefined; isLoading: boolean } {
    const end = sub(new Date(), { days: 1 });
    const start = sub(end, { days: 250 });

    const symbolSeries = useTimeSeries(stock, start, end);
    const indexSeries = useTimeSeries(index, start, end);

    return useMemo(() => {
        const change = (current: { close: number }, previous: { close: number }) =>
            Math.log(current.close / previous.close);

        const alignedStockValues =
            symbolSeries.data?.values?.filter((stockVal) =>
                indexSeries.data?.values?.some(
                    (indexVal) => stockVal.datetime === indexVal.datetime
                )
            ) || [];

        const alignedIndexValues =
            indexSeries.data?.values?.filter((indexVal) =>
                alignedStockValues.some(
                    (stockVal) => stockVal.datetime === indexVal.datetime
                )
            ) || [];

        const stockChange = alignedStockValues
            .map((value, i, arr) => (i > 0 ? change(value, arr[i - 1]) : 0))
            .slice(1);

        const indexChange = alignedIndexValues
            .map((value, i, arr) => (i > 0 ? change(value, arr[i - 1]) : 0))
            .slice(1);

        return {
            data: linearRegression(indexChange, stockChange),
            isLoading: symbolSeries.isLoading || indexSeries.isLoading
        };
    }, [indexSeries, symbolSeries]);
}
