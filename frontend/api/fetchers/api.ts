import Session from 'supertokens-auth-react/recipe/session';

const apiFetcher = async (path: string) => {
    const token = await Session.getAccessToken();

    if (!process.env['NEXT_PUBLIC_API_BASE_URL']) {
        throw new Error('API base URL not defined');
    }

    const response = await fetch(`${process.env['NEXT_PUBLIC_API_BASE_URL']}${path}`, {
        headers: { Authorization: `Bearer ${token}` }
    });

    if (!response.ok) {
        throw new Error(`Request failed with status code ${response.status}`);
    }

    return await response.json();
};
export default apiFetcher;
