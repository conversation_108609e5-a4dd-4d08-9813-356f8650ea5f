import Session from 'supertokens-auth-react/recipe/session';

export async function postMutator<Body, Response>(
    path: string,
    { arg }: { arg: Body }
): Promise<Response> {
    const token = await Session.getAccessToken();

    if (!process.env['NEXT_PUBLIC_API_BASE_URL']) {
        throw new Error('API base URL not defined');
    }

    const response = await fetch(`${process.env['NEXT_PUBLIC_API_BASE_URL']}${path}`, {
        headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
        method: 'POST',
        body: JSON.stringify(arg)
    });

    if (!response.ok) {
        throw new Error(`Request failed with status code ${response.status}`);
    }

    return await response.json();
}

export async function deleteMutator<Response>(
    path: string,
    { arg }: { arg: URLSearchParams }
): Promise<Response> {
    const token = await Session.getAccessToken();

    if (!process.env['NEXT_PUBLIC_API_BASE_URL']) {
        throw new Error('API base URL not defined');
    }

    const response = await fetch(
        `${process.env['NEXT_PUBLIC_API_BASE_URL']}${path}?${arg.toString()}`,
        {
            headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
            method: 'DELETE'
        }
    );

    if (!response.ok) {
        throw new Error(`Request failed with status code ${response.status}`);
    }

    return await response.json();
}
