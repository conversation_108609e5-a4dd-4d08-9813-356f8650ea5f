<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="512" height="512" fill="url(#paint0_radial_1_3)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M213.221 105.181L249.025 152.708L213.221 202.197V105.181ZM166.003 234.009V104H205.744V212.404L178.202 250.107L166.003 234.009ZM118 104V170.375L158.526 224.185V104H118ZM299.779 105.181L263.971 152.708L299.779 202.197V105.181ZM346.997 234.009V104H307.252V212.796L334.406 250.504L346.997 234.009ZM395 104V170.375L354.47 224.185V104H395ZM346.997 281.136V408H307.252V226.15L346.997 281.136ZM393.422 345.158V408H354.47V291.348L393.422 345.158ZM299.779 215.938V408L260.038 353.798V161.343L299.779 215.938ZM166.003 281.136V408H205.744V226.15L166.003 281.136ZM119.574 345.158V408H158.526V291.348L119.574 345.158ZM213.221 215.938V408L252.962 353.798V161.343L213.221 215.938Z" fill="white"/>
<defs>
<radialGradient id="paint0_radial_1_3" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(159 145) rotate(52.9349) scale(381.602)">
<stop stop-color="#009FE1"/>
<stop offset="1" stop-color="#14558C"/>
</radialGradient>
</defs>
</svg>
