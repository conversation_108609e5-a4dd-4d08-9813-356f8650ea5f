import EmailPassword from 'supertokens-auth-react/recipe/emailpassword';
import Session from 'supertokens-auth-react/recipe/session';
import { appInfo } from './appinfo';
import { useRouter } from 'next/navigation';
import { SuperTokensConfig } from 'supertokens-auth-react/lib/build/types';

const routerInfo: { router?: ReturnType<typeof useRouter>; pathName?: string } = {};

export function setRouter(router: ReturnType<typeof useRouter>, pathName: string) {
    routerInfo.router = router;
    routerInfo.pathName = pathName;
}

export const frontendConfig = (): SuperTokensConfig => {
    return {
        appInfo,
        recipeList: [
            EmailPassword.init({
                signInAndUpFeature: {
                    signInForm: {
                        style: `
                            [data-supertokens~=superTokensBranding] {
                                display: none;
                            }

                            [data-supertokens~=headerSubtitle] {
                                display: none;
                            }
                        `
                    },
                    signUpForm: {
                        style: `
                            [data-supertokens~=superTokensBranding] {
                                display: none;
                            }
                        `
                    }
                },
                resetPasswordUsingTokenFeature: {
                    submitNewPasswordForm: {
                        style: `
                            [data-supertokens~=superTokensBranding] {
                                display: none;
                            }
                        `
                    },
                    enterEmailForm: {
                        style: `
                            [data-supertokens~=superTokensBranding] {
                                display: none;
                            }
                        `
                    }
                }
            }),
            Session.init()
        ],
        windowHandler: (orig) => {
            return {
                ...orig,
                location: {
                    ...orig.location,
                    getPathName: () => routerInfo.pathName!,
                    assign: (url) => routerInfo.router!.push(url.toString()),
                    setHref: (url) => routerInfo.router!.push(url.toString())
                }
            };
        }
    };
};
