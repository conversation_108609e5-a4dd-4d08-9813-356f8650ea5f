import SuperTokens from 'supertokens-node';
import EmailPasswordNode from 'supertokens-node/recipe/emailpassword';
import SessionNode from 'supertokens-node/recipe/session';
import DashboardNode from 'supertokens-node/recipe/dashboard';
import UserRolesNode from 'supertokens-node/recipe/userroles';

import { appInfo } from './appinfo';
import { TypeInput } from 'supertokens-node/types';

export const backendConfig = (): TypeInput => {
    return {
        framework: 'custom',
        supertokens: {
            connectionURI: process.env.SUPERTOKENS_CONNECTION_URI!,
            apiKey: process.env.SUPERTOKENS_API_TOKEN!
        },
        appInfo,
        recipeList: [
            EmailPasswordNode.init({
                override: {
                    apis: (original) => {
                        return {
                            ...original,
                            signUpPOST: undefined
                        };
                    }
                }
            }),
            SessionNode.init({ exposeAccessTokenToFrontendInCookieBasedAuth: true }),
            DashboardNode.init(),
            UserRolesNode.init()
        ],
        isInServerlessEnv: true
    };
};

let initialized = false;
// This function is used in your APIs to make sure SuperTokens is initialised
export function ensureSuperTokensInit() {
    if (!initialized) {
        SuperTokens.init(backendConfig());
        initialized = true;
    }
}
