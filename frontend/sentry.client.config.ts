import * as Sentry from '@sentry/nextjs';

const url = process.env.NEXT_PUBLIC_FRONTEND_URL ?? '';

if (['https://app.qback.dev', 'https://app.qback.au'].includes(url)) {
    Sentry.init({
        dsn: 'https://<EMAIL>/4507520444530768',

        environment: url === 'https://app.qback.au' ? 'production' : 'development',

        integrations: [Sentry.replayIntegration()],

        tracesSampleRate: 0.5,

        replaysSessionSampleRate: 0.1,
        replaysOnErrorSampleRate: 1.0
    });
}
