import { withSentryConfig } from '@sentry/nextjs';

/** @type {import('next').NextConfig} */
const nextConfig = {
    webpack: (config) => {
        return {
            ...config,
            resolve: {
                ...(config.resolve ?? {}),
                fallback: {
                    ...(config.resolve.fallback ?? {}),
                    fs: false,
                    stream: import.meta.resolve('stream-browserify') // Honestly just fuck JavaScript
                }
            }
        };
    }
};

export default withSentryConfig(nextConfig, {
    org: 'quarterback',
    project: 'frontend',
    authToken: process.env.SENTRY_AUTH_TOKEN,
    silent: false
});
