FROM node:22-alpine AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
# RUN corepack enable #corepack is causing issues. enable later and remove line below
RUN npm install -g pnpm@9.15.5

FROM base AS build
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_FRONTEND_URL
ARG NEXT_PUBLIC_POSTHOG_HOST
ARG NEXT_PUBLIC_POSTHOG_KEY
ARG SENTRY_AUTH_TOKEN
ARG SUPERTOKENS_CONNECTION_URI
ARG SUPERTOKENS_API_TOKEN
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_FRONTEND_URL=$NEXT_PUBLIC_FRONTEND_URL
ENV NEXT_PUBLIC_POSTHOG_HOST=$NEXT_PUBLIC_POSTHOG_HOST
ENV NEXT_PUBLIC_POSTHOG_KEY=$NEXT_PUBLIC_POSTHOG_KEY
ENV SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN
ENV SUPERTOKENS_CONNECTION_URI=$SUPERTOKENS_CONNECTION_URI
ENV SUPERTOKENS_API_TOKEN=$SUPERTOKENS_API_TOKEN
COPY pnpm-lock.yaml /app/
COPY pnpm-workspace.yaml /app/
COPY package.json /app/
COPY ./types /app/types
COPY ./util /app/util
COPY ./frontend /app/frontend
WORKDIR /app
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --filter=@quarterback/frontend --filter=@quarterback/types --filter=@quarterback/util --config.dedupe-peer-dependents=false --frozen-lockfile
RUN pnpm run -r --filter=@quarterback/frontend --filter=@quarterback/types --filter=@quarterback/util build
RUN pnpm deploy --filter=@quarterback/frontend --prod /dist/frontend
RUN mkdir -p /dist/frontend/.next && cp -r /app/frontend/.next/* /dist/frontend/.next/

FROM base as frontend
COPY --from=build /dist/frontend /app
WORKDIR /app
ENTRYPOINT [ "pnpm", "start" ]
