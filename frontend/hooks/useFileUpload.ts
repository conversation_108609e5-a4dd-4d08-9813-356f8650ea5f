import { useState, useCallback } from 'react';
import { ActivityFile, ActivityFile as BaseActivityFile } from '@quarterback/types';
import useFileUploadMutation from '@/api/hooks/mutations/useFileUploadMutation';
import { extractFilePathFromUrl } from '@/utils/fileUtils';

interface UseFileUploadOptions {
    onUploadSuccess?: (file: ActivityFile[]) => void;
    onUploadError?: (error: Error) => void;
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
    const [isUploading, setIsUploading] = useState(false);
    const [uploadedFiles, setUploadedFiles] = useState<ActivityFile[]>([]);
    const { uploadFile, isLoading } = useFileUploadMutation();

    const uploadFiles = useCallback(
        async (files: File[]): Promise<ActivityFile[]> => {
            try {
                setIsUploading(true);
                const results = await Promise.all(files.map((file) => uploadFile(file)));
                setIsUploading(false);

                const response = results.filter(Boolean).map(
                    (it) =>
                        ({
                            ...it,
                            storagePath: extractFilePathFromUrl(it?.signedUrl!)
                        }) as ActivityFile
                ) as ActivityFile[];

                setUploadedFiles(response);
                options?.onUploadSuccess?.(response);

                return response;
            } catch (error: any) {
                setIsUploading(false);
                options?.onUploadError?.(error);
                return [];
            }
        },
        [options, uploadFile]
    );

    return {
        isUploading: isLoading || isUploading,
        uploadedFiles,
        uploadFile,
        uploadFiles,
        setUploadedFiles
    };
}
