/**
 * Extract the file path from a signed upload URL
 * 
 * @param signedUrl The signed URL from GCP Storage
 * @returns The file path in the bucket
 */
export function extractFilePathFromUrl(signedUrl: string): string {
    try {
        const url = new URL(signedUrl);
        
        // Extract the path from the URL
        // The format is typically /bucket-name/path/to/file?query-params
        const pathWithQuery = url.pathname;
        
        // Find the bucket name from the environment or extract it from the URL
        const bucketName = process.env.NEXT_PUBLIC_GCP_ACTIVITY_FILES_BUCKET || 
            pathWithQuery.split('/')[1]; // Extract bucket name from URL
        
        // The path starts with /bucket-name/
        const path = pathWithQuery.substring(bucketName.length + 2);
        
        return path;
    } catch (error) {
        console.error('Failed to extract file path from URL', error, signedUrl);
        // Return a fallback path with timestamp to avoid collisions
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        return `activity-files/${timestamp}-${randomString}-unknown-file`;
    }
}
