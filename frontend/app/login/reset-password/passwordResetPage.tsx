'use client';
import Alert from '@/components/Alert';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { FormEvent, useState } from 'react';
import {
    doesEmailExist,
    sendPasswordResetEmail,
    submitNewPassword
} from 'supertokens-auth-react/recipe/emailpassword';

export default function ResetPassword() {
    const [status, setStatus] = useState<
        | {
              status: 'error' | 'success';
              message: string;
          }
        | undefined
    >(undefined);

    const [email, setEmail] = useState('');

    const [password, setPassword] = useState<string>('');

    const params = useSearchParams();

    const token = params.get('token') || undefined;
    const tenantId = params.get('tenantId') || undefined;

    async function handlePasswordResetEmail(event: FormEvent) {
        event.preventDefault();

        const { doesExist } = await doesEmailExist({ email, options: {} });

        if (!doesExist) {
            setStatus({ status: 'error', message: 'Email address not found' });
            return;
        }

        if (doesExist) {
            const response = await sendPasswordResetEmail({
                formFields: [{ id: 'email', value: email }]
            });

            switch (response.status) {
                case 'FIELD_ERROR':
                    setStatus({
                        status: 'error',
                        message: 'Email password combination is incorrect.'
                    });
                    break;
                case 'PASSWORD_RESET_NOT_ALLOWED':
                    setStatus({ status: 'error', message: 'An error occured' });
                    break;
                case 'OK':
                    setEmail('');
                    setStatus({
                        status: 'success',
                        message:
                            'An email with the password reset instructions has been sent'
                    });
                    break;
            }
        }
    }

    async function handlePasswordReset(event: FormEvent) {
        event.preventDefault();

        if (!token || !tenantId) {
            setStatus({
                status: 'error',
                message: 'Token and tenantId is required'
            });
            return;
        }

        const response = await submitNewPassword({
            formFields: [
                {
                    id: 'password',
                    value: password
                }
            ]
        });

        switch (response.status) {
            case 'RESET_PASSWORD_INVALID_TOKEN_ERROR':
                setStatus({
                    status: 'error',
                    message: 'The token is invalid or expired'
                });
                break;
            case 'FIELD_ERROR':
                setStatus({ status: 'error', message: response.formFields[0].error });
                break;
            case 'OK':
                setPassword('');
                setStatus({ status: 'success', message: 'password reset successfully' });
                break;
        }
    }

    return (
        <div className="flex min-h-full flex-1">
            <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:max-w-3xl lg:px-20 xl:px-24">
                <div className="mx-auto w-full max-w-sm lg:w-96">
                    <div>
                        <img className="h-14 w-auto" src="/logo.svg" alt="Quarterback" />
                        <h2 className="mt-8 text-2xl font-bold leading-9 tracking-tight text-gray-900">
                            Reset your password
                        </h2>
                    </div>

                    {status ? (
                        <div className="mt-4 -mb-4">
                            <Alert
                                status={status.status}
                                text={status.message}
                                dismiss={() => setStatus(undefined)}
                            />
                        </div>
                    ) : null}

                    <div className="mt-10">
                        {token && tenantId ? (
                            <div>
                                <form
                                    action="#"
                                    method="POST"
                                    className="space-y-6"
                                    onSubmit={handlePasswordReset}>
                                    <div>
                                        <label
                                            htmlFor="password"
                                            className="block text-sm font-medium leading-6 text-gray-900">
                                            New password
                                        </label>
                                        <div className="mt-2">
                                            <input
                                                id="password"
                                                name="password"
                                                type="password"
                                                value={password}
                                                onChange={(e) =>
                                                    setPassword(e.target.value)
                                                }
                                                required
                                                className="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <button
                                            type="submit"
                                            className="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                            Reset password
                                        </button>
                                    </div>
                                </form>
                            </div>
                        ) : (
                            <div>
                                <form
                                    action="#"
                                    method="POST"
                                    className="space-y-6"
                                    onSubmit={handlePasswordResetEmail}>
                                    <div>
                                        <label
                                            htmlFor="email"
                                            className="block text-sm font-medium leading-6 text-gray-900">
                                            Email address
                                        </label>
                                        <div className="mt-2">
                                            <input
                                                id="email"
                                                name="email"
                                                type="email"
                                                autoComplete="email"
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                required
                                                className="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <button
                                            type="submit"
                                            className="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                                            send Email
                                        </button>
                                    </div>
                                </form>
                            </div>
                        )}
                    </div>
                    <Link
                        href="/login"
                        className="font-semibold text-indigo-600 hover:text-indigo-500 flex justify-center mt-6">
                        Back to login
                    </Link>
                </div>
            </div>
            <div className="relative hidden w-0 flex-1 lg:block">
                <img
                    className="absolute inset-0 h-full w-full object-cover"
                    src="/magicpattern.jpg"
                    alt=""
                />
            </div>
        </div>
    );
}
