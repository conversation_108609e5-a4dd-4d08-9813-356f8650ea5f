'use client';

import Navigation from '@/components/navigation';
import React from 'react';
import { SessionAuth } from 'supertokens-auth-react/recipe/session';
import OrganisationProvider from '@/components/OrganisationProvider';
import TrackedIdentityProvider from '@/components/TrackedIdentityProvider';
import CommandPalette from '@/components/CommandPalette';
import PageHeader from '@/components/ui/PageHeader';

export default function AuthenticatedLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <SessionAuth>
            <OrganisationProvider>
                <TrackedIdentityProvider>
                    <CommandPalette>
                        <Navigation>{children}</Navigation>
                    </CommandPalette>
                </TrackedIdentityProvider>
            </OrganisationProvider>
        </SessionAuth>
    );
}
