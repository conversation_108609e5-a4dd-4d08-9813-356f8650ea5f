'use client';

import React, { useMemo, useState } from 'react';
import { useOrganisation } from '@/components/OrganisationProvider';
import useAlertRules from '@/api/hooks/useAlertRules';
import useAlertRulesMutation from '@/api/hooks/mutations/useAlertRulesMutation';
import { useUser } from '@/api/hooks/useUser';

export default function Alerts() {
    const organisation = useOrganisation();
    const { data: userData, isLoading: userLoading } = useUser();

    const { create } = useAlertRulesMutation(
        organisation?.selected?.organisation,
        organisation?.selected?.entity
    );

    const {
        data,
        isLoading: alertsLoading,
        error
    } = useAlertRules(
        organisation?.selected?.organisation,
        organisation?.selected?.entity
    );

    const createAlert = () => {
        if (userData) {
            create([
                {
                    field: 'SHARE_PERCENT',
                    threshold: 1.0,
                    comparator: 'GTE',
                    emails: userData.emails,
                    interval: null,
                    searchTerm: null
                }
            ]);
        }
    };

    const loading = userLoading || alertsLoading;

    return (
        <div className="p-4">
            <h2 className="text-lg font-semibold leading-7 text-gray-900 sm:truncate sm:text-xl sm:tracking-tight">
                Alerts
            </h2>
            {loading ? (
                <div>Loading...</div>
            ) : (
                <>
                    <button
                        className="bg-green-600 text-white rounded-md p-5"
                        onClick={createAlert}>
                        Create an Alert!
                    </button>
                    <div className="bg-white rounded-md p-4 mt-4 shadow flex items-end gap-x-4">
                        {error && (
                            <div className="bg-red text-white">
                                {JSON.stringify(error)}
                            </div>
                        )}
                        <pre className="pretty">{JSON.stringify(data, undefined, 2)}</pre>
                    </div>
                </>
            )}
        </div>
    );
}
