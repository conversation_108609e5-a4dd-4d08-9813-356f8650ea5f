import useAlertRulesMutation from '@/api/hooks/mutations/useAlertRulesMutation';
import AlertNotificationConfig from '@/app/(authenticated)/alerts/_form/AlertNotificationConfig';
import AlertTypePicker from '@/app/(authenticated)/alerts/_form/AlertTypePicker';
import PlaceholderAlertForm from '@/app/(authenticated)/alerts/_form/PlaceholderAlertForm';
import PriceMovementAlertForm from '@/app/(authenticated)/alerts/_form/PriceMovementAlertForm';
import SearchAlertForm from '@/app/(authenticated)/alerts/_form/SearchAlertForm';
import { useOrganisation } from '@/components/OrganisationProvider';
import { DialogTitle, Transition } from '@headlessui/react';
import { ActivityAlertInterval, AlertField, AlertThreshold } from '@quarterback/types';
import classNames from 'classnames';
import React, { useMemo, useState } from 'react';
import ActivityAlertForm from './ActivityAlertForm';
import SentimentAlertForm from './SentimentAlertForm';

export default function AlertForm({
    setOpen
}: {
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    const organisation = useOrganisation();

    const { create: createAlerts } = useAlertRulesMutation(
        organisation?.selected?.organisation,
        organisation?.selected?.entity
    );

    const [type, setType] = useState<AlertField | undefined>(undefined);
    const [emails, setEmails] = useState<Array<string>>([]);
    const [interval, setInterval] = useState<ActivityAlertInterval>('DAY');

    const [threshold, setThreshold] = useState<AlertThreshold>({
        comparator: 'GTE',
        threshold: 10
    });

    const [sentimentThreshold, setSentimentThreshold] = useState<AlertThreshold>({
        comparator: 'GTE',
        threshold: 0.75
    });

    const [activityThreshold, setActivityThreshold] = useState<AlertThreshold>({
        comparator: 'GTE',
        threshold: 10
    });

    const [broadSearchThreshold, setBroadSearchThreshold] = useState<AlertThreshold>({
        comparator: 'GTE',
        threshold: 0.48
    });

    const [searchTerm, setSearchTerm] = useState<string>('');

    const classes = [
        'w-full transition ease-in-out data-[closed]:opacity-0',
        'data-[leave]:duration-300 data-[leave]:data-[closed]:-translate-x-full',
        'data-[enter]:duration-100 data-[enter]:data-[closed]:translate-x-full'
    ];

    const form = useMemo(() => {
        switch (type) {
            case 'SEARCH':
                return (
                    <SearchAlertForm
                        threshold={broadSearchThreshold}
                        setThreshold={setBroadSearchThreshold}
                        searchTerm={searchTerm}
                        setSearchTerm={setSearchTerm}
                    />
                );
            case 'SHARE_PERCENT':
                return (
                    <PriceMovementAlertForm
                        threshold={threshold}
                        setThreshold={setThreshold}
                    />
                );
            case 'SENTIMENT':
                return (
                    <SentimentAlertForm
                        threshold={sentimentThreshold}
                        setThreshold={setSentimentThreshold}
                    />
                );
            case 'ACTIVITY':
                return (
                    <ActivityAlertForm
                        threshold={activityThreshold}
                        setThreshold={setActivityThreshold}
                        interval={interval}
                        setInterval={setInterval}
                    />
                );
            default:
                return <PlaceholderAlertForm />;
        }
    }, [
        type,
        broadSearchThreshold,
        searchTerm,
        threshold,
        sentimentThreshold,
        activityThreshold,
        interval
    ]);

    const title = useMemo(() => {
        switch (type) {
            case 'SHARE_PERCENT':
                return {
                    title: 'Price movement alert',
                    description:
                        'Alerts you to a change in your share price that may require attention.'
                };
            case 'SENTIMENT':
                return {
                    title: 'Sentiment alert',
                    description:
                        'Alerts you to any new activities that fall within certain sentiment parameters.'
                };
            case 'ACTIVITY':
                return {
                    title: 'Activity level alert',
                    description:
                        'Alerts you when the activity count changes by certain percentage'
                };
            case 'SEARCH':
                return {
                    title: 'Broad Search Alert',
                    description:
                        'Alerts you to conversations about broad topics or themes happening right now.'
                };
            default:
                return undefined;
        }
    }, [type]);

    const valid = useMemo(() => {
        if (!type) return false;
        if (!emails.length) return false;

        switch (type) {
            case 'SHARE_PERCENT':
                return true;
            case 'SENTIMENT':
                return true;
            case 'ACTIVITY':
                return true;
            case 'SEARCH':
                return searchTerm.length > 0;
            default:
                return false;
        }
    }, [type, emails]);

    // TODO: Probs move this up a level or smth
    async function handleSubmit() {
        function getThreshold(type: AlertField): AlertThreshold {
            if (type === 'SENTIMENT')
                return {
                    comparator: sentimentThreshold?.comparator,
                    threshold: sentimentThreshold?.threshold
                };

            if (type === 'ACTIVITY')
                return {
                    comparator: 'GTE',
                    threshold: activityThreshold?.threshold
                };

            if (type === 'SEARCH')
                return {
                    comparator: 'GTE',
                    threshold: broadSearchThreshold?.threshold
                };

            return {
                comparator: 'GTE',
                threshold: threshold?.threshold
            };
        }

        const thresholdData = getThreshold(type as AlertField);

        await createAlerts([
            {
                threshold: thresholdData?.threshold,
                emails,
                field: type!,
                comparator: thresholdData?.comparator,
                interval: type === 'ACTIVITY' ? interval : null,
                searchTerm: type === 'SEARCH' ? searchTerm : null
            }
        ]);

        setOpen(false);
    }

    return (
        <>
            <div className="relative overflow-hidden flex-1 flex flex-col">
                <Transition show={!type}>
                    <div className={classNames(classes, 'absolute inset-0')}>
                        <div
                            className={classNames(
                                'flex min-h-0 flex-1 flex-col overflow-y-scroll py-6'
                            )}>
                            <div className="px-4 sm:px-6">
                                <DialogTitle>
                                    <h2 className="text-base font-semibold leading-6 text-gray-900">
                                        Create an alert
                                    </h2>
                                    <p className="mt-1 text-sm text-gray-500">
                                        Get started by selecting a template.
                                    </p>
                                </DialogTitle>
                            </div>
                            <div className="relative mt-6 flex-1 px-4 sm:px-6">
                                <AlertTypePicker setType={setType} />
                            </div>
                        </div>
                    </div>
                </Transition>
                <Transition show={!!type}>
                    <div
                        className={classNames(
                            classes,
                            'absolute inset-0 flex flex-col divide-y divide-gray-200'
                        )}>
                        <div
                            className={classNames(
                                'flex min-h-0 flex-1 flex-col overflow-y-scroll py-6'
                            )}>
                            <div className="px-4 sm:px-6">
                                <DialogTitle>
                                    <h2 className="text-base font-semibold leading-6 text-gray-900">
                                        {title?.title}
                                    </h2>
                                    <p className="mt-1 text-sm text-gray-500">
                                        {title?.description}
                                    </p>
                                </DialogTitle>
                            </div>
                            <div className="relative mt-6 flex-1 px-4 sm:px-6">
                                {form}
                                <AlertNotificationConfig
                                    emails={emails}
                                    setEmails={setEmails}
                                />
                            </div>
                        </div>
                        <div
                            className={classNames(
                                classes,
                                'flex flex-shrink-0 justify-end px-4 py-4'
                            )}>
                            <button
                                type="button"
                                onClick={() => setOpen(false)}
                                className="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:ring-gray-400">
                                Cancel
                            </button>
                            <button
                                onClick={handleSubmit}
                                type="button"
                                disabled={!valid}
                                className="ml-4 inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:bg-gray-200">
                                Save
                            </button>
                        </div>
                    </div>
                </Transition>
            </div>
        </>
    );
}
