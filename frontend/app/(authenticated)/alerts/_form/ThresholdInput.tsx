import { CheckIcon } from '@heroicons/react/24/outline';
import { useEffect, useRef } from 'react';
import { <PERSON><PERSON>Field } from '@quarterback/types';
import { AlertThreshold } from '@quarterback/types';

export default function ThresholdInput({
    type,
    min = 0.001,
    max = 100,
    step = 0.001,
    close,
    threshold,
    setThreshold
}: {
    type: AlertField;
    close: () => void;
    threshold: AlertThreshold;
    setThreshold: React.Dispatch<React.SetStateAction<AlertThreshold>>;
    min?: number;
    max?: number;
    step?: number;
}) {
    const ref = useRef<HTMLInputElement>(null);

    useEffect(() => {
        ref.current?.focus();
        ref.current?.select();
    }, []);

    return (
        <form
            action={(data) => {
                setThreshold({
                    ...threshold,
                    threshold: parseFloat(data.get('threshold') as string)
                });
                close();
            }}
            className="flex flex-row items-center gap-x-1.5">
            <div className="relative rounded-md shadow-sm">
                <input
                    ref={ref}
                    id="threshold"
                    name="threshold"
                    type="number"
                    min={min}
                    step={step}
                    max={max}
                    placeholder="0"
                    defaultValue={threshold?.threshold}
                    required
                    className="block w-full max-w-24 rounded-md border-0 py-1.5 pr-6 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
                {type === 'SHARE_PERCENT' || type === 'ACTIVITY' ? (
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <span id="price-currency" className="text-gray-500 sm:text-sm">
                            %
                        </span>
                    </div>
                ) : null}
            </div>
            <button type="submit" className="rounded hover:bg-gray-50 p-1 -m-1">
                <CheckIcon className="size-4" />
            </button>
        </form>
    );
}
