import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import React from 'react';
import ThresholdInput from './ThresholdInput';
import { ActivityAlertInterval } from '@quarterback/types';

export default function ActivityAlertForm({
    threshold,
    setThreshold,
    interval,
    setInterval
}: {
    threshold: {
        comparator: 'GTE' | 'LTE';
        threshold: number;
    };
    setThreshold: React.Dispatch<
        React.SetStateAction<{ comparator: 'GTE' | 'LTE'; threshold: number }>
    >;
    interval: ActivityAlertInterval;
    setInterval: React.Dispatch<React.SetStateAction<ActivityAlertInterval>>;
}) {
    return (
        <>
            <div className="mt-4 bg-gray-50 border rounded-lg p-4 text-sm">
                When total activity count has changed by{' '}
                <Popover as="span" className="relative">
                    <PopoverButton
                        as={'span'}
                        className="inline font-semibold underline decoration-dotted text-indigo-700 cursor-pointer">
                        {threshold?.threshold.toLocaleString()}%
                    </PopoverButton>
                    <PopoverPanel
                        anchor={{
                            to: 'bottom'
                        }}
                        className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                        {({ close }) => (
                            <ThresholdInput
                                type="ACTIVITY"
                                close={close}
                                threshold={threshold}
                                setThreshold={setThreshold}
                                min={0.01}
                                max={100}
                                step={0.01}
                            />
                        )}
                    </PopoverPanel>
                </Popover>{' '}
                compared to previous{' '}
                <select
                    value={interval!}
                    className="w-fit text-sm border rounded-md p-0 pl-2 pr-8 mt-1 mr-2 bg-gray-50 border-gray-300"
                    onChange={(e) => {
                        setInterval(e.target.value as ActivityAlertInterval);
                    }}>
                    <option value={'DAY'}>day</option>
                    <option value="MONTH">month</option>
                    <option value="WEEK">week</option>
                </select>
            </div>
        </>
    );
}
