import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import React from 'react';
import ThresholdInput from './ThresholdInput';

export default function SentimentAlertForm({
    threshold,
    setThreshold
}: {
    threshold: {
        comparator: 'GTE' | 'LTE';
        threshold: number;
    };
    setThreshold: React.Dispatch<
        React.SetStateAction<{ comparator: 'GTE' | 'LTE'; threshold: number }>
    >;
}) {
    return (
        <>
            <div className="mt-4 bg-gray-50 border rounded-lg p-4 text-sm">
                When the new activity is detected with a sentiment that is
                <select
                    value={threshold?.comparator}
                    className="w-fit text-sm border rounded-md p-0 pl-2 pr-8 mt-1 mr-2 bg-gray-50 border-gray-300"
                    onChange={(e) => {
                        setThreshold({
                            ...threshold,
                            comparator: e.target.value as 'GTE' | 'LTE'
                        });
                    }}>
                    <option value="LTE">less than</option>
                    <option value="GTE">more than</option>
                </select>
                <Popover as="span" className="relative">
                    <PopoverButton
                        as={'span'}
                        className="inline font-semibold underline decoration-dotted text-indigo-700 cursor-pointer">
                        {threshold?.threshold.toLocaleString()}
                    </PopoverButton>
                    <PopoverPanel
                        anchor={{
                            to: 'bottom'
                        }}
                        className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                        {({ close }) => (
                            <ThresholdInput
                                type="SENTIMENT"
                                close={close}
                                threshold={threshold}
                                setThreshold={setThreshold}
                                min={-1}
                                max={1}
                            />
                        )}
                    </PopoverPanel>
                </Popover>
            </div>
        </>
    );
}
