import React from 'react';
import { AlertThreshold, searchTermRelevanceText } from '@quarterback/types';

export default function SearchAlertForm({
    threshold,
    setThreshold,
    searchTerm,
    setSearchTerm
}: {
    threshold: AlertThreshold;
    searchTerm: string;
    setThreshold: React.Dispatch<React.SetStateAction<AlertThreshold>>;
    setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
}) {
    return (
        <>
            <div className="mt-4 bg-gray-50 border rounded-lg p-4 text-sm">
                <select
                    className="w-fit text-sm border rounded-md p-0 pl-2 pr-8 bg-gray-50 border-gray-300 mb-2"
                    onChange={(e) => {
                        setThreshold({
                            ...threshold,
                            threshold: parseFloat(e.target.value)
                        });
                    }}
                    value={threshold?.threshold}>
                    <option value="0.48" className="capitalize">
                        <span className="capitalize">
                            {searchTermRelevanceText.get(0.48)}{' '}
                        </span>
                        relevant
                    </option>
                    <option value="0.41" className="capitalize">
                        <span className="capitalize">
                            {searchTermRelevanceText.get(0.41)} relevant
                        </span>
                    </option>
                    <option value="0.3">
                        <span className="capitalize">
                            {searchTermRelevanceText.get(0.3)} relevant
                        </span>
                    </option>
                </select>{' '}
                chatter or broadcast about{' '}
                <input
                    id="searchTerm"
                    name="searchTerm"
                    type="text"
                    placeholder="e.g. Acquisition"
                    value={searchTerm}
                    onChange={(event) => setSearchTerm(event.target.value)}
                    className="block w-full rounded rounded-l-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                />
            </div>
        </>
    );
}
