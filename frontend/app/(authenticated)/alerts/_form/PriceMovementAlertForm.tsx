import React, { SetStateAction, useEffect, useRef, useState } from 'react';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';
import ThresholdInput from './ThresholdInput';
import { AlertThreshold } from '../../../../../types/src/Alert';

export default function PriceMovementAlertForm({
    threshold,
    setThreshold
}: {
    threshold: AlertThreshold;
    setThreshold: React.Dispatch<SetStateAction<AlertThreshold>>;
}) {
    return (
        <>
            <div className="mt-4 bg-gray-50 border rounded-lg p-4 text-sm">
                When share price has moved{' '}
                <Popover as="span" className="relative">
                    <PopoverButton
                        as={'span'}
                        className="inline font-semibold underline decoration-dotted text-indigo-700 cursor-pointer">
                        {threshold?.threshold.toLocaleString()}%
                    </PopoverButton>
                    <PopoverPanel
                        anchor={{
                            to: 'bottom'
                        }}
                        className="absolute bg-white py-1.5 px-2 shadow rounded-lg">
                        {({ close }) => (
                            <ThresholdInput
                                type="SHARE_PERCENT"
                                close={close}
                                threshold={threshold}
                                setThreshold={setThreshold}
                            />
                        )}
                    </PopoverPanel>
                </Popover>{' '}
                compared to price at open
            </div>
        </>
    );
}
