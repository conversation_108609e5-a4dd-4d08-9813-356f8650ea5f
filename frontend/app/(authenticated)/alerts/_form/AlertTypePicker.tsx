import AlertFieldIcon from '@/app/(authenticated)/alerts/AlertFieldIcon';
import { ChevronRightIcon } from '@heroicons/react/16/solid';
import { <PERSON><PERSON>Field } from '@quarterback/types';
import classNames from 'classnames';
import React from 'react';

export default function AlertTypePicker({
    setType
}: {
    setType: React.Dispatch<React.SetStateAction<AlertField | undefined>>;
}) {
    const items: Array<{
        name: string;
        description: string;
        field: AlertField;
        disabled: boolean;
    }> = [
        {
            name: 'Price Movement',
            description:
                'Alerts you to a change in your share price that may require attention.',
            field: 'SHARE_PERCENT',
            disabled: false
        },
        {
            name: 'Broad Search',
            description:
                'Alerts you to conversations about broad topics or themes happening right now.',
            field: 'SEARCH',
            disabled: false
        },
        {
            name: 'Sentiment',
            description:
                'Alerts you to any new activities that fall within certain sentiment parameters.',
            field: 'SENTIMENT',
            disabled: false
        },
        {
            name: 'Activity Level',
            description:
                'Alerts when activity volume changes by type, category or across the board.',
            field: 'ACTIVITY',
            disabled: false
        }
    ];

    return (
        <div>
            <ul role="list" className="divide-y divide-gray-200 -mx-4">
                {items.map((item) => (
                    <li
                        key={item.field}
                        onClick={() => {
                            if (!item.disabled) {
                                setType(item.field);
                            }
                        }}
                        className={classNames('px-4', {
                            'opacity-40': item.disabled,
                            'cursor-pointer hover:bg-gray-50': !item.disabled
                        })}>
                        <div className="group relative flex items-start space-x-3 py-4">
                            <div className="flex-shrink-0">
                                <AlertFieldIcon field={item.field} />
                            </div>
                            <div className="min-w-0 flex-1">
                                <div className="flex flex-row gap-x-1 items-center">
                                    <div className="text-sm font-medium text-gray-900">
                                        <span
                                            aria-hidden="true"
                                            className="absolute inset-0"
                                        />
                                        {item.name}
                                    </div>
                                    {item.disabled ? (
                                        <span className="inline-flex items-center rounded-full bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-800 ring-1 ring-inset ring-yellow-600/20">
                                            Coming soon
                                        </span>
                                    ) : undefined}
                                </div>
                                <p className="text-sm text-gray-500">
                                    {item.description}
                                </p>
                            </div>
                            <div className="flex-shrink-0 self-center">
                                <ChevronRightIcon
                                    aria-hidden="true"
                                    className={classNames('h-5 w-5 text-gray-400', {
                                        'group-hover:text-gray-500': !item.disabled
                                    })}
                                />
                            </div>
                        </div>
                    </li>
                ))}
            </ul>
        </div>
    );
}
