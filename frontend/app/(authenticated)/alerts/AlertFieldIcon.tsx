import { ArrowUpIcon } from '@heroicons/react/16/solid';
import {
    ExclamationCircleIcon,
    FaceFrownIcon,
    MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { <PERSON><PERSON><PERSON>ield } from '@quarterback/types';
import classNames from 'classnames';
import { useMemo } from 'react';

export default function AlertFieldIcon({ field }: { field: AlertField }) {
    const color = useMemo(() => {
        switch (field) {
            case 'SHARE_PERCENT':
                return 'bg-pink-500';
            case 'SENTIMENT':
                return 'bg-blue-500';
            case 'SEARCH':
                return 'bg-purple-500';
            case 'ACTIVITY':
                return 'bg-green-500';
        }
    }, [field]);

    const Icon = useMemo(() => {
        switch (field) {
            case 'SHARE_PERCENT':
                return ExclamationCircleIcon;
            case 'SENTIMENT':
                return FaceFrownIcon;
            case 'SEARCH':
                return MagnifyingGlassIcon;
            case 'ACTIVITY':
                return ArrowUpIcon;
        }
    }, [field]);

    return (
        <span
            className={classNames(
                color,
                'inline-flex h-8 w-8 items-center justify-center rounded-lg'
            )}>
            <Icon aria-hidden="true" className="h-5 w-5 text-white" />
        </span>
    );
}
