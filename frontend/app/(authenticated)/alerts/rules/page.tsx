'use client';

import React from 'react';
import { useOrganisation } from '@/components/OrganisationProvider';
import useAlertRules from '@/api/hooks/useAlertRules';
import AlertRuleRow from '@/app/(authenticated)/alerts/rules/AlertRuleRow';
import { BellIcon } from '@heroicons/react/24/outline';

export default function AlertRulesPage() {
    const organisation = useOrganisation();
    const { data: alerts = [] } = useAlertRules(
        organisation?.selected?.organisation,
        organisation?.selected?.entity
    );

    return (
        <>
            <div className="p-4">
                {alerts.length ? (
                    <ul role="list" className="divide-y divide-gray-100">
                        {alerts.map((alert) => (
                            <AlertRuleRow key={alert.id} alert={alert} />
                        ))}
                    </ul>
                ) : (
                    <div className="text-center my-16">
                        <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-semibold text-gray-900">
                            You don&rsquo;t have any alert rules
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                            Get started by creating a new alert rule.
                        </p>
                    </div>
                )}
            </div>
        </>
    );
}
