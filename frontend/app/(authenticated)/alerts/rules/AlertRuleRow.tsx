import useAlertRulesMutation from '@/api/hooks/mutations/useAlertRulesMutation';
import AlertFieldIcon from '@/app/(authenticated)/alerts/AlertFieldIcon';
import {
    Dialog,
    DialogBackdrop,
    DialogPanel,
    DialogTitle,
    Menu,
    MenuButton,
    MenuItem,
    MenuItems
} from '@headlessui/react';
import { EllipsisVerticalIcon } from '@heroicons/react/20/solid';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { AlertConfig, AlertField, searchTermRelevanceText } from '@quarterback/types';
import React, { useMemo, useState } from 'react';

function Confirmation({
    open,
    setOpen,
    title,
    message,
    button,
    action
}: {
    title: string;
    message: string;
    button: string;
    action: () => void;
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) {
    return (
        <Dialog open={open} onClose={setOpen} className="relative z-10">
            <DialogBackdrop
                transition
                className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
            />

            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <DialogPanel
                        transition
                        className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-lg sm:p-6 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95">
                        <div className="sm:flex sm:items-start">
                            <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <ExclamationTriangleIcon
                                    aria-hidden="true"
                                    className="h-6 w-6 text-red-600"
                                />
                            </div>
                            <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                                <DialogTitle
                                    as="h3"
                                    className="text-base font-semibold leading-6 text-gray-900">
                                    {title}
                                </DialogTitle>
                                <div className="mt-2">
                                    <p className="text-sm text-gray-500">{message}</p>
                                </div>
                            </div>
                        </div>
                        <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                            <button
                                type="button"
                                onClick={() => action()}
                                className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto">
                                {button}
                            </button>
                            <button
                                type="button"
                                data-autofocus={true}
                                onClick={() => setOpen(false)}
                                className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                                Cancel
                            </button>
                        </div>
                    </DialogPanel>
                </div>
            </div>
        </Dialog>
    );
}

export default function AlertRuleRow({ alert }: { alert: AlertConfig }) {
    const [deleting, setDeleting] = useState(false);

    const { delete: deleteAlerts } = useAlertRulesMutation(
        { id: alert.organisation },
        {
            symbol: alert.symbol,
            exchange: alert.exchange
        }
    );

    const emails = useMemo(() => {
        if (!alert.emails.length) {
            return <>no one</>;
        } else if (alert.emails.length === 1) {
            return <>{alert.emails[0]}</>;
        } else {
            return (
                <>
                    {alert.emails[0]} and{' '}
                    <span
                        className="underline decoration-dotted cursor-help"
                        title={alert.emails.slice(1).join('\n')}>
                        {alert.emails.length - 1} more
                    </span>
                </>
            );
        }
    }, [alert]);

    const description = useMemo(() => {
        switch (alert.field) {
            case 'SHARE_PERCENT':
                return `When share price moves more than ${alert.threshold}%`;
            case 'SENTIMENT':
                return `When sentiment is ${alert.comparator === 'LTE' ? 'less than' : 'more than'} ${alert.threshold}`;
            case 'ACTIVITY':
                return `When activity is ${alert.threshold}% more than previous ${alert.interval?.toLowerCase()}`;
            case 'SEARCH':
                return `When activity is ${searchTermRelevanceText?.get(alert.threshold)} relevant to ${alert.searchTerm}`;
            default:
                return undefined;
        }
    }, [
        alert.comparator,
        alert.field,
        alert.interval,
        alert.searchTerm,
        alert.threshold
    ]);

    const titile = useMemo(() => {
        switch (alert.field) {
            case 'SHARE_PERCENT':
                return `Price Movement Alert`;
            case 'SENTIMENT':
                return `Sentiment Alert`;
            case 'ACTIVITY':
                return `Activity Level Alert`;
            case 'SEARCH':
                return `Broad Search Alert`;
            default:
                return undefined;
        }
    }, [alert]);

    async function handleDelete() {
        if (alert.id) {
            await deleteAlerts(new URLSearchParams({ id: alert.id }));
            setDeleting(false);
        }
    }

    return (
        <li key={alert.id} className="flex items-center justify-between gap-x-6 py-5">
            <div className="flex min-w-0 gap-x-4">
                <AlertFieldIcon field={alert.field as AlertField} />
                <div className="min-w-0 flex-auto">
                    <p className="text-sm font-semibold leading-6 text-gray-900">
                        {titile}
                    </p>
                    <p className="truncate text-xs leading-5 text-gray-500">
                        {description}, send an alert to {emails}
                    </p>
                    {/*<p className="mt-1 leading-5 text-xs text-gray-400">*/}
                    {/*    Last triggered <span className="font-medium">never</span>*/}
                    {/*</p>*/}
                </div>
            </div>
            <Menu as="div" className="relative flex-none">
                <MenuButton className="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
                    <span className="sr-only">Open options</span>
                    <EllipsisVerticalIcon aria-hidden="true" className="h-5 w-5" />
                </MenuButton>
                <MenuItems
                    transition
                    className="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in">
                    {/*<MenuItem>*/}
                    {/*    <a*/}
                    {/*        href="#"*/}
                    {/*        className="block px-3 py-1 text-sm leading-6 text-gray-900 data-[focus]:bg-gray-50">*/}
                    {/*        Edit*/}
                    {/*    </a>*/}
                    {/*</MenuItem>*/}
                    <MenuItem>
                        <a
                            href="#"
                            onClick={() => setDeleting(true)}
                            className="block px-3 py-1 text-sm leading-6 text-gray-900 data-[focus]:bg-gray-50">
                            Delete
                        </a>
                    </MenuItem>
                </MenuItems>
            </Menu>
            <Confirmation
                open={deleting}
                setOpen={setDeleting}
                title="Delete alert"
                message="Are you sure you want to delete this alert? This action cannot be undone."
                button="Delete"
                action={handleDelete}
            />
        </li>
    );
}
