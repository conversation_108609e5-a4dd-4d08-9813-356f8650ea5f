import { Activity, ListedEntity, Organisation, OrganisationUser, User } from '@quarterback/types';
import { useUsers } from '@/api/hooks/admin/useUsers';
import useEntities from '@/api/hooks/admin/useEntities';
import React, { FormEvent, useMemo, useState } from 'react';
import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions,
    Label
} from '@headlessui/react';
import { ChevronUpDownIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { CheckIcon } from '@heroicons/react/20/solid';
import useOrganisationsMutation from '@/api/hooks/admin/mutations/useOrganisationsMutation';

function AddUserRow({
    user,
    setUser,
    email
}: {
    user: OrganisationUser;
    email: string;
    setUser: (user: OrganisationUser) => void;
}) {
    const { data: entities = [] } = useEntities();

    const [entityQuery, setEntityQuery] = useState('');

    const filteredEntities = useMemo(() => {
        return entities.filter(
            (it) => !entityQuery || it.name.toLowerCase().includes(entityQuery.toLowerCase())
        );
    }, [entityQuery, entities]);

    return (
        <tr>
            <td className="whitespace-nowrap py-2 pl-4 pr-3 text-sm text-gray-500 sm:pl-0">
                {email}
            </td>
            <td className="whitespace-nowrap px-2 py-2 text-sm font-medium text-gray-900 flex items-center justify-between">
                <Combobox
                    as="div"
                    multiple
                    by={(a, b) => a.symbol === b.symbol && a.exchange === b.exchange}
                    value={user.entities}
                    className="w-full"
                    onChange={(entities) => {
                        setEntityQuery('');
                        setUser({ ...user, entities });
                    }}>
                    <div className="relative">
                        <ComboboxInput
                            className="w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            onChange={(event) => setEntityQuery(event.target.value)}
                            onBlur={() => setEntityQuery('')}
                            displayValue={(entities: Array<ListedEntity>) =>
                                entities.map((it) => it.name).join(', ')
                            }
                        />
                        <ComboboxButton className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                            <ChevronUpDownIcon
                                className="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                            />
                        </ComboboxButton>

                        {filteredEntities.length > 0 && (
                            <ComboboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                {filteredEntities.map((entity) => (
                                    <ComboboxOption
                                        key={`${entity.symbol}:${entity.exchange}`}
                                        value={entity}
                                        className={({ focus }) =>
                                            classNames(
                                                'relative cursor-default select-none py-2 pl-3 pr-9',
                                                focus ? 'bg-indigo-600 text-white' : 'text-gray-900'
                                            )
                                        }>
                                        {({ focus, selected }) => (
                                            <>
                                                <span
                                                    className={classNames(
                                                        'block truncate',
                                                        selected && 'font-semibold'
                                                    )}>
                                                    {entity.name}
                                                </span>

                                                {selected && (
                                                    <span
                                                        className={classNames(
                                                            'absolute inset-y-0 right-0 flex items-center pr-4',
                                                            focus ? 'text-white' : 'text-indigo-600'
                                                        )}>
                                                        <CheckIcon
                                                            className="h-5 w-5"
                                                            aria-hidden="true"
                                                        />
                                                    </span>
                                                )}
                                            </>
                                        )}
                                    </ComboboxOption>
                                ))}
                            </ComboboxOptions>
                        )}
                    </div>
                </Combobox>
            </td>
        </tr>
    );
}

function AddUsers({
    users,
    setUsers
}: {
    users: Array<OrganisationUser>;
    setUsers: React.Dispatch<React.SetStateAction<Array<OrganisationUser>>>;
}) {
    const { data: userChoices = [] } = useUsers();

    const [userQuery, setUserQuery] = useState('');
    const [selectedUser, setSelectedUser] = useState<User | null>(null);

    const filteredUsers = useMemo(() => {
        return userChoices.filter(
            (it) =>
                (!userQuery || it.emails[0].toLowerCase().includes(userQuery.toLowerCase())) &&
                !users.map((user) => user.id).includes(it.id)
        );
    }, [userQuery, userChoices, users]);

    function addUser() {
        setUsers((users) => {
            return [...users, { id: selectedUser!.id!, entities: [] }];
        });

        setUserQuery('');
        setSelectedUser(null);
    }

    return (
        <div>
            <table className="min-w-full divide-y divide-gray-300">
                <thead>
                    <tr>
                        <th
                            scope="col"
                            className="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                            User
                        </th>
                        <th
                            scope="col"
                            className="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900">
                            Entities
                        </th>
                    </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                    {users.map((user) => (
                        <AddUserRow
                            key={user.id}
                            user={user}
                            email={
                                userChoices.find((it) => it.id === user.id)?.emails[0] ?? user.id
                            }
                            setUser={(user) => {
                                setUsers((users) => {
                                    return users.map((it) => {
                                        if (it.id === user.id) {
                                            return user;
                                        } else {
                                            return it;
                                        }
                                    });
                                });
                            }}
                        />
                    ))}
                </tbody>
            </table>
            <div className="mt-4">
                <label className="block text-sm font-medium leading-6 text-gray-900">
                    Add user
                </label>
            </div>
            <div className="flex items-center gap-x-2 mt-2">
                <Combobox
                    as="div"
                    by={(a, b) => a?.emails[0] === b?.emails[0]}
                    value={selectedUser}
                    className="flex-1"
                    onChange={(user) => {
                        setUserQuery('');
                        setSelectedUser(user);
                    }}>
                    <div className="relative">
                        <ComboboxInput
                            className="w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            onChange={(event) => setUserQuery(event.target.value)}
                            onBlur={() => setUserQuery('')}
                            displayValue={(user: User | null) => user?.emails[0] ?? ''}
                        />
                        <ComboboxButton className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                            <ChevronUpDownIcon
                                className="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                            />
                        </ComboboxButton>

                        {filteredUsers.length > 0 && (
                            <ComboboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                {filteredUsers.map((user) => (
                                    <ComboboxOption
                                        key={user.emails[0]}
                                        value={user}
                                        className={({ focus }) =>
                                            classNames(
                                                'relative cursor-default select-none py-2 pl-3 pr-9',
                                                focus ? 'bg-indigo-600 text-white' : 'text-gray-900'
                                            )
                                        }>
                                        {({ focus, selected }) => (
                                            <>
                                                <span
                                                    className={classNames(
                                                        'block truncate',
                                                        selected && 'font-semibold'
                                                    )}>
                                                    {user.emails[0]}
                                                </span>

                                                {selected && (
                                                    <span
                                                        className={classNames(
                                                            'absolute inset-y-0 right-0 flex items-center pr-4',
                                                            focus ? 'text-white' : 'text-indigo-600'
                                                        )}>
                                                        <CheckIcon
                                                            className="h-5 w-5"
                                                            aria-hidden="true"
                                                        />
                                                    </span>
                                                )}
                                            </>
                                        )}
                                    </ComboboxOption>
                                ))}
                            </ComboboxOptions>
                        )}
                    </div>
                </Combobox>
                <button
                    type="button"
                    className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                    disabled={!selectedUser}
                    onClick={addUser}>
                    Add
                </button>
            </div>
        </div>
    );
}

export default function AddOrganisationForm({
    organisation,
    onSubmit
}: {
    organisation?: Organisation;
    onSubmit: () => void;
}) {
    const { trigger, isMutating } = useOrganisationsMutation();

    const [name, setName] = useState(organisation?.name ?? '');
    const [users, setUsers] = useState(organisation?.users ?? []);

    async function handleSubmit(event: FormEvent) {
        event.preventDefault();

        await trigger([
            {
                id: organisation?.id,
                name,
                users
            }
        ]);

        onSubmit();
    }

    return (
        <form id="add-entity" onSubmit={handleSubmit}>
            <div className="space-y-12">
                <div className="border-b border-gray-900/10 pb-12">
                    <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                        <div className="sm:col-span-full">
                            <label
                                htmlFor="name"
                                className="block text-sm font-medium leading-6 text-gray-900">
                                Name*
                            </label>
                            <div className="mt-2">
                                <input
                                    type="text"
                                    name="name"
                                    id="name"
                                    placeholder="e.g. Veem Pty Ltd"
                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    required
                                />
                            </div>
                        </div>
                        <div className="col-span-full">
                            <AddUsers users={users} setUsers={setUsers} />
                        </div>
                    </div>
                </div>
            </div>
        </form>
    );
}
