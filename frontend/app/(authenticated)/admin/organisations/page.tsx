'use client';

import React, { useMemo, useState } from 'react';

import { Organisation } from '@quarterback/types';
import useOrganisations from '@/api/hooks/admin/useOrganisations';
import AddOrganisationModal from '@/app/(authenticated)/admin/organisations/AddOrganisationModal';

function OrganisationRow({
    organisation,
    onEdit
}: {
    organisation: Organisation;
    onEdit: () => void;
}) {
    const entities = useMemo(() => {
        return Array.from(
            new Set(
                organisation.users
                    ?.flatMap((it) => it.entities)
                    ?.map((it) => `${it.symbol}:${it.exchange}`)
            )
        );
    }, [organisation]);

    return (
        <tr key={`${organisation.id}`}>
            <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                {organisation.name}
            </td>
            <td className="px-3 py-4 text-sm text-gray-500">{entities.join(', ')}</td>
            <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                <button onClick={onEdit} className="text-indigo-600 hover:text-indigo-900">
                    Edit
                    <span className="sr-only">, {organisation.name}</span>
                </button>
            </td>
        </tr>
    );
}

export default function Organisations() {
    const { data: organisations } = useOrganisations();
    const [modalOpen, setModalOpen] = useState(false);
    const [editing, setEditing] = useState<Organisation | undefined>(undefined);

    return (
        <div className="p-4 sm:p-6 lg:p-8">
            <div className="sm:flex sm:items-center">
                <div className="sm:flex-auto">
                    <h1 className="text-base font-semibold leading-6 text-gray-900">
                        Organisations
                    </h1>
                    <p className="mt-2 text-sm text-gray-700">
                        User account organisations and connected entities
                    </p>
                </div>
                <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                    <button
                        type="button"
                        onClick={() => {
                            setEditing(undefined);
                            setModalOpen(true);
                        }}
                        className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                        Add organisation
                    </button>
                </div>
            </div>
            <AddOrganisationModal open={modalOpen} setOpen={setModalOpen} organisation={editing} />
            <div className="mt-8 flow-root">
                <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <table className="min-w-full divide-y divide-gray-300">
                            <thead>
                                <tr>
                                    <th
                                        scope="col"
                                        className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">
                                        Name
                                    </th>
                                    <th
                                        scope="col"
                                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                                        Entities
                                    </th>
                                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-0">
                                        <span className="sr-only">Edit</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                                {(organisations ?? []).map((organisation) => (
                                    <OrganisationRow
                                        key={organisation.id}
                                        organisation={organisation}
                                        onEdit={() => {
                                            setEditing(organisation);
                                            setModalOpen(true);
                                        }}
                                    />
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}
