import { Activity2 } from '@quarterback/types';
import { ChevronRightIcon } from '@heroicons/react/16/solid';
import React from 'react';
import ActivityCard from '@/components/cards/ActivityCard';

export default function Preview({
    activities,
    onSubmit: handleSubmit,
    loading
}: {
    activities: Array<Activity2>;
    onSubmit: () => void;
    loading: boolean;
}) {
    return (
        <div>
            <div className="my-4 sm:flex sm:items-center">
                <div className="sm:flex-auto">
                    <h1 className="text-base font-semibold leading-6 text-gray-900">
                        2. Preview
                    </h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Double check the activities below are correct. Pay close attention
                        to dates, in particular the year.
                    </p>
                </div>
            </div>
            <div className="mt-2">
                {activities.map((activity, index) => (
                    <ActivityCard key={index} activity={activity} />
                ))}
            </div>
            <button
                type="button"
                onClick={handleSubmit}
                disabled={loading}
                className="mt-2 w-full flex flex-row items-center justify-center gap-x-2 rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                {loading ? (
                    <>
                        <div
                            className="inline-block size-4 animate-spin rounded-full border-2 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] text-white"
                            role="status">
                            <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                                Loading...
                            </span>
                        </div>
                    </>
                ) : (
                    <>
                        <span>Upload</span>
                        <ChevronRightIcon className="size-4" />
                    </>
                )}
            </button>
        </div>
    );
}
