'use client';

import React, { useCallback, useState } from 'react';
import { Activity2 } from '@quarterback/types';
import Upload from './_form/Upload';
import Preview from '@/app/(authenticated)/admin/news/import/_form/Preview';
import { Transition } from '@headlessui/react';
import useActivitiesMutation from '@/api/hooks/admin/mutations/useActivitiesMutation';

// Fucking lmao
// @ts-ignore
globalThis['setImmediate'] = function (func, ...args) {
    const func_bound = func.bind(this, ...args);
    setTimeout(func_bound, 0);
};

export default function NewsImport() {
    const [activities, setActivities] = useState<Array<Activity2>>([]);
    const activitiesMutation = useActivitiesMutation();

    const handleSubmit = useCallback(async () => {
        try {
            await activitiesMutation.trigger(activities);
            setActivities([]);
        } catch (err) {
            //
        }
    }, [activities, activitiesMutation]);

    return (
        <div className="relative w-full">
            <Transition show={!activities.length}>
                <div className="absolute inset-0">
                    <Upload setActivities={setActivities} />
                </div>
            </Transition>
            <Transition show={!!activities.length}>
                <div className="absolute inset-0">
                    <Preview
                        activities={activities}
                        loading={activitiesMutation.isMutating}
                        onSubmit={handleSubmit}
                    />
                </div>
            </Transition>
        </div>
    );
}
