'use client';

import Link from 'next/link';
import classNames from 'classnames';
import { PlusIcon } from '@heroicons/react/20/solid';
import Drawer from '@/components/ui/Drawer';
import AlertForm from '@/app/(authenticated)/alerts/_form/AlertForm';
import React from 'react';
import { usePathname } from 'next/navigation';

export default function News({ children }: React.PropsWithChildren) {
    const tabs = [
        // { name: 'Query', href: '/admin/news/query' },
        { name: 'Import', href: '/admin/news/import' }
    ];

    const pathname = usePathname();

    return (
        <div className="p-4">
            <div className="border-b border-gray-200">
                <div className="sm:flex sm:items-baseline">
                    <h3 className="text-base font-semibold leading-6 text-gray-900">
                        News
                    </h3>
                    <div className="grow mt-4 sm:ml-10 sm:mt-0">
                        <nav className="-mb-px flex space-x-8">
                            {tabs.map((tab) => (
                                <Link
                                    key={tab.name}
                                    href={tab.href}
                                    className={classNames(
                                        pathname.startsWith(tab.href)
                                            ? 'border-indigo-500 text-indigo-600'
                                            : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                                        'whitespace-nowrap border-b-2 px-1 pb-4 text-sm font-medium'
                                    )}>
                                    {tab.name}
                                </Link>
                            ))}
                        </nav>
                    </div>
                    {/*<div className="flex flex-row items-center gap-x-1.5">*/}
                    {/*    <button*/}
                    {/*        type="button"*/}
                    {/*        className="inline-flex items-center gap-x-1.5 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">*/}
                    {/*        <PlusIcon aria-hidden="true" className="-ml-0.5 h-5 w-5" />*/}
                    {/*        Alert rule*/}
                    {/*    </button>*/}
                    {/*</div>*/}
                </div>
            </div>

            {children}
        </div>
    );
}
