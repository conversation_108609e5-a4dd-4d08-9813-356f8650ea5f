'use client';

import useNewsCatcherQuery from '@/api/hooks/admin/mutations/useNewsCatcherQuery';
import React from 'react';

export default function NewsQuery() {
    const query = useNewsCatcherQuery();

    return (
        <div>
            <div className="my-4 sm:flex sm:items-center">
                <div className="sm:flex-auto">
                    <h1 className="text-base font-semibold leading-6 text-gray-900">
                        News Query Builder
                    </h1>
                    <p className="mt-2 text-sm text-gray-700">
                        Test news query output before setting up a companies settings
                    </p>
                </div>
            </div>
            <div className="mt-2">
                <div className="isolate -space-y-px rounded-md shadow-sm">
                    <div className="relative rounded-md rounded-b-none px-3 pb-1.5 pt-2.5 ring-1 ring-inset ring-gray-300 focus-within:z-10 focus-within:ring-2 focus-within:ring-indigo-600">
                        <label
                            htmlFor="name"
                            className="block text-xs font-medium text-gray-900">
                            q=
                        </label>
                        <input
                            id="name"
                            name="name"
                            type="text"
                            placeholder="<PERSON> Smith"
                            className="block w-full border-0 p-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm/6"
                        />
                    </div>
                    <div className="relative px-3 pb-1.5 pt-2.5 ring-1 ring-inset ring-gray-300 focus-within:z-10 focus-within:ring-2 focus-within:ring-indigo-600">
                        <label
                            htmlFor="job-title"
                            className="block text-xs font-medium text-gray-900">
                            lang=
                        </label>
                        <input
                            id="job-title"
                            name="job-title"
                            type="text"
                            placeholder="Head of Tomfoolery"
                            className="block w-full border-0 p-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm/6"
                        />
                    </div>
                    <div className="relative rounded-md rounded-t-none px-3 pb-1.5 pt-2.5 ring-1 ring-inset ring-gray-300 focus-within:z-10 focus-within:ring-2 focus-within:ring-indigo-600">
                        <label
                            htmlFor="job-title"
                            className="block text-xs font-medium text-gray-900">
                            ORG_entity_name=
                        </label>
                        <input
                            id="job-title"
                            name="job-title"
                            type="text"
                            placeholder="Head of Tomfoolery"
                            className="block w-full border-0 p-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm/6"
                        />
                    </div>
                </div>
                <button
                    onClick={async () => {
                        await query.trigger();
                    }}>
                    Go
                </button>
            </div>
        </div>
    );
}
