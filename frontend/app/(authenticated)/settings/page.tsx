'use client';

import React, { FormEvent, useEffect, useState } from 'react';
import { useOrganisation } from '@/components/OrganisationProvider';
import Alert from '@/components/Alert';
import useMailchimpConfigMutation from '@/api/hooks/mutations/useMailchimpConfigMutation';
import useMailchimpConfig from '@/api/hooks/useMailchimpConfig';

function LinkedInIcon({ className }: { className?: string }) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 448 512"
            className={className}>
            <path
                fill="white"
                d="M100.3 448H7.4V148.9h92.9zM53.8 108.1C24.1 108.1 0 83.5 0 53.8a53.8 53.8 0 0 1 107.6 0c0 29.7-24.1 54.3-53.8 54.3zM447.9 448h-92.7V302.4c0-34.7-.7-79.2-48.3-79.2-48.3 0-55.7 37.7-55.7 76.7V448h-92.8V148.9h89.1v40.8h1.3c12.4-23.5 42.7-48.3 87.9-48.3 94 0 111.3 61.9 111.3 142.3V448z"
            />
        </svg>
    );
}

function MailChimpSettings() {
    const organisation = useOrganisation();

    const [error, setError] = useState('');
    const [message, setMessage] = useState('');

    const [apiKey, setAPIKey] = useState('');
    const [listId, setListId] = useState('');

    const { data: mailchimp } = useMailchimpConfig(
        organisation.selected?.organisation,
        organisation.selected?.entity
    );

    const { set } = useMailchimpConfigMutation(
        organisation.selected?.organisation,
        organisation.selected?.entity
    );

    useEffect(() => {
        if (mailchimp && mailchimp.length) {
            const config = mailchimp[0];

            setAPIKey(config.token);
            setListId(config.audience);
        }
    }, [mailchimp]);

    async function handleSubmit(event: FormEvent) {
        event.preventDefault();

        if (apiKey && listId) {
            await set([{ token: apiKey, audience: listId }]);
            setMessage('MailChimp updated successfully');
        } else {
            setError('API key and audience ID must be set');
        }
    }

    return (
        <form onSubmit={handleSubmit} className="md:col-span-2">
            <div className="grid grid-cols-1 gap-x-6 gap-y-8">
                {error || message ? (
                    <div className="col-span-full">
                        {error ? (
                            <Alert
                                status="error"
                                text={error}
                                dismiss={() => setError('')}
                            />
                        ) : null}
                        {message ? (
                            <Alert
                                status="success"
                                text={message}
                                dismiss={() => setMessage('')}
                            />
                        ) : null}
                    </div>
                ) : null}

                <div className="sm:col-span-3">
                    <label
                        htmlFor="first-name"
                        className="block text-sm font-medium leading-6 text-gray-900">
                        API Key
                    </label>
                    <p className="mt-1 text-sm leading-6 text-gray-600">
                        You can find this in MailChimp under Profile → Extras → API Keys.
                    </p>
                    <div className="mt-2">
                        <input
                            id="first-name"
                            name="first-name"
                            type="text"
                            autoComplete="given-name"
                            value={apiKey}
                            onChange={(e) => setAPIKey(e.target.value)}
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div className="sm:col-span-3">
                    <label
                        htmlFor="last-name"
                        className="block text-sm font-medium leading-6">
                        Unique Audience ID
                    </label>
                    <p className="mt-1 text-sm leading-6 text-gray-600">
                        You can find this in MailChimp under Audience → Manage → Settings
                        → Unique Audience ID.
                    </p>
                    <div className="mt-2">
                        <input
                            id="last-name"
                            name="last-name"
                            type="text"
                            autoComplete="family-name"
                            value={listId}
                            onChange={(e) => setListId(e.target.value)}
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
            </div>

            <div className="mt-8 flex">
                <button
                    type="submit"
                    className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                    Save
                </button>
            </div>
        </form>
    );
}

export default function Settings() {
    const organisation = useOrganisation();

    return (
        <div>
            <div className="p-4">
                <h2 className="text-lg font-semibold leading-7 text-gray-900 sm:truncate sm:text-xl sm:tracking-tight">
                    {organisation.selected?.entity?.name ?? ''}
                </h2>
            </div>

            {/* Settings forms */}
            <div className="divide-y divide-white/5 -mt-12">
                <div className="grid max-w-7xl grid-cols-1 gap-x-8 gap-y-10 px-4 py-16 sm:px-6 md:grid-cols-3 lg:px-8">
                    <div>
                        <h2 className="text-base font-semibold leading-7">MailChimp</h2>
                        <p className="mt-1 text-sm leading-6 text-gray-500">
                            Connect your MailChimp account to track your mailing list
                            subscribers over time. This will be visible across your
                            organisation.
                        </p>
                    </div>

                    <div className="md:col-span-2 sm:max-w-xl">
                        <MailChimpSettings />
                    </div>
                </div>
            </div>
        </div>
    );
}
