import {
    TwitterFormData,
    TwitterFormData as TwitterFormSchema
} from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';

interface TwitterFormProps {
    formData: TwitterFormData;
    setFormData: (data: TwitterFormData) => void;
}

export default function TwitterForm({ formData, setFormData }: TwitterFormProps) {
    const { getFieldError, validateField } = useFormValidation(
        TwitterFormSchema,
        formData
    );

    const updateField = useCallback(
        (field: keyof TwitterFormData, value: string | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);
            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof TwitterFormData) => {
            validateField(field);
        },
        [validateField]
    );

    return (
        <div className="space-y-4 mt-4">
            <TextArea
                label="Tweet Content"
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                onBlur={() => handleBlur('url')}
                placeholder="https://twitter.com/username/status/123456789"
                error={getFieldError('url')}
            />

            <TextField
                label="Image URL"
                value={formData.image || ''}
                onChange={(value) => updateField('image', value)}
                onBlur={() => handleBlur('image')}
                placeholder="https://example.com/image.jpg"
                error={getFieldError('image')}
            />

            <TextField
                label="Likes"
                value={formData.likes || ''}
                onChange={(value) => updateField('likes', value)}
                onBlur={() => handleBlur('likes')}
                placeholder="0"
                type="number"
                error={getFieldError('likes')}
            />

            <TextField
                label="views"
                value={formData.views || ''}
                onChange={(value) => updateField('views', value)}
                onBlur={() => handleBlur('views')}
                placeholder="0"
                type="number"
                error={getFieldError('views')}
            />
        </div>
    );
}
