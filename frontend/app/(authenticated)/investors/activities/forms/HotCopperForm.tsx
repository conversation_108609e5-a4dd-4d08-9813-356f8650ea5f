import {
    HotCopperFormData,
    HotCopperFormData as HotCopperFormSchema
} from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';
import { SelectField } from '@/components/forms/fields/SelectField';

interface HotCopperFormProps {
    formData: HotCopperFormData;
    setFormData: (data: HotCopperFormData) => void;
}

export default function HotCopperForm({ formData, setFormData }: HotCopperFormProps) {
    const { getFieldError, validateField } = useFormValidation(
        HotCopperFormSchema,
        formData
    );

    const updateField = useCallback(
        (field: keyof HotCopperFormData, value: string | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);

            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof HotCopperFormData) => {
            validateField(field);
        },
        [validateField]
    );

    const sentimentOptions = [
        { value: 'BUY', label: 'Buy' },
        { value: 'HOLD', label: 'Hold' },
        { value: 'SELL', label: 'Sell' },
        { value: 'NONE', label: 'None' }
    ];

    const disclosureOptions = [
        { value: 'HELD', label: 'Held' },
        { value: 'NOT_HELD', label: 'Not Held' },
        { value: 'UNDISCLOSED', label: 'Undisclosed' }
    ];

    return (
        <div className="space-y-4 mt-4">
            <TextField
                label="Post Title"
                value={formData.title || ''}
                onChange={(value) => updateField('title', value)}
                onBlur={() => handleBlur('title')}
                placeholder="Post Title"
                required
                error={getFieldError('title')}
            />

            <TextArea
                label="Post Content"
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                onBlur={() => handleBlur('url')}
                placeholder="https://hotcopper.com.au/threads/..."
                required
                error={getFieldError('url')}
            />

            <TextField
                label="Image URL"
                value={formData.image || ''}
                onChange={(value) => updateField('image', value)}
                onBlur={() => handleBlur('image')}
                placeholder="https://example.com/image.jpg"
                error={getFieldError('image')}
            />

            <TextField
                label="Thread ID"
                value={formData.thread || ''}
                onChange={(value) => updateField('thread', value)}
                onBlur={() => handleBlur('thread')}
                placeholder="Thread ID"
                required
                error={getFieldError('thread')}
            />

            <TextField
                label="Views"
                type="number"
                value={formData.threadViews || ''}
                onChange={(value) => updateField('threadViews', value)}
                onBlur={() => handleBlur('threadViews')}
                placeholder="1000"
                error={getFieldError('threadViews')}
            />

            <SelectField
                label="Sentiment"
                value={formData.sentiment || ''}
                onChange={(value) => updateField('sentiment', value)}
                options={sentimentOptions}
                required
                error={getFieldError('sentiment')}
            />

            <SelectField
                label="Disclosure"
                value={formData.disclosure || ''}
                onChange={(value) => updateField('disclosure', value)}
                options={disclosureOptions}
                required
                error={getFieldError('disclosure')}
            />
        </div>
    );
}
