import { NewsFormData, NewsFormData as NewsFormSchema } from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';

interface NewsFormProps {
    formData: NewsFormData;
    setFormData: (data: NewsFormData) => void;
}

export default function NewsForm({ formData, setFormData }: NewsFormProps) {
    const { getFieldError, validateField } = useFormValidation(NewsFormSchema, formData);

    const updateField = useCallback(
        (field: keyof NewsFormData, value: string | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);
            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof NewsFormData) => {
            validateField(field);
        },
        [validateField]
    );

    return (
        <div className="space-y-4 mt-4">
            <TextField
                label="Title"
                value={formData.title || ''}
                onChange={(value) => updateField('title', value)}
                placeholder="Article Title"
                onBlur={() => handleBlur('title')}
                error={getFieldError('title')}
            />

            <TextArea
                label="Article Content"
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                placeholder="https://example.com/article"
                onBlur={() => handleBlur('url')}
                error={getFieldError('url')}
            />

            <TextField
                label="Image URL"
                value={formData.image || ''}
                onChange={(value) => updateField('image', value)}
                placeholder="https://example.com/image.jpg"
                onBlur={() => handleBlur('image')}
                error={getFieldError('image')}
            />
        </div>
    );
}
