import { RedditFormData, RedditFormData as RedditFormSchema } from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';

interface RedditFormProps {
    formData: RedditFormData;
    setFormData: (data: RedditFormData) => void;
}

export default function RedditForm({ formData, setFormData }: RedditFormProps) {
    const { getFieldError, validateField } = useFormValidation(
        RedditFormSchema,
        formData
    );

    const updateField = useCallback(
        (field: keyof RedditFormData, value: string | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);
            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof RedditFormData) => {
            validateField(field);
        },
        [validateField]
    );
    return (
        <div className="space-y-4 mt-4">
            <TextField
                label="Subreddit"
                value={formData.subreddit || ''}
                onChange={(value) => updateField('subreddit', value)}
                placeholder="r/subreddit"
                onBlur={() => handleBlur('subreddit')}
                required
                error={getFieldError('subreddit')}
            />

            <TextField
                label="Title"
                value={formData.title || ''}
                onChange={(value) => updateField('title', value)}
                placeholder="Post Title"
                onBlur={() => handleBlur('title')}
                error={getFieldError('title')}
            />

            <TextArea
                label="Post Content"
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                onBlur={() => handleBlur('url')}
                placeholder="https://www.reddit.com/r/..."
                error={getFieldError('url')}
            />

            <TextField
                label="Image URL"
                value={formData.image || ''}
                onChange={(value) => updateField('image', value)}
                onBlur={() => handleBlur('image')}
                placeholder="https://example.com/image.jpg"
                error={getFieldError('image')}
            />

            <TextField
                label="Score"
                value={formData.score || ''}
                onChange={(value) => updateField('score', value)}
                onBlur={() => handleBlur('score')}
                placeholder="0"
                type="number"
                error={getFieldError('score')}
            />

            <TextField
                label="Ratio"
                value={formData.ratio || ''}
                onBlur={() => handleBlur('ratio')}
                onChange={(value) => updateField('ratio', value)}
                placeholder="0.95"
                type="number"
                min={0}
                max={1}
                step={0.01}
                error={getFieldError('ratio')}
            />
        </div>
    );
}
