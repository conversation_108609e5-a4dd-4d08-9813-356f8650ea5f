import {
    PinterestFormData,
    PinterestFormData as PinterestFormSchema
} from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';

interface PinterestFormProps {
    formData: PinterestFormData;
    setFormData: (data: PinterestFormData) => void;
}

export default function PinterestForm({ formData, setFormData }: PinterestFormProps) {
    const { getFieldError, validateField } = useFormValidation(
        PinterestFormSchema,
        formData
    );

    const updateField = useCallback(
        (field: keyof PinterestFormData, value: string | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);
            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof PinterestFormData) => {
            validateField(field);
        },
        [validateField]
    );

    return (
        <div className="space-y-4 mt-4">
            <TextField
                label="Title"
                value={formData.title || ''}
                onChange={(value) => updateField('title', value)}
                onBlur={() => handleBlur('title')}
                placeholder="Pin Title"
                error={getFieldError('title')}
            />

            <TextArea
                label="Pin Description"
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                onBlur={() => handleBlur('url')}
                placeholder="https://www.pinterest.com/..."
                error={getFieldError('url')}
            />

            <TextField
                label="Image URL"
                value={formData.image || ''}
                onChange={(value) => updateField('image', value)}
                onBlur={() => handleBlur('image')}
                placeholder="https://example.com/image.jpg"
                error={getFieldError('image')}
            />

            <TextField
                label="Likes"
                value={formData.likes || ''}
                onChange={(value) => updateField('likes', value)}
                onBlur={() => handleBlur('likes')}
                placeholder="0"
                type="number"
                error={getFieldError('likes')}
            />
        </div>
    );
}
