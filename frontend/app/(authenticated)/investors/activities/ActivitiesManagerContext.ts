import { createContext } from 'react';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { Activity } from '@quarterback/types';

const ActivitiesManagerContext = createContext<{
    timeSeriesByDate: { [key: string]: TimeSeriesQuote[] } | undefined;
    indexSeriesByDate: { [key: string]: TimeSeriesQuote[] } | undefined;
    risk: { slope: number; intercept: number; r2: number } | undefined;
    activitiesByDay: { [key: string]: Array<Activity> } | undefined;
}>({
    timeSeriesByDate: undefined,
    indexSeriesByDate: undefined,
    risk: undefined,
    activitiesByDay: undefined
});

export default ActivitiesManagerContext;
