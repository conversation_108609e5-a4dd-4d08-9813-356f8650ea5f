// components/ActivitiesLayoutContext.tsx
import React, { createContext, useContext, useState } from 'react';

const ActivitiesLayoutContext = createContext<{
    isRightPanelExpanded: boolean;
    setIsRightPanelExpanded: (val: boolean) => void;
}>({
    isRightPanelExpanded: false,
    setIsRightPanelExpanded: () => { },
});


export const useActivitiesLayout = () => useContext(ActivitiesLayoutContext);

export default ActivitiesLayoutContext