import { SearchableSelectField } from '@/components/forms/fields/SearchableSelectField';
import usePaginatedSocialSources from '@/api/hooks/usePaginatedSocialSources';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SocialSource } from '@/api/hooks/useSocialSources';
import { FormAction, SourceType } from '../reducers/activityFormReducer';
import NewSourceModal from './NewsourceModel';

interface SocialSourceDropdownProps {
    value: string;
    dispatch: React.Dispatch<FormAction>;
    error?: string;
    hideActionButton?: boolean;
}

export default function SocialSourceDropdown({
    value,
    dispatch,
    error,
    hideActionButton = false
}: SocialSourceDropdownProps) {
    const {
        sources: socialSources,
        handleSearch,
        loadMore,
        hasMore,
        isLoading: isLoadingSources
    } = usePaginatedSocialSources({
        initialLimit: 20,
        prefetch: true
    });

    const [newSource, setNewSource] = useState(false);

    const sourceOptions = useMemo(() => {
        return socialSources.map((source) => ({
            key: `social-source-${source.url}`,
            value: source.url,
            label: source.name
        }));
    }, [socialSources]);

    const handleSocialSourceChange = useCallback(
        (sourceData: SocialSource | undefined) => {
            if (!sourceData) return;

            dispatch({
                type: 'SET_SOURCE',
                payload: {
                    sourceType: sourceData.url as SourceType
                }
            });
        },
        [dispatch]
    );

    const handleSourceChange = useCallback(
        (value: string) => {
            if (value) {
                const selectedSource = socialSources.find(
                    (source) => source.url === value
                );

                if (selectedSource) {
                    handleSocialSourceChange(selectedSource);
                } else if (value.trim() !== '') {
                    if (handleSearch) {
                        handleSearch(value);
                    }
                }
            } else {
                handleSocialSourceChange(undefined);
                handleSearch('');
            }
        },
        [socialSources, handleSearch, handleSocialSourceChange]
    );

    const handleNewSource = useCallback(() => {
        setNewSource(true);
    }, []);

    useEffect(() => {
        if (value && !socialSources.some((source) => source.url === value)) {
            const selectedSource = socialSources.find((source) => source.url === value);
            if (selectedSource) {
                handleSocialSourceChange(selectedSource);
            }
        }
    }, [value, socialSources, handleSocialSourceChange]);

    return (
        <>
            <SearchableSelectField
                label="Source"
                value={value}
                onChange={handleSourceChange}
                options={sourceOptions}
                error={error}
                onSearch={handleSearch}
                onLoadMore={loadMore}
                hasMore={hasMore}
                isLoading={isLoadingSources}
                searchPlaceholder="Search sources..."
                actionButton={
                    !hideActionButton
                        ? {
                              label: "Can't find source",
                              onClick: handleNewSource
                          }
                        : undefined
                }
            />
            <NewSourceModal open={newSource} setOpen={setNewSource} />
        </>
    );
}
