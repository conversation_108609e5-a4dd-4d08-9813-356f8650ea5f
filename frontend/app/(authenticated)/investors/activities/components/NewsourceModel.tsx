import {
    Dialog,
    DialogPanel,
    DialogTitle,
    Transition,
    TransitionChild
} from '@headlessui/react';
import React from 'react';
import AuthorCreationForm from './AuthorCreationForm';
import { Author } from '@quarterback/types';

interface NewSourceModalProps {
    open: boolean;
    setOpen: (value: boolean) => void;
}

export default function NewSourceModal({ open, setOpen }: NewSourceModalProps) {
    const handleConfirm = (e: React.MouseEvent) => {
        setOpen(false);
    };

    return (
        <Dialog className="relative z-50" onClose={() => {}} open={open}>
            <div className="fixed inset-0 z-50 overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4 text-center">
                    <DialogPanel className="relative w-full max-w-md transform overflow-hidden rounded-xl bg-white px-6 py-6 text-center shadow-xl transition-all">
                        <DialogTitle
                            as="h3"
                            className="text-lg font-semibold text-gray-900 mb-4">
                            Can’t find your source?
                        </DialogTitle>
                        <p className="text-sm text-gray-700 mb-6">
                            Please email us at{' '}
                            <a
                                href="mailto:<EMAIL>"
                                className="text-indigo-600 underline">
                                <EMAIL>
                            </a>{' '}
                            with the name of the source you’d like added to the list. This
                            will make it available for you to select as a manual activity.
                        </p>
                        <button
                            type="button"
                            onClick={handleConfirm}
                            className="w-full rounded-lg bg-indigo-600 px-4 py-3 text-sm font-medium text-white shadow-sm focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-indigo-600">
                            Return
                        </button>
                    </DialogPanel>
                </div>
            </div>
        </Dialog>
    );
}
