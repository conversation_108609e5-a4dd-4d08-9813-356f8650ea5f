import {
    Dialog,
    DialogPanel,
    DialogTitle,
    Transition,
    TransitionChild
} from '@headlessui/react';
import React from 'react';
import AuthorCreationForm from './AuthorCreationForm';
import { Author } from '@quarterback/types';

interface AuthorCreationModalProps {
    open: boolean;
    setOpen: (value: boolean) => void;
    onSuccess: (author: Author) => void;
    sourceFilter?: string;
}

export default function AuthorCreationModal({
    open,
    setOpen,
    onSuccess,
    sourceFilter
}: AuthorCreationModalProps) {
    const handleSuccess = (author: Author) => {
        onSuccess(author);
        setOpen(false);
    };

    const handleCancel = (e: React.MouseEvent) => {
        // e.stopPropagation();
        setOpen(false);
    };

    return (
        <Dialog
            className="relative z-50 transition-opacity ease-in-out data-[closed]:opacity-0"
            onClose={() => {}}
            open={open}>
            <div className="fixed inset-0 z-50 overflow-y-auto">
                <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <DialogPanel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                        <DialogTitle
                            as="h3"
                            className="text-lg font-semibold leading-6 text-gray-900 mb-4">
                            Add new Author
                        </DialogTitle>
                        <AuthorCreationForm
                            onSuccess={handleSuccess}
                            onCancel={handleCancel}
                            sourceFilter={sourceFilter}
                        />
                    </DialogPanel>
                </div>
            </div>
        </Dialog>
    );
}
