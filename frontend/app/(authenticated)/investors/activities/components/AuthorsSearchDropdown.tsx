import { SearchableSelectField } from '@/components/forms/fields/SearchableSelectField';
import usePaginatedAuthors from '@/api/hooks/usePaginatedAuthors';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Author } from '@quarterback/types';
import { FormAction } from '../reducers/activityFormReducer';
import AuthorCreationModal from './AuthorCreationModal';

interface AuthorsSearchDropdownProps {
    value: string;
    dispatch: React.Dispatch<FormAction>;
    error?: string;
    sourceFilter?: string;
}

export default function AuthorsSearchDropdown({
    value,
    dispatch,
    error,
    sourceFilter
}: AuthorsSearchDropdownProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const {
        authors,
        handleSearch,
        loadMore,
        hasMore,
        isLoading: isLoadingAuthors,
        refresh
    } = usePaginatedAuthors({
        initialLimit: 20,
        prefetch: true,
        sourceFilter
    });

    // Author options - memoized to prevent unnecessary recalculations
    const authorOptions = useMemo(() => {
        return authors.map((author) => ({
            key: author.key,
            value: author.key,
            label: author.userId
        }));
    }, [authors]);

    // Handle author change
    const handleAuthorChange = useCallback(
        (authorData: Author | undefined) => {
            if (!authorData) return;

            dispatch({
                type: 'SET_AUTHOR',
                payload: authorData
            });
        },
        [dispatch]
    );

    const handleValueChange = useCallback(
        (value: string) => {
            if (value) {
                const selectedAuthor = authors.find((author) => author.key === value);

                if (selectedAuthor) {
                    handleAuthorChange(selectedAuthor);
                } else {
                    if (handleSearch) {
                        handleSearch(value);
                    }
                }
            } else {
                handleSearch('');
                handleAuthorChange(undefined);
            }
        },
        [authors, handleSearch, handleAuthorChange]
    );

    useEffect(() => {
        if (value && !authors.some((author) => author.key === value)) {
            handleSearch(value);
        } else if (value) {
            const selectedAuthor = authors.find((author) => author.key === value);
            if (selectedAuthor) {
                handleAuthorChange(selectedAuthor);
            }
        }
    }, [value, authors, handleSearch, handleAuthorChange]);

    // Handle new author creation success
    const handleAuthorCreated = useCallback(
        (newAuthor: Author) => {
            handleAuthorChange(newAuthor);
            refresh();
        },
        [handleAuthorChange, refresh]
    );

    // Handle opening the author creation modal
    const handleAddNewAuthor = useCallback(() => {
        setIsModalOpen(true);
    }, []);

    return (
        <div>
            <SearchableSelectField
                label="Author"
                value={value}
                onChange={handleValueChange}
                options={authorOptions}
                error={error}
                onSearch={handleSearch}
                onLoadMore={loadMore}
                hasMore={hasMore}
                isLoading={isLoadingAuthors}
                searchPlaceholder="Search authors..."
                actionButton={{
                    label: 'Add new author',
                    onClick: handleAddNewAuthor
                }}
            />

            <AuthorCreationModal
                open={isModalOpen}
                setOpen={setIsModalOpen}
                onSuccess={handleAuthorCreated}
                sourceFilter={sourceFilter}
            />
        </div>
    );
}
