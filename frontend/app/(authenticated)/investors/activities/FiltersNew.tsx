import { Bars3BottomLeftIcon, ChevronUpDownIcon } from '@heroicons/react/24/outline';
import React, { useEffect, useMemo, useState } from 'react';
import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions,
    Field,
    Label,
    Popover,
    PopoverButton,
    PopoverPanel,
    Switch,
    Transition
} from '@headlessui/react';
import classNames from 'classnames';
import { CheckIcon, XMarkIcon } from '@heroicons/react/20/solid';
import {
    TrashIcon, BookOpenIcon, FlagIcon, ChatBubbleLeftEllipsisIcon, DocumentTextIcon
} from '@heroicons/react/24/outline';

export interface SubItem {
    label: React.ReactNode;
    action: () => void;
}

export interface MenuItem {
    icon: React.ReactNode;
    name: string;
    connector?: string | string[];
    items: SubItem[];
}

const MENU_VALUES = {
    MENU: 'menu',
    CONNECTOR: 'connector',
    OPTIONS: 'option'
}

type PopoverKey = (typeof MENU_VALUES)[keyof typeof MENU_VALUES];

export default function Filters({
    sources,
    formats,
    sourceFilters,
    setSourceFilters,
    formatFilters,
    handleFormatFilters,
    hasAnnouncement,
    showArchived,
    showFlagged,
    showPriceSensitive,
    handleHasAnouncementFilter,
    setShowArchived,
    setShowFlagged,
    setShowPriceSensitive,
    setSelectedFilter,
    selectedFilter
}: {
    sources: Array<string>;
    formats: Array<string>;
    sourceFilters: Array<string>;
    setSourceFilters: (filters: Array<string>) => void;
    formatFilters: Array<string>;
    handleFormatFilters: (filters: Array<string>) => void;
    hasAnnouncement: boolean;
    showArchived: boolean;
    showFlagged: boolean;
    showPriceSensitive: boolean;
    handleHasAnouncementFilter: (hasAnnouncement: boolean) => void;
    setShowArchived: (showArchived: boolean) => void;
    setShowFlagged: (showFlagged: boolean) => void;
    setShowPriceSensitive: (priceSensitive: boolean) => void;
    setSelectedFilter: (filters: MenuItem | null) => void;
    selectedFilter: MenuItem | null;
}) {
    const [connectorValue, setConnectorValue] = useState<null | string>(null)
    const [openPopover, setOpenPopover] = useState<PopoverKey | null>(null);

    useEffect(() => {


    }, [selectedFilter])
    const count = useMemo(() => {
        let count = 0;

        if (sourceFilters.length) count++;
        if (formatFilters.length) count++;
        if (hasAnnouncement) count++;

        return count;
    }, [sourceFilters, formatFilters, hasAnnouncement]);

    const togglePopover = (key: PopoverKey) => {
        setOpenPopover(prev => (prev === key ? null : key));
    };
    const filterMenu = [
        {
            icon: <TrashIcon className="w-4 h-4" />, name: 'Source', connector: ['contains', 'does not contain'], items: [
                { label: <>asas A</>, action: () => setSourceFilters('A') },
                { label: <>asas b</>, action: () => setSourceFilters('b') },
                { label: <>asas c</>, action: () => setSourceFilters('c') },
            ]
        },
        {
            icon: <BookOpenIcon className="w-4 h-4" />, name: 'Format', connector: ['contains', 'does not contain'], items: [
                { label: <>asas A</>, action: () => handleFormatFilters('A') },
                { label: <>asas b</>, action: () => handleFormatFilters('b') },
                { label: <>asas c</>, action: () => handleFormatFilters('c') },
            ]
        },
        {
            icon: <FlagIcon className="w-4 h-4" />, name: 'Flagged', connector: ['is'], items: [
                { label: 'yes', action: () => setShowFlagged(true) },
                { label: 'no', action: () => setShowFlagged(true) },
            ]
        },
        {
            icon: <DocumentTextIcon className="w-4 h-4" />, name: 'Announcement', items: [
                { label: 'announcement days only', action: () => handleHasAnouncementFilter(true) },
                { label: 'price sensitive only', action: () => handleHasAnouncementFilter(true) }
            ]
        },
        {
            icon: <ChatBubbleLeftEllipsisIcon className="w-4 h-4" />, name: 'Comments', connector: ['is'], items: [
                { label: 'comments only', action: () => handleHasAnouncementFilter(true) },
                { label: 'public comments only', action: () => handleHasAnouncementFilter(true) },
            ]
        },
    ]
    function reset() {
        setSelectedFilter(null)
        setSourceFilters([]);
        handleFormatFilters([]);
        handleHasAnouncementFilter(false);
        setShowArchived(false);
        setShowFlagged(false);
        setShowPriceSensitive(false);
    }

    const anouncementOnlyFilter = useMemo(() => {
        return formatFilters.length === 1 && formatFilters[0] === 'Announcement';
    }, [formatFilters]);

    const includesAnnouncementFilter = useMemo(() => {
        return formatFilters.includes('Announcement');
    }, [formatFilters]);


    return (
        <>
            <Popover className="relative">
                {({ open }) => (
                    <>
                        <PopoverButton onClick={() => togglePopover(MENU_VALUES.MENU)}>
                            <div className="flex items-center gap-x-1 hover:bg-gray-100 p-2 rounded-md cursor-pointer -mx-2">
                                <Bars3BottomLeftIcon className="size-4" />
                                <span className="text-sm text-gray-500">
                                    {selectedFilter?.name || 'Filters'}
                                </span>
                            </div>
                        </PopoverButton>

                        {openPopover === MENU_VALUES.MENU && <PopoverPanel className="absolute z-10 mt-2 bg-white border rounded shadow-lg flex min-w-[600px]" static>
                            {/* First Column: Menu */}
                            <div className="flex flex-col p-2 border-r min-w-[180px]">
                                {filterMenu.map((item) => {
                                    const { icon, name } = item;
                                    return (
                                        <button
                                            key={name}
                                            onClick={() => {
                                                if (item?.connector?.length === 1) {
                                                    setOpenPopover(MENU_VALUES.OPTIONS)
                                                    setConnectorValue(item.connector[0])
                                                } else {
                                                    setOpenPopover(MENU_VALUES.CONNECTOR)
                                                }
                                                setSelectedFilter(item)
                                            }}
                                            className={`px-4 py-2 text-left hover:bg-blue-50 ${selectedFilter?.name === name ? 'bg-blue-100 font-semibold' : ''}`}
                                        >
                                            <span className="inline-flex items-center gap-2">
                                                {icon}{name}
                                            </span>
                                        </button>
                                    );
                                })}
                            </div>
                        </PopoverPanel>}
                    </>)}
            </Popover>
            {selectedFilter?.connector &&
                <Popover className="relative">
                    {({ open }) => (
                        <>
                            <PopoverButton className="px-4 py-2 bg-blue-600 text-white rounded w-full text-left"
                                onClick={() => togglePopover(MENU_VALUES.OPTIONS)}>
                                {connectorValue || 'Select'}
                            </PopoverButton>
                            {
                                openPopover === MENU_VALUES.CONNECTOR &&
                                <PopoverPanel className="absolute z-10 mt-2 bg-white border rounded shadow-lg flex min-w-[600px]" static>
                                    <div className="flex flex-col p-2 border-r min-w-[180px]">
                                        {(Array.isArray(selectedFilter.connector)
                                            ? selectedFilter.connector
                                            : [selectedFilter.connector]
                                        ).map((conn) => (
                                            <button
                                                key={conn}
                                                onClick={() => {
                                                    setConnectorValue(conn);
                                                    togglePopover(MENU_VALUES.OPTIONS);
                                                }}
                                                className={`px-4 py-2 text-left hover:bg-blue-50 ${selectedFilter?.name === name ? 'bg-blue-100 font-semibold' : ''}`}
                                            >
                                                <span className="inline-flex items-center gap-2">
                                                    {conn}
                                                </span>
                                            </button>

                                        ))}
                                    </div>
                                </PopoverPanel>}
                        </>)}
                </Popover >}
            {selectedFilter?.items && (selectedFilter?.connector || connectorValue) && openPopover === MENU_VALUES.OPTIONS &&
                <Popover className="relative" >
                    {({ open }) => (
                        <>
                            <PopoverButton className="px-4 py-2 bg-blue-600 text-white rounded w-full text-left"
                                onClick={() => togglePopover(null)}>
                                Options
                            </PopoverButton>
                            {
                                openPopover === MENU_VALUES.OPTIONS &&
                                <PopoverPanel className="absolute z-10 mt-2 bg-white border rounded shadow-lg flex min-w-[600px]" static>
                                    {/* Third Column: Items */}
                                    {selectedFilter && (
                                        <div className="flex flex-col p-2 min-w-[180px]">
                                            {selectedFilter.items.map((item, idx) => (
                                                <button
                                                    key={idx}
                                                    onClick={item.action}
                                                    className="px-4 py-2 text-left hover:bg-blue-50"
                                                >
                                                    {item.label}
                                                </button>
                                            ))}


                                        </div>
                                    )}
                                </PopoverPanel>}</>)}
                </Popover>}

            <button
                onClick={reset}
                className="mt-4 px-4 py-2 text-left text-red-600 hover:bg-red-50"
            >
                Reset Filter
            </button>
        </>
    );

}
