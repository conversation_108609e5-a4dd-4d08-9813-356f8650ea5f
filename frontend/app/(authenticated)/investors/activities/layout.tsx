'use client';

import useInsightsMutation from '@/api/hooks/mutations/useInsightsMutation';
import useActivities from '@/api/hooks/useActivities';
import useRisk from '@/api/hooks/useRisk';
import useTimeSeries, { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import ActivitiesManagerContext from '@/app/(authenticated)/investors/activities/ActivitiesManagerContext';
import ActivityGroup from '@/app/(authenticated)/investors/activities/ActivityGroup';
import Filters from '@/app/(authenticated)/investors/activities/Filters';
import DateRangePicker from '@/components/DateRangePicker';
import { useOrganisation } from '@/components/OrganisationProvider';
import activityFormat from '@/util/activityFormat';
import { sentimentScore } from '@/util/sentiment';
import source from '@/util/source';
import {
    PlusIcon,
    SparklesIcon,
    XMarkIcon,
    CloudArrowDownIcon
} from '@heroicons/react/24/outline';
import { Activity, Activity2 } from '@quarterback/types';
import { groupBy, wait } from '@quarterback/util';
import { isBefore, startOfDay, sub, subMonths } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { download, generateCsv, mkConfig } from 'export-to-csv';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { DateRange } from 'react-day-picker';
import { remark } from 'remark';
import html from 'remark-html';
import ManualActivityFormFactory from './ManualActivityFormFactory';
import useActivitiesMutation from '../../../../api/hooks/admin/mutations/useActivitiesMutation';
import Button from '@/components/ui/Button';
import ActivitiesLayoutContext, { useActivitiesLayout } from './ActivityLayoutContext';
import classNames from 'classnames';
import ActivitiesHeader from './ActivitiesHeader';
import Link from 'next/link';
import RightPanel from '@/components/ui/RightPanel';

function BaseActivitiesLayout({ children }: React.PropsWithChildren) {
    const router = useRouter();
    const organisation = useOrganisation();
    const searchParams = useSearchParams();
    const { trigger } = useActivitiesMutation();
    const { isRightPanelExpanded, setIsRightPanelExpanded } = useActivitiesLayout();

    const [range, setRange] = useState<DateRange>({
        from: startOfDay(
            searchParams.has('from')
                ? new Date(searchParams.get('from')!)
                : sub(new Date(), {
                    months: 1
                })
        ),
        to: startOfDay(
            searchParams.has('to') ? new Date(searchParams.get('to')!) : new Date()
        )
    });

    const [showSummary, setShowSummary] = useState(false);
    const [sourceFilters, setSourceFilters] = useState<Array<string>>([]);
    const [formatFilters, setFormatFilters] = useState<Array<string>>([]);
    const [hasAnnouncement, setHasAnnouncement] = useState(false);
    const [showArchived, setShowArchived] = useState(false);
    const [showFlagged, setShowFlagged] = useState(false);
    const [summary, setSummary] = useState<string | undefined>(undefined);
    const [summaryLoading, setSummaryLoading] = useState<boolean>(false);
    const [showPriceSensitive, setShowPriceSensitive] = useState(false);
    const [addingActivity, setAddingActivity] = useState(false);

    useEffect(() => {
        if (isRightPanelExpanded) {
            setShowSummary(false);
        }
    }, [isRightPanelExpanded]);

    async function addManualActivity(data: Activity2[]) {
        const result = await trigger(data);
        return result;
    }

    const { data: activities = [] } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!,
        {
            archived: showArchived
        }
    );

    const handleFormatFilters = useCallback(
        (filters: Array<string>) => {
            if (!filters.includes('Announcement') || !hasAnnouncement) {
                setShowPriceSensitive(false);
            }
            setFormatFilters(filters);
        },
        [hasAnnouncement]
    );

    const handleHasAnouncementFilter = useCallback(
        (hasAnnouncement: boolean) => {
            if (!hasAnnouncement || !formatFilters.includes('Announcement')) {
                setShowPriceSensitive(false);
            }
            setHasAnnouncement(hasAnnouncement);
        },
        [formatFilters]
    );

    const { data: risk } = useRisk(organisation?.selected?.entity, {
        symbol: 'AXJO',
        exchange: 'ASX'
    });

    const { data: indexSeries } = useTimeSeries(
        { symbol: 'AXJO', exchange: 'ASX' },
        range.from!,
        range.to!
    );
    const { data: timeSeries } = useTimeSeries(
        organisation?.selected?.entity,
        range.from!,
        range.to!
    );

    const timeSeriesByDate = useMemo(() => {
        return groupBy(timeSeries?.values ?? [], (it) =>
            formatInTimeZone(new Date(it.datetime), 'Australia/Sydney', 'yyyy-MM-dd')
        );
    }, [timeSeries]);

    const indexSeriesByDate = useMemo(() => {
        return groupBy(indexSeries?.values ?? [], (it) =>
            formatInTimeZone(new Date(it.datetime), 'Australia/Sydney', 'yyyy-MM-dd')
        );
    }, [indexSeries]);

    const activitiesByDay = useMemo(() => {
        return groupBy(activities, (it) =>
            formatInTimeZone(it.posted!, 'Australia/Sydney', 'yyyy-MM-dd')
        );
    }, [activities]);

    const grouped = useMemo(() => {
        return Object.keys(activitiesByDay)
            .map((group) => {
                const date = new Date(group);

                return {
                    name: formatInTimeZone(
                        date,
                        'Australia/Sydney',
                        isBefore(date, subMonths(new Date(), 12))
                            ? 'MMM do, yyyy'
                            : 'MMM do'
                    ),
                    datetime: date,
                    activities: activitiesByDay[group].sort(
                        (a, b) => b.posted.valueOf() - a.posted.valueOf()
                    )
                };
            })
            .sort((a, b) => {
                return b.datetime.valueOf() - a.datetime.valueOf();
            });
    }, [activitiesByDay]);

    const filteredGroups = useMemo(() => {
        return grouped
            .filter((group) => {
                let include = true;

                if (hasAnnouncement) {
                    include =
                        include &&
                        group.activities.some(
                            (activity) => activity.type === 'asx-announcement'
                        );
                }

                if (showPriceSensitive) {
                    include =
                        include &&
                        group.activities.some(
                            (activity) =>
                                activity.type === 'asx-announcement' &&
                                activity.priceSensitive
                        );
                }

                if (showFlagged) {
                    include =
                        include && group.activities.some((activity) => activity.flagged);
                }

                if (sourceFilters?.length && formatFilters?.length) {
                    include =
                        include &&
                        group.activities.some(
                            (activity) =>
                                sourceFilters.some(
                                    (filter) => filter === source(activity)
                                ) &&
                                formatFilters.some(
                                    (filter) => filter === activityFormat(activity)
                                )
                        );
                } else {
                    if (sourceFilters.length) {
                        include =
                            include &&
                            group.activities.some((activity) =>
                                sourceFilters.some(
                                    (filter) => filter === source(activity)
                                )
                            );
                    }

                    if (formatFilters.length) {
                        include =
                            include &&
                            group.activities.some((activity) =>
                                formatFilters.some(
                                    (filter) => filter === activityFormat(activity)
                                )
                            );
                    }
                }

                return include;
            })
            .map((group) => {
                return {
                    ...group,
                    activities: group.activities.filter((activity) => {
                        let include = true;

                        if (showPriceSensitive) {
                            include =
                                include &&
                                activity.type === 'asx-announcement' &&
                                activity.priceSensitive;
                        }

                        if (showFlagged) {
                            include = include && !!activity.flagged;
                        }

                        if (formatFilters.length) {
                            include =
                                include &&
                                formatFilters.some(
                                    (filter) => filter === activityFormat(activity)
                                );
                        }

                        if (sourceFilters.length) {
                            include =
                                include &&
                                sourceFilters.some(
                                    (filter) => filter === source(activity)
                                );
                        }

                        return include;
                    })
                };
            });
    }, [
        grouped,
        hasAnnouncement,
        showPriceSensitive,
        showFlagged,
        sourceFilters,
        formatFilters
    ]);

    const filteredActivityIds: string[] = useMemo(() => {
        return filteredGroups.flatMap((it) => it.activities).map((it) => it.id!);
    }, [filteredGroups]);

    const type = useMemo(() => {
        if (sourceFilters.length === 1 && formatFilters.length === 1) {
            return `${sourceFilters[0]} ${formatFilters[0] === 'publication' ? 'news' : formatFilters[0]}`;
        }
        return 'mixed';
    }, [sourceFilters, formatFilters]);

    const { getInsight } = useInsightsMutation({
        organisation: organisation.selected?.organisation!,
        entity: organisation.selected?.entity!
    });

    async function handleGenerateSummary() {
        if (isRightPanelExpanded) {
            setIsRightPanelExpanded(false);
            router.push('/investors/activities')
        }
        if (!showSummary) {
            setSummaryLoading(true);
            setShowSummary(true);

            const { data, error } = await getInsight({
                organisation: organisation.selected?.organisation?.id!,
                symbol: organisation.selected?.entity?.symbol!,
                exchange: organisation.selected?.entity?.exchange!,
                from: range.from!,
                to: range.to!,
                limit: 70,
                ids: filteredActivityIds,
                type: type
            });

            const result = error
                ? '*Something went wrong while generating AI insights*'
                : data;

            if (result) {
                const content = await remark().use(html).process(result);
                await wait(900);
                setSummary(content?.value.toString());
                setSummaryLoading(false);
            }
        }
    }

    const activityRedirect = useMemo(() => {
        const first = filteredGroups.flatMap((group) => group.activities)[0];

        if (first) {
            return `/investors/activities/${first.id}${searchParams.size > 0 ? `?${searchParams.toString()}` : ''}`;
        } else {
            return undefined;
        }
    }, [filteredGroups, searchParams]);

    useEffect(() => {
        setShowSummary(false);
        setSummary(undefined);
    }, [filteredActivityIds]);

    const formats = useMemo(() => {
        return Array.from(new Set(activities.map((it) => activityFormat(it))));
    }, [activities]);

    const sources = useMemo(() => {
        return Array.from(new Set(activities.map((it) => source(it))));
    }, [activities]);

    useEffect(() => {
        if (activityRedirect) {
            router.replace(activityRedirect);
        }
    }, [activityRedirect, router]);

    function exportCSV() {
        function title(activity: Activity) {
            switch (activity.type) {
                case 'hotcopper':
                case 'asx-announcement':
                case 'linkedIn':
                    return activity.title ?? '';
                default:
                    return '';
            }
        }

        function body(activity: Activity) {
            switch (activity.type) {
                case 'hotcopper':
                    return activity.description ?? '';
                case 'tweet':
                case 'linkedIn':
                case 'reddit':
                case 'redditComment':
                    return activity.body ?? '';
                default:
                    return '';
            }
        }

        function author(activity: Activity) {
            switch (activity.type) {
                case 'asx-announcement':
                    return 'ASX';
                case 'tweet':
                case 'hotcopper':
                case 'reddit':
                case 'redditComment':
                    return activity?.author?.userId ?? '';
                case 'linkedIn':
                    return activity?.author?.name ?? '';
                case 'news':
                    return activity.source.name ?? '';
                default:
                    return '';
            }
        }

        const config = mkConfig({
            showColumnHeaders: true,
            columnHeaders: [
                { key: 'type', displayLabel: 'Type' },
                { key: 'posted', displayLabel: 'Posted' },
                { key: 'author', displayLabel: 'Author' },
                { key: 'title', displayLabel: 'Title' },
                { key: 'body', displayLabel: 'Body' },
                { key: 'hotcopperSentiment', displayLabel: 'HotCopper Sentiment' },
                { key: 'hotcopperDisclosure', displayLabel: 'HotCopper Disclosure' },
                { key: 'sentiment', displayLabel: 'Sentiment' }
            ]
        });

        const filteredActivities = filteredGroups.flatMap((it) => it.activities);

        const csv = generateCsv(config)(
            filteredActivities.map((activity) => ({
                type: activity.type,
                posted: activity.posted.toISOString(),
                title: title(activity),
                body: body(activity),
                author: author(activity),
                sentiment: activity.sentiment ? sentimentScore(activity.sentiment) : '',
                hotcopperSentiment:
                    activity.type === 'hotcopper'
                        ? (activity.hotcopper?.sentiment ?? 'NONE')
                        : '',
                hotcopperDisclosure:
                    activity.type === 'hotcopper'
                        ? (activity.hotcopper?.disclosure ?? 'UNDISCLOSED')
                        : ''
            }))
        );

        download(config)(csv);
    }

    const getQuoteData = useCallback(
        (
            date: Date,
            source: Record<string, TimeSeriesQuote[]>
        ): {
            current: TimeSeriesQuote | undefined;
            previous: TimeSeriesQuote | undefined;
        } => {
            const daysToSubtractMap = [2, 3, 1, 1, 1, 1, 1];
            const currentDay = formatInTimeZone(date, 'Australia/Sydney', 'yyyy-MM-dd');
            const daysToSubtract = daysToSubtractMap[date.getDay()];
            const previousDay = formatInTimeZone(
                sub(date, { days: daysToSubtract }),
                'Australia/Sydney',
                'yyyy-MM-dd'
            );
            return {
                current: source[currentDay]?.[0],
                previous: source[previousDay]?.[0]
            };
        },
        []
    );

    return (
        <div>
            <div className="border-r">
                <div className="bg-white divide-y divide-gray-200 sticky top-0 z-10">
                    <div className="sm:flex sm:items-center px-4 py-2">
                        <div className="flex flex-row gap-x-2 justify-between w-full">
                            <div className="flex gap-x-3 items-center">
                                <div className="flex items-center gap-x-2">
                                    <label
                                        htmlFor="sortby"
                                        className="block text-sm font-medium text-gray-900">
                                        Group by
                                    </label>
                                    <select
                                        id="sortby"
                                        name="sortby"
                                        className="block text-sm rounded border-0 py-1 pl-2 pr-8 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600"
                                        defaultValue="Date">
                                        <option>Date</option>
                                    </select>
                                </div>
                                <div className="h-5 w-px bg-gray-300" />

                                {/* FIXME: Create Filter Context */}
                                <Filters
                                    formats={formats}
                                    sources={sources}
                                    formatFilters={formatFilters}
                                    showPriceSensitive={showPriceSensitive}
                                    handleFormatFilters={handleFormatFilters}
                                    sourceFilters={sourceFilters}
                                    setSourceFilters={setSourceFilters}
                                    hasAnnouncement={hasAnnouncement}
                                    showArchived={showArchived}
                                    showFlagged={showFlagged}
                                    handleHasAnouncementFilter={
                                        handleHasAnouncementFilter
                                    }
                                    setShowArchived={setShowArchived}
                                    setShowFlagged={setShowFlagged}
                                    setShowPriceSensitive={setShowPriceSensitive}
                                />
                            </div>
                            <DateRangePicker range={range} setRange={setRange} required />
                        </div>
                    </div>
                    <div className="flex justify-between items-center px-4 py-2">
                        {filteredActivityIds?.length ? (
                            <>
                                <button
                                    className="flex items-center gap-2 px-2 py-1 text-[#5A3FCC] font-semibold rounded-lg bg-gradient-to-r to-[#E0E7FF] from-[#F5F3FF] border border-[#C4B5FD] shadow-sm"
                                    onClick={handleGenerateSummary}>
                                    <SparklesIcon className="size-4" />
                                    <span>Generate Summary</span>
                                </button>
                            </>
                        ) : null}
                        <div className="flex gap-2">
                            <Button
                                variant="secondary"
                                onClick={exportCSV}
                                size="sm"
                                icon={<CloudArrowDownIcon className="size-4" />}>
                                Export CSV
                            </Button>
                            <Button
                                variant="primary"
                                onClick={() => setAddingActivity(true)}
                                size="sm"
                                icon={<PlusIcon className="size-4" />}>
                                New Activity
                            </Button>
                        </div>
                    </div>
                </div>
                <RightPanel open={showSummary} onClose={() => setShowSummary(false)}>
                    <div className="flex justify-between items-center flex-1 bg-gradient-to-r to-[#E0E7FF] from-[#F5F3FF] px-4 py-3 border border-b-[#C4B5FD]">
                        <div className="flex gap-2 items-center text-[#5A3FCC]">
                            <div className="rounded-lg px-2 py-1 bg-gradient-to-br to-[#E0E7FF] from-[#F5F3FF] border border-[#C4B5FD] shadow-sm">
                                <SparklesIcon className="size-4 " />
                            </div>
                            <div>AI Generated Summary</div>
                        </div>
                    </div>
                    {summaryLoading ? (
                        <div className="my-4 ml-10 text-sm">
                            <div className="flex space-x-2 justify-center items-center">
                                <span className="sr-only">Loading...</span>
                                <div className="size-1.5 bg-indigo-600 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                                <div className="size-1.5 bg-indigo-600 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                                <div className="size-1.5 bg-indigo-600 rounded-full animate-bounce"></div>
                            </div>
                        </div>
                    ) : null}
                    {summary && !summaryLoading ? (
                        <div
                            className="py-4 px-6 text-sm activity-summary"
                            dangerouslySetInnerHTML={{ __html: summary }}
                        />
                    ) : null}
                </RightPanel>

                <div className="flow-root">
                    <div className="overflow-x-auto">
                        <div className="inline-block min-w-full align-middle">
                            <table className="w-full bg-white table-auto relative border-collapse border border-gray-300">
                                <ActivitiesHeader />
                                <tbody className="bg-white">
                                    {filteredGroups.map((group) => (
                                        <ActivityGroup
                                            key={group.name}
                                            group={group}
                                            entity={organisation.selected?.entity}
                                            quote={getQuoteData(
                                                group.datetime,
                                                timeSeriesByDate
                                            )}
                                            index={getQuoteData(
                                                group.datetime,
                                                indexSeriesByDate
                                            )}
                                            risk={risk}
                                        />
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div className={classNames(
                    { 'w-[34rem] z-10': isRightPanelExpanded },
                    "top-0 bottom-0 right-0 overflow-y-scroll fixed bg-transparent mt-[60px]")}>
                    {children}
                </div>
            </div>

            <ManualActivityFormFactory
                onSubmit={addManualActivity}
                addingActivity={addingActivity}
                setAddingActivity={setAddingActivity}
            />
        </div>
    );
}

export default function ActivitiesLayout({ children }: { children: React.ReactNode }) {
    const [isRightPanelExpanded, setIsRightPanelExpanded] = useState(false);
    return (
        <ActivitiesLayoutContext.Provider
            value={{
                isRightPanelExpanded,
                setIsRightPanelExpanded
            }}
        >
            <BaseActivitiesLayout>{children}</BaseActivitiesLayout>
        </ActivitiesLayoutContext.Provider>
    );
}