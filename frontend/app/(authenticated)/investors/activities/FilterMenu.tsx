import { Popover, Transition, Portal } from '@headlessui/react';
import { Fragment, useRef, useState, useEffect } from 'react';
import { CheckIcon } from '@heroicons/react/20/solid';
import classNames from 'classnames';

type FilterMenuProps<T> = {
    menuName: string;
    items: T[];
    selectedItems: T[];
    onChange: (selected: T[]) => void;
    itemToString?: (item: T) => string;
    className?: string;
};

export default function FilterMenu<T>({
    menuName,
    items,
    selectedItems,
    onChange,
    itemToString = (item) => (typeof item === 'string' ? item : String(item)),
    className,
}: FilterMenuProps<T>) {
    const buttonRef = useRef<HTMLButtonElement>(null);
    const panelRef = useRef<HTMLDivElement>(null);
    const [position, setPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
    const [open, setOpen] = useState(false);

    // to osition popover panel next to button
    useEffect(() => {
        if (open && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setPosition({
                top: rect.top + window.scrollY,
                left: rect.right + 2,
                // left: rect.right + window.scrollX + 8, // right edge of the element + x scroll offset + extra gap between the element and panel
            });
        }
    }, [open]);

    // blur the panel 
    useEffect(() => {
        function onClickOutside(event: MouseEvent) {
            const target = event.target as Node;
            if (
                open &&
                panelRef.current &&
                buttonRef.current &&
                !panelRef.current.contains(target) &&
                !buttonRef.current.contains(target)
            ) {
                setOpen(false);
            }
        }

        document.addEventListener('mousedown', onClickOutside);
        return () => {
            document.removeEventListener('mousedown', onClickOutside);
        };
    }, [open]);

    function toggleItem(item: T) {
        if (selectedItems.includes(item)) {
            onChange(selectedItems.filter(i => i !== item));
        } else {
            onChange([...selectedItems, item]);
        }
    }

    return (
        <div className="relative">
            <button
                ref={buttonRef}
                type="button"
                className={classNames(
                    "py-1 px-2 w-[288px] text-left text-sm font-medium leading-6 text-gray-900 hover:bg-gray-100",
                    "truncate overflow-hidden whitespace-nowrap",
                    className
                )}
                onClick={() => setOpen(!open)}
            >
                {menuName} {selectedItems.length ? `(${selectedItems.join(', ')})` : ''}
            </button>

            <Portal>
                <Transition
                    as={Fragment}
                    show={open}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                >
                    <div
                        ref={panelRef}
                        style={{ top: position.top, left: position.left, position: 'absolute' }}
                        className="w-48 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none max-h-60 overflow-auto z-50"
                    >
                        {items.map(item => {
                            const label = itemToString(item);
                            const selected = selectedItems.includes(item);
                            return (
                                <button
                                    key={label}
                                    type="button"
                                    onClick={e => {
                                        e.stopPropagation();
                                        toggleItem(item);
                                    }}
                                    className={classNames(
                                        "flex items-center w-full px-2 py-1 text-sm cursor-pointer",
                                        selected ? "bg-indigo-600 text-white" : "text-gray-900 hover:bg-indigo-100"
                                    )}
                                >
                                    {selected && <CheckIcon className="h-5 w-5 mr-1" aria-hidden="true" />}
                                    <span>{label}</span>
                                </button>
                            );
                        })}
                    </div>
                </Transition>
            </Portal>
        </div>
    );
}
