import { Sentiment } from '@quarterback/types';
import { DiscreteSentiment, discreteSentiment, sentimentScore } from '@/util/sentiment';
import React, { useMemo } from 'react';
import classNames from 'classnames';
import { FaceFrownIcon, FaceSmileIcon } from '@heroicons/react/24/outline';

export default function SentimentIndicator({ sentiment, showIcon = false }: { sentiment: Sentiment, showIcon?: boolean }) {
    const score = sentimentScore(sentiment);
    const discrete = discreteSentiment(score);

    const colors = useMemo(() => {
        if (discrete === DiscreteSentiment.NEGATIVE)
            return ['text-red-700'];
        if (discrete === DiscreteSentiment.LACKING)
            return ['text-yellow-700'];
        if (discrete === DiscreteSentiment.POSITIVE)
            return ['text-green-700'];
        if (discrete === DiscreteSentiment.NEUTRAL)
            return ['text-slate-700'];

        return [];
    }, [discrete]);

    return (
        <div
            className={classNames(
                'inline-flex gap-x-1 items-center rounded-md px-2 py-1 text-xs font-medium ',
                ...colors
            )}>
            {showIcon && (score < 0 ? (
                <FaceFrownIcon className="size-4" />
            ) : (
                <FaceSmileIcon className="size-4" />
            ))}
            <span>{score.toFixed(2)}</span>

        </div>
    );
}
