import { ActivityFormData, NewsFormData, NewsSource } from '@quarterback/types';
import { Author } from '@quarterback/types';
import { createDate } from '@/util/date';

export type SourceType =
    | 'https://x.com'
    | 'https://hotcopper.com.au'
    | 'https://linkedin.com'
    | 'https://reddit.com'
    | 'news'
    | 'https://asx.com.au'
    | 'https://facebook.com'
    | 'https://instagram.com'
    | 'https://youtube.com'
    | 'https://tiktok.com'
    | 'https://discord.com'
    | 'https://t.me'
    | 'https://medium.com'
    | 'https://quora.com'
    | 'https://slack.com'
    | 'https://advfn.com'
    | 'https://podcasts.apple.com'
    | 'https://audible.com'
    | 'https://aussiestockforums.com'
    | 'https://castbox.fm'
    | 'https://clubhouse.com'
    | 'https://iheart.com'
    | 'https://investorshub.advfn.com'
    | 'https://pinterest.com'
    | 'https://snapchat.com'
    | 'https://spotify.com'
    | 'https://stocktwits.com'
    | 'https://strawman.com'
    | 'https://tradingqna.com'
    | 'https://tumblr.com'
    | 'https://vimeo.com'
    | 'https://wechat.com'
    | 'https://whatsapp.com'
    | 'https://whirlpool.net.au'
    | 'https://bogleheads.org';
export type FormatType = 'chatter' | 'broadcast' | 'news';

export type FormAction =
    | { type: 'SET_FORMAT'; payload: FormatType }
    | { type: 'SET_SOURCE'; payload: { sourceType: SourceType; sourceData?: NewsSource } }
    | { type: 'SET_AUTHOR'; payload: Author }
    | { type: 'UPDATE_FIELD'; payload: { field: string; value: any } }
    | { type: 'RESET' };

export const initialFormState: ActivityFormData = {
    format: 'chatter',
    source: 'https://advfn.com',
    body: '',
    title: '',
    url: '',
    image: '',
    posted: createDate().toISOString(),
    likes: '0'
} as ActivityFormData;

export function formReducer(
    state: ActivityFormData,
    action: FormAction
): ActivityFormData {
    switch (action.type) {
        case 'SET_FORMAT': {
            const format = action.payload;

            let source: SourceType = 'https://advfn.com';
            if (format === 'news') {
                source = 'news';
            } else if (format === 'broadcast' || format === 'chatter') {
                source = 'https://advfn.com';
            }

            return {
                ...initialFormState,
                format,
                source,
                posted: createDate().toISOString()
            } as ActivityFormData;
        }

        case 'SET_SOURCE': {
            const { sourceType, sourceData } = action.payload;

            const newState = {
                ...state,
                source: sourceType,
                author: undefined
            } as ActivityFormData;

            if (sourceType === 'news' && sourceData) {
                (newState as NewsFormData).newsSource = sourceData;
            }

            return newState;
        }

        case 'SET_AUTHOR': {
            const author = action.payload;

            return {
                ...state,
                author
            } as ActivityFormData;
        }

        case 'UPDATE_FIELD':
            return {
                ...state,
                [action.payload.field]: action.payload.value
            } as ActivityFormData;

        case 'RESET':
            return {
                ...initialFormState,
                posted: createDate().toISOString()
            };

        default:
            return state;
    }
}
