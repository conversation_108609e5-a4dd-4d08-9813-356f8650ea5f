import { isBroadcast } from '@/api/hooks/useBroadcasts';
import SentimentIndicator from '@/app/(authenticated)/investors/activities/SentimentIndicator';
import ActivityFormatIndicator from '@/components/ActivityFormatIndicator';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import Checkbox from '@/components/ui/Checkbox';
import { formatWithTimeZone } from '@/util/date';
import useActivitySummary from '@/util/useActivitySummary';
import { ArrowTurnDownRightIcon, FlagIcon } from '@heroicons/react/20/solid';
import { Activity } from '@quarterback/types';
import classNames from 'classnames';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useMemo } from 'react';

export default function ActivityRow({
    activity,
    className,
    selectedActivities,
    setSelectedActivities
}: {
    activity: Activity;
    className?: string;
    selectedActivities: Array<string>;
    setSelectedActivities: React.Dispatch<React.SetStateAction<string[]>>;
}) {
    const pathname = usePathname();
    const searchParams = useSearchParams();

    const router = useRouter();
    const { title, description, author, engagement } = useActivitySummary(activity);

    const broadcast = useMemo(() => {
        return isBroadcast(activity);
    }, [activity]);

    const truncatedAuthor = useMemo(() => {
        if (author === 'Australian Securities Exchange') return 'ASX';
        else if (author && (author?.length ?? 0) > 25) {
            return author.substring(0, 25) + '…';
        } else {
            return author;
        }
    }, [author]);

    function handleClick() {
        router.push(
            `/investors/activities/${activity.id}${searchParams.size > 0 ? `?${searchParams.toString()}` : ''}`
        );
    }

    const showThreadIcon = useMemo(() => {
        return activity.type === 'hotcopper' && activity.url.includes('page-');
    }, [activity]);

    const priceSensitive = useMemo(() => {
        if (activity.type === 'asx-announcement' && activity.priceSensitive)
            return <span className="text-red-500">$</span>;
        return null;
    }, [activity]);
    return (
        <tr
            key={activity.id}
            className={classNames(className, 'cursor-pointer')}
            onClick={handleClick}>
            <td className="whitespace-nowrap py-3 text-xs text-gray-500 sm:pl-3 w-10">
                <Checkbox checked={false} />
            </td>
            <td className="whitespace-nowrap text-sm px-2 ">
                <div className="flex items-center gap-x-2">
                    <ActivitySourceIcon
                        className="rounded-[4px] size-5 object-cover"
                        activity={activity}
                    />
                    <span className="text-xs font-medium">{truncatedAuthor}</span>
                </div>
            </td>

            <td className="text-gray-500 px-4 overflow-hidden break-all text-ellipsis border border-gray-200 bg-purple-50">
                <div className="text-sm font-medium flex items-center gap-x-2">
                    {showThreadIcon ? (
                        <ArrowTurnDownRightIcon className="size-5 text-indigo-800" />
                    ) : null}
                    {title ? (
                        <>
                            <span className="text-gray-900 text-ellipsis overflow-hidden line-clamp-1 break-all">
                                {title}
                            </span>
                            {priceSensitive}
                        </>
                    ) : null}
                    {description ? (
                        <span className="mt-1 text-xs flex-1 text-ellipsis overflow-hidden line-clamp-1 break-all">
                            {description}
                        </span>
                    ) : null}
                </div>
            </td>
            <td className="whitespace-nowrap py-3 text-xs text-gray-500 sm:pl-3 border border-gray-200 bg-purple-50">
                {formatWithTimeZone(activity.posted, 'HH:mm')}
            </td>
            <td className="relative whitespace-nowrap text-right text-sm font-medium sm:pr-3 border border-gray-200 bg-purple-50">
                {activity.sentiment && (
                    <SentimentIndicator sentiment={activity.sentiment} />
                )}
            </td>
            <td className="whitespace-nowrap py-2 text-xs text-gray-500 sm:pl-3 text-right pr-3 border border-gray-200 bg-purple-50">
                {
                    <ActivityFormatIndicator
                        activity={activity}
                    />
                }
            </td>
        </tr>
    );
}
