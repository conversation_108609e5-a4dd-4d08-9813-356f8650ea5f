'use client';

import React, { useMemo, useState } from 'react';
import { sub } from 'date-fns';
import DateRangePicker from '@/components/DateRangePicker';
import { DateRange } from 'react-day-picker';
import {
    BoardReportDownloadLink,
    BoardReportPreview
} from '@/app/(authenticated)/investors/reports/board';
import classNames from 'classnames';

// Toggle between download button and pdf viewer
const DEBUG = false;

export default function Reports() {
    const [type, setType] = useState<'board'>('board');

    const [range, setRange] = useState<DateRange>({
        from: sub(new Date(), { months: 2 }),
        to: new Date()
    });

    const [Preview, Download] = useMemo(() => {
        switch (type) {
            case 'board':
                return [BoardReportPreview, BoardReportDownloadLink];
        }
    }, [type]);

    return (
        <div className="p-4">
            <h2 className="text-lg font-semibold leading-7 text-gray-900 sm:truncate sm:text-xl sm:tracking-tight">
                Reports
            </h2>
            <div className="bg-white rounded-md p-4 mt-4 border flex items-end gap-x-4">
                <div className="grid grid-cols-2 gap-x-4 flex-1">
                    <div>
                        <label
                            htmlFor="report-type"
                            className="block text-sm font-medium leading-6 text-gray-900">
                            Type
                        </label>
                        <select
                            id="report-type"
                            name="report-type"
                            value={type}
                            onChange={(e) => setType(e.target.value as typeof type)}
                            className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            <option value="board">Board report</option>
                        </select>
                    </div>
                    <div>
                        <label
                            htmlFor="date-range-picker"
                            className="block text-sm font-medium leading-6 text-gray-900">
                            Date range
                        </label>
                        <DateRangePicker
                            id="date-range-picker"
                            range={range}
                            setRange={setRange}
                            className="mt-2"
                            required
                        />
                    </div>
                </div>
                {!DEBUG && (
                    <Download
                        range={range}
                        className={(loading) =>
                            classNames(
                                'rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600',
                                {
                                    'bg-indigo-500 pointer-events-none cursor-wait':
                                        loading
                                }
                            )
                        }
                    />
                )}
            </div>
            {DEBUG && (
                <div className="mt-8">
                    <Preview range={range} />
                </div>
            )}
        </div>
    );
}
