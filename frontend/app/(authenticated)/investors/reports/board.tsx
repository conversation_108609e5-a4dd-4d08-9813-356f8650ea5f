import { DateRange } from 'react-day-picker';
import useBoardReportData from '@/components/reports/board-report/useBoardReportData';
import { PDFDownloadLink, PDFViewer } from '@react-pdf/renderer';
import BoardReport from '@/components/reports/board-report';
import React from 'react';
import posthog from 'posthog-js';
import { useOrganisation } from '@/components/OrganisationProvider';

export function BoardReportPreview({ range }: { range: DateRange }) {
    const organisation = useOrganisation();

    const { loading, ...data } = useBoardReportData(
        organisation?.selected?.organisation,
        organisation?.selected?.entity,
        range.from!,
        range.to!
    );

    return (
        <PDFViewer style={{ width: '100%', height: '100vh' }}>
            <BoardReport {...data} from={range.from!} to={range.to!} />
        </PDFViewer>
    );
}

export function BoardReportDownloadLink({
    range,
    className
}: {
    range: DateRange;
    className?: (loading: boolean) => string;
}) {
    const organisation = useOrganisation();

    const { loading, ...data } = useBoardReportData(
        organisation?.selected?.organisation,
        organisation?.selected?.entity,
        range.from!,
        range.to!
    );

    return (
        <PDFDownloadLink
            className={className?.(loading)}
            document={<BoardReport {...data} from={range.from!} to={range.to!} />}
            onClick={() =>
                posthog.capture('report downloaded', {
                    to: range.to!,
                    from: range.from!,
                    type: 'board',
                    organisation: organisation?.selected?.organisation.name,
                    entity: organisation?.selected?.entity.name
                })
            }>
            Download
        </PDFDownloadLink>
    );
}
