'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import useSearch from '@/api/hooks/useSearch';
import { useOrganisation } from '@/components/OrganisationProvider';
import { Activity2 } from '@quarterback/types';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import {
    Bars3BottomLeftIcon,
    FlagIcon as UnflaggedIcon,
    LinkIcon
} from '@heroicons/react/24/outline';
import { FlagIcon as FlaggedIcon } from '@heroicons/react/24/solid';
import Tooltip from '@/components/Tooltip';
import useFlagActivityMutation from '@/api/hooks/mutations/useFlagActivityMutation';
import classNames from 'classnames';
import DateRangePicker from '@/components/DateRangePicker';
import { DateRange } from 'react-day-picker';
import { formatWithTimeZone } from '@/util/date';

const DEBUG_SIMILARITY = false;

function SearchResultRow({
    activity,
    similarity
}: {
    activity: Activity2;
    similarity: number;
}) {
    const organisation = useOrganisation();

    const { flag, unflag } = useFlagActivityMutation(organisation.selected?.organisation);
    const flagged = useMemo(
        () =>
            activity.flags?.some(
                (it) => it.organisation === organisation.selected?.organisation.id
            ) ?? false,
        [activity, organisation]
    );

    const FlagIcon = flagged ? FlaggedIcon : UnflaggedIcon;

    return (
        <div className="flex flex-col bg-white rounded-lg border p-4">
            <div className="flex flex-ro gap-x-2">
                <ActivitySourceIcon
                    activity={activity}
                    className="size-8 rounded-full self-center object-cover"
                />
                <div className="flex-1">
                    <div className="text-xs font-medium">{activity.author?.name}</div>
                    <div className="text-sm font-semibold">
                        {activity.title}
                        {DEBUG_SIMILARITY ? ` (${similarity.toFixed(4)})` : ''}
                    </div>
                </div>
                <div>
                    <span className="text-xs text-gray-500">
                        {formatWithTimeZone(activity.posted!, 'd MMM, yyyy')}
                    </span>
                </div>
            </div>

            {activity.body ? (
                <div className="my-2">
                    <span className="text-sm text-gray-500 whitespace-pre-line">
                        {activity.body}
                    </span>
                </div>
            ) : null}

            <div className="flex flex-row justify-end">
                <Tooltip text="Open in new tab">
                    <a
                        href={activity.url}
                        rel="noopener noreferrer"
                        target="_blank"
                        className="p-1.5 rounded-md hover:bg-gray-200"
                        onClick={async () => {
                            if (flagged) {
                                await unflag(new URLSearchParams({ id: activity.id! }));
                            } else {
                                await flag([{ activity: activity.id! }]);
                            }
                        }}>
                        <LinkIcon className={classNames('size-4 text-gray-600')} />
                    </a>
                </Tooltip>
                <Tooltip text={`${flagged ? 'Unflag' : 'Flag'} activity`}>
                    <button
                        className="p-1.5 rounded-md hover:bg-gray-200"
                        onClick={async () => {
                            if (flagged) {
                                await unflag(new URLSearchParams({ id: activity.id! }));
                            } else {
                                await flag([{ activity: activity.id! }]);
                            }
                        }}>
                        <FlagIcon
                            className={classNames('size-4', {
                                'text-red-500': flagged,
                                'text-gray-600': !flagged
                            })}
                        />
                    </button>
                </Tooltip>
            </div>
        </div>
    );
}

export default function Search() {
    const organisation = useOrganisation();

    const params = useSearchParams();
    const query = params.get('query');

    const [sort, setSort] = useState<'relevance' | 'newest' | 'oldest'>('relevance');
    const [range, setRange] = useState<DateRange>({ from: undefined, to: undefined });

    const { data: results = [], isLoading } = useSearch(
        organisation?.selected?.organisation,
        organisation?.selected?.entity,
        range.from,
        range.to,
        query ?? ''
    );

    const sortedResults = useMemo(() => {
        return [...results].sort((a, b) => {
            switch (sort) {
                case 'relevance':
                    return b.similarity - a.similarity;
                case 'newest':
                    return (
                        new Date(b.activity.posted!).valueOf() -
                        new Date(a.activity.posted!).valueOf()
                    );
                case 'oldest':
                    return (
                        new Date(a.activity.posted!).valueOf() -
                        new Date(b.activity.posted!).valueOf()
                    );
            }
        });
    }, [results, sort]);

    return (
        <div className="py-4 px-8">
            <div className="flex flex-row justify-between items-center">
                <h2 className="text-lg font-semibold leading-7 text-gray-900 sm:truncate sm:text-xl sm:tracking-tight">
                    Search results for &ldquo;{query}&rdquo;
                </h2>
                <div className="flex flex-row gap-x-2">
                    <div className="relative">
                        <select
                            id="location"
                            name="location"
                            value={sort}
                            onChange={(e) =>
                                setSort(
                                    e.target.value as 'relevance' | 'newest' | 'oldest'
                                )
                            }
                            className="block w-full rounded-md border-0 py-1.5 pl-9 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            <option value="relevance">Relevance</option>
                            <option value="newest">Newest</option>
                            <option value="oldest">Oldest</option>
                        </select>
                        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-2">
                            <Bars3BottomLeftIcon className="size-5 text-gray-400" />
                        </span>
                    </div>
                    <div>
                        <DateRangePicker range={range} setRange={setRange} />
                    </div>
                </div>
            </div>
            {isLoading ? (
                <div className="mt-16 flex flex-row justify-center">
                    <div
                        className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] text-indigo-600"
                        role="status">
                        <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                            Loading...
                        </span>
                    </div>
                </div>
            ) : null}
            <div className="mt-6 flex flex-col gap-y-4">
                {!isLoading && sortedResults.length === 0 ? (
                    <span className="italic text-center">No activities found</span>
                ) : null}
                {sortedResults.map((result) => (
                    <SearchResultRow
                        key={result.activity.id}
                        activity={result.activity}
                        similarity={result.similarity}
                    />
                ))}
            </div>
        </div>
    );
}
