'use client';

import ActivityChart from '@/components/charts/activity';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import { startOfDay, sub } from 'date-fns';
import DateRangePicker from '@/components/DateRangePicker';
import { useOrganisation } from '@/components/OrganisationProvider';
import { ChevronRightIcon, LinkIcon } from '@heroicons/react/16/solid';
import Link from 'next/link';
import React, { useMemo, useState } from 'react';
import useActivities from '@/api/hooks/useActivities';
import { Activity } from '@quarterback/types';
import useFollowers from '@/api/hooks/useFollowers';
import ActivityPie from '@/components/charts/activity-pie';
import { engagement } from '@/util/useActivitySummary';
import useBroadcasts from '@/api/hooks/useBroadcasts';
import useChatter from '@/api/hooks/useChatter';
import SentimentChart from '@/components/charts/sentiment';
import { DateRange } from 'react-day-picker';
import Metrics from '@/app/(authenticated)/investors/dashboard/Metrics';
import Chatter from '@/app/(authenticated)/investors/dashboard/Chatter';
import ActivityRow from '@/app/(authenticated)/investors/dashboard/ActivityRow';
import { ParentSize } from '@visx/responsive';
import { useRouter } from 'next/navigation';
import Sidebar from '@/app/(authenticated)/investors/dashboard/Sidebar';
import Drawer from '@/components/ui/Drawer';
import ActivityDetailsCard from '@/components/ActivityDetailsCard';

export default function Dashboard() {
    const router = useRouter();

    const [range, setRange] = useState<DateRange>({
        from: startOfDay(sub(new Date(), { months: 1 })),
        to: startOfDay(new Date())
    });

    const [viewing, setViewing] = useState<Activity | undefined>(undefined);

    const organisation = useOrganisation();

    const { data: activities = [] } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: followers = { twitter: [], linkedIn: [] } } = useFollowers(
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: timeSeries } = useTimeSeries(
        organisation?.selected
            ? {
                  symbol: organisation.selected.entity.symbol,
                  exchange: organisation.selected.entity.exchange
              }
            : undefined,
        range.from!,
        range.to!
    );

    const broadcasts = useBroadcasts(activities);
    const chatter = useChatter(activities);

    const topBroadcasts = useMemo(() => {
        return broadcasts
            .sort((a, b) => {
                return (engagement(b) ?? 0) - (engagement(a) ?? 0);
            })
            .slice(0, 5);
    }, [broadcasts]);

    const topChatter = useMemo(() => {
        return chatter
            .sort((a, b) => {
                return (engagement(b) ?? 0) - (engagement(a) ?? 0);
            })
            .slice(0, 5);
    }, [chatter]);

    function handleActivityChartClick(datetime: string) {
        router.push(`/investors/activities?from=${datetime}&to=${datetime}`);
    }

    return (
        <div>
            <div className="p-4 mr-96">
                <div className="md:flex md:items-center md:justify-between">
                    <div className="mt-4 flex items-center md:ml-4 md:mt-0">
                        <DateRangePicker range={range} setRange={setRange} required />
                    </div>
                </div>

                <div className="grid grid-cols-3 gap-x-4 mt-4">
                    <Metrics
                        className="p-4 bg-white border rounded-md col-span-2"
                        from={range.from!}
                        to={range.to!}
                    />
                    <Chatter
                        chatter={chatter}
                        followers={followers}
                        className="p-4 bg-white border rounded-md"
                    />
                </div>

                <div className="w-full bg-white p-4 border mt-4 rounded-lg">
                    <span className="font-semibold">Share price vs Activities</span>
                    <div className="h-[32rem]">
                        <ParentSize>
                            {({ width, height }) => (
                                <ActivityChart
                                    width={width}
                                    height={height}
                                    entity={organisation?.selected?.entity}
                                    activities={activities}
                                    followers={followers}
                                    timeSeries={[...(timeSeries?.values ?? [])].sort(
                                        (a, b) => (a.datetime < b.datetime ? -1 : 1)
                                    )}
                                    onClick={handleActivityChartClick}
                                />
                            )}
                        </ParentSize>
                    </div>
                    <div className="flex justify-center gap-x-4 -mt-6">
                        <div className="flex gap-x-2 items-center">
                            <div
                                className="size-3 rounded-full"
                                style={{ background: '#56B5A7' }}
                            />
                            <span className="text-sm">Publication</span>
                        </div>
                        <div className="flex gap-x-2 items-center">
                            <div
                                className="size-3 rounded-full"
                                style={{ background: '#4E7FEE' }}
                            />
                            <span className="text-sm">Chatter</span>
                        </div>
                        <div className="flex gap-x-2 items-center">
                            <div
                                className="size-3 rounded-full"
                                style={{ background: '#845DEE' }}
                            />
                            <span className="text-sm">Broadcast</span>
                        </div>
                        <div className="flex gap-x-2 items-center">
                            <div
                                className="size-3 rounded-full"
                                style={{ background: '#DFB649' }}
                            />
                            <span className="text-sm">Announcement</span>
                        </div>
                        <div className="flex gap-x-2 items-center">
                            <div
                                className="size-3 rounded-full"
                                style={{ background: '#9aa2a7' }}
                            />
                            <span className="text-sm">Trade Volume</span>
                        </div>
                    </div>
                </div>

                <div className="w-full mt-4 grid grid-cols-4 gap-x-4">
                    <div className="bg-white border rounded-md p-4 col-span-2 flex flex-col">
                        <span className="font-semibold">Sentiment</span>
                        <SentimentChart
                            entity={organisation.selected?.entity}
                            activities={chatter.filter((it) => !!it.sentiment)}
                            from={range.from!}
                            to={range.to!}
                        />
                    </div>
                    <div className="bg-white border rounded-md p-4">
                        <span className="font-semibold">Activities by Source</span>
                        <ActivityPie
                            entity={organisation.selected?.entity}
                            activities={activities}
                            groupBy="source"
                        />
                    </div>
                    <div className="bg-white border rounded-md p-4">
                        <span className="font-semibold">Activities by Format</span>
                        <ActivityPie
                            entity={organisation.selected?.entity}
                            activities={activities}
                            groupBy="format"
                        />
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-x-4 mt-4">
                    <div className="p-4 bg-white border rounded-md">
                        <div className="flex justify-between">
                            <span className="font-semibold">
                                Your top engaged broadcasts
                            </span>
                            <div className="flex items-center text-indigo-600">
                                <Link
                                    href="/investors/activities"
                                    className="text-sm font-medium">
                                    See all
                                </Link>
                                <ChevronRightIcon className="size-4" />
                            </div>
                        </div>
                        <div className="divide-gray-200 divide-y mt-2">
                            {topBroadcasts.map((activity) => (
                                <ActivityRow
                                    key={activity.id}
                                    activity={activity}
                                    onClick={() => setViewing(activity)}
                                />
                            ))}
                        </div>
                    </div>
                    <div className="p-4 bg-white border rounded-md">
                        <div className="flex justify-between">
                            <span className="font-semibold">
                                Your top engaged chatter
                            </span>
                            <div className="flex items-center text-indigo-600">
                                <Link
                                    href="/investors/activities"
                                    className="text-sm font-medium">
                                    See all
                                </Link>
                                <ChevronRightIcon className="size-4" />
                            </div>
                        </div>
                        <div className="divide-gray-200 divide-y mt-2">
                            {topChatter.map((activity) => (
                                <ActivityRow
                                    key={activity.id}
                                    activity={activity}
                                    onClick={() => setViewing(activity)}
                                />
                            ))}
                        </div>
                    </div>
                </div>
                <Drawer open={!!viewing} setOpen={() => setViewing(undefined)}>
                    {viewing && (
                        <div className="p-4">
                            <ActivityDetailsCard
                                activity={viewing}
                                className="border rounded-lg p-4"
                            />
                            <div className="flex items-center justify-start my-2">
                                <a
                                    href={viewing.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="hover:bg-gray-100 p-1 rounded-md flex items-center text-sm font-medium text-indigo-600 gap-x-1">
                                    <LinkIcon className="size-4" />
                                    <span>Open in new tab</span>
                                </a>
                            </div>
                        </div>
                    )}
                </Drawer>
            </div>
            <div className="p-4">
                <Sidebar />
            </div>
        </div>
    );
}
