import { useSentimentBands } from '@/util/sentiment';
import { ArrowDownIcon, ArrowUpIcon, ChevronRightIcon } from '@heroicons/react/16/solid';
import { Activity, Followers } from '@quarterback/types';
import { isDefined } from '@quarterback/util';
import classNames from 'classnames';
import Link from 'next/link';
import { useMemo } from 'react';

export default function Chatter({
    chatter,
    followers,
    className
}: {
    chatter: Array<Activity>;
    followers: Followers;
    className?: string;
}) {
    const authors = useMemo(() => {
        return new Set(
            chatter
                .map((activity) => {
                    switch (activity.type) {
                        case 'hotcopper':
                        case 'tweet':
                        case 'reddit':
                        case 'redditComment':
                            return activity.author?.userId;
                        case 'linkedIn':
                            return activity.author?.name;
                        case 'news':
                            return activity.source.name;
                        default:
                            return undefined;
                    }
                })
                .filter(isDefined)
        );
    }, [chatter]);

    const { now: currentFollowers, previous: previousFollowers } = useMemo(() => {
        return {
            now:
                +(followers.twitter?.[0]?.followers ?? 0) +
                +(followers.linkedIn?.[0]?.followers ?? 0),
            previous:
                +(
                    followers?.twitter?.[1]?.followers ??
                    followers?.twitter?.[0]?.followers ??
                    0
                ) +
                +(
                    followers?.linkedIn?.[1]?.followers ??
                    followers?.linkedIn?.[0]?.followers ??
                    0
                )
        };
    }, [followers?.linkedIn, followers?.twitter]);

    const followersChange = useMemo(() => {
        return currentFollowers - previousFollowers;
    }, [currentFollowers, previousFollowers]);

    const [red, yellow, gray, green] = useSentimentBands(chatter);

    return (
        <div className={classNames(className ?? '', 'flex flex-col gap-y-4')}>
            <div className="flex justify-between">
                <span className="font-medium">Chatter</span>
                <Link
                    href="/investors/inbox"
                    className="flex items-center text-gray-400 text-sm hover:underline hover:text-gray-500">
                    View all chatter
                    <ChevronRightIcon className="size-4" />
                </Link>
            </div>

            <div className="overflow-hidden flex rounded-md">
                <div
                    className="h-4 bg-red-500"
                    style={{ width: `${(red * 100).toFixed(2)}%` }}
                />
                <div
                    className="h-4 bg-yellow-300"
                    style={{ width: `${(yellow * 100).toFixed(2)}%` }}
                />
                <div
                    className="h-4 bg-slate-300"
                    style={{ width: `${(gray * 100).toFixed(2)}%` }}
                />
                <div
                    className="h-4 bg-green-400"
                    style={{ width: `${(green * 100).toFixed(2)}%` }}
                />
            </div>

            <div className="grid grid-cols-3 gap-x-4">
                <div className="flex flex-col gap-y-1">
                    <span className="text-3xl font-medium">{chatter.length}</span>
                    <span className="text-gray-400">Activities</span>
                </div>

                <div className="flex flex-col gap-y-1">
                    <span className="text-3xl font-medium">{authors.size}</span>
                    <span className="text-gray-400">Authors</span>
                </div>

                <div className="flex flex-col gap-y-1">
                    <div className="flex items-center gap-x-1">
                        <span className="text-3xl font-medium">
                            {currentFollowers ?? 'N/A'}
                        </span>
                        {followersChange !== 0 ? (
                            <div
                                className={classNames('flex items-center', {
                                    'text-red-700': followersChange < 0,
                                    'text-green-700': followersChange > 0
                                })}>
                                {followersChange > 0 ? (
                                    <ArrowUpIcon className="size-3" />
                                ) : null}
                                {followersChange < 0 ? (
                                    <ArrowDownIcon className="size-3" />
                                ) : null}
                                <span className="text-xs font-medium">
                                    {Math.abs(followersChange)}
                                </span>
                            </div>
                        ) : null}
                    </div>
                    <span className="text-gray-400 mr-1">Followers</span>
                </div>
            </div>
        </div>
    );
}
