import { Activity } from '@quarterback/types';
import useActivitySummary from '@/util/useActivitySummary';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import { HandThumbUpIcon } from '@heroicons/react/24/outline';
import React from 'react';
import { isBroadcast } from '@/api/hooks/useBroadcasts';
import { formatWithTimeZone } from '@/util/date';

export default function ActivityRow({
    activity,
    onClick
}: {
    activity: Activity;
    onClick?: () => void;
}) {
    const { description, engagement, title, author } = useActivitySummary(activity);
    const posted = formatWithTimeZone(activity.posted);

    return (
        <div
            className="relative flex items-center gap-x-2 p-2 cursor-pointer hover:bg-gray-50"
            onClick={onClick}>
            <ActivitySourceIcon
                activity={activity}
                className="h-8 w-8 flex-none rounded-full bg-gray-50 object-cover"
            />
            <div className="flex-1 flex flex-col min-w-0">
                <span className="font-medium text-sm">{author}</span>
                <p className="flex-grow-0 text-sm text-gray-500 truncate">
                    {isBroadcast(activity) ? (title ?? description) : description}
                </p>
            </div>
            <div className="flex flex-shrink-0 flex-col items-end justify-start gap-y-1">
                <span className="text-sm text-gray-400">{posted}</span>
                <div className="flex items-center gap-x-2">
                    <HandThumbUpIcon className="size-4 text-gray-400" />
                    <span className="text-sm text-gray-500">{engagement ?? 0}</span>
                </div>
            </div>
        </div>
    );
}
