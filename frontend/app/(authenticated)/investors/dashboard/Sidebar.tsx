import useInsightsMutation from '@/api/hooks/mutations/useInsightsMutation';
import useActivitiesCount from '@/api/hooks/useActivitiesCount';
import { useOrganisation } from '@/components/OrganisationProvider';
import Tooltip from '@/components/Tooltip';
import { ChatBubbleLeftRightIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { ListedEntity, Organisation } from '@quarterback/types';
import classNames from 'classnames';
import { sub } from 'date-fns';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import posthog from 'posthog-js';
import React, { useEffect, useMemo, useState } from 'react';
import { remark } from 'remark';
import html from 'remark-html';
import './ai-summary.scss';

function SidebarAlert({
    title,
    body,
    cta,
    href,
    color,
    icon: Icon
}: {
    href: string;
    title: string;
    body: string;
    cta: string;
    color: 'violet' | 'red' | 'white';
    icon: React.ComponentType<{ className: string | undefined }>;
}) {
    return (
        <>
            <div
                className={classNames('flex flex-col gap-y-3 p-3 rounded-md', {
                    'bg-white border border-gray-200': color === 'white',
                    'bg-red-100': color === 'red',
                    'bg-violet-100': color === 'violet'
                })}>
                <div className="flex items-center gap-x-2">
                    <Icon
                        className={classNames('size-6 p-1 rounded-md', {
                            'bg-indigo-300 text-indigo-800': color === 'white',
                            'bg-red-300 text-red-800': color === 'red',
                            'bg-violet-300 text-violet-800': color === 'violet'
                        })}
                    />
                    <span className="">{title}</span>
                </div>

                <span className="text-sm text-gray-500">{body}</span>
                <Link
                    href={href}
                    className={classNames(
                        'text-sm rounded-md py-1 px-2 text-center text-gray-900',
                        {
                            'border border-gray-200 hover:bg-gray-100': color === 'white',
                            'bg-red-300 hover:bg-red-300/50': color === 'red',
                            'bg-violet-300 hover:bg-violet-300/50': color === 'violet'
                        }
                    )}>
                    {cta}
                </Link>
            </div>
        </>
    );
}

export default function Sidebar() {
    const organisation = useOrganisation();

    const selectedOrganisation = useMemo(() => {
        if (organisation?.selected) {
            return organisation.selected;
        }
    }, [organisation.selected]);

    const { data: unread } = useActivitiesCount(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        { read: false, provenance: 'chatter' }
    );

    const range = useMemo(() => {
        return { from: sub(new Date(), { days: 30 }), to: new Date() };
    }, []);

    const { getInsight } = useInsightsMutation({
        organisation: selectedOrganisation?.organisation!,
        entity: selectedOrganisation?.entity!
    });

    const [summary, setSummary] = useState<string | undefined>();
    const [summaryError, setSummaryError] = useState<Error | undefined>();
    const [summaryLoading, setSummaryLoading] = useState<boolean>(false);

    useEffect(() => {
        async function fetchInsight() {
            if (selectedOrganisation && range) {
                setSummaryLoading(true);

                const { data, error } = await getInsight({
                    organisation: selectedOrganisation?.organisation?.id!,
                    symbol: selectedOrganisation?.entity?.symbol!,
                    exchange: selectedOrganisation?.entity?.exchange!,
                    from: range.from!,
                    to: range.to!,
                    type: 'mixed'
                });
                setSummaryLoading(false);
                setSummary(data);
                setSummaryError(error);
            }
        }

        fetchInsight();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedOrganisation, range]);

    return (
        <div className="bg-white border-l w-96 top-0 bottom-0 lg:right-0 overflow-y-auto fixed p-4 flex flex-col gap-y-2 mt-[60px]">
            <SidebarAlert
                href="/investors/inbox"
                title="New mentions"
                body={`You have ${unread} new mentions in your inbox`}
                cta="Go to inbox"
                color="violet"
                icon={ChatBubbleLeftRightIcon}
            />
            <AIInsight
                markdown={
                    summaryError
                        ? '*Something went wrong while generating AI insights*'
                        : summary
                }
                loading={summaryLoading}
                selected={selectedOrganisation!}
            />
        </div>
    );
}

function AIInsight({
    markdown,
    loading,
    selected
}: {
    markdown: string | undefined;
    loading: boolean;
    selected: { entity: ListedEntity; organisation: Organisation };
}) {
    const router = useRouter();
    const [innerHtml, setInnerHtml] = useState('');

    async function processMarkdown(markdown: string) {
        setInnerHtml(
            await remark()
                .use(html)
                .process(markdown)
                .then((it) => it.toString())
        );
    }

    useEffect(() => {
        if (markdown) {
            processMarkdown(markdown).then();
        } else {
            setInnerHtml('');
        }
    }, [markdown]);

    function handleClick(event: React.MouseEvent<HTMLDivElement, MouseEvent>) {
        const element = event.target as HTMLElement;

        if (element.tagName === 'STRONG') {
            const text = element.innerText;

            posthog.capture('ai insight clicked', {
                term: text,
                organisation: selected?.organisation?.name,
                entity: selected?.entity.name
            });

            router.push(`/investors/search?query=${encodeURIComponent(text)}`);
        }
    }

    return (
        <div className="bg-white border border-gray-200 rounded-md p-4">
            <div className="flex flex-row items-center justify-between">
                <div className="flex flex-row items-center gap-x-2">
                    <Tooltip text="AI generated insights">
                        <div className="p-1 w-fit rounded-lg bg-gradient-to-r from-sky-400 to-purple-400 text-white cursor-help">
                            <SparklesIcon className="size-4" />
                        </div>
                    </Tooltip>
                    <h2 className="text-gray-700 font-medium">Insights</h2>
                </div>
            </div>
            {loading ? (
                <div className="my-4">
                    <div className="flex space-x-2 justify-center items-center">
                        <span className="sr-only">Loading...</span>
                        <div className="size-1.5 bg-indigo-600 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                        <div className="size-1.5 bg-indigo-600 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                        <div className="size-1.5 bg-indigo-600 rounded-full animate-bounce"></div>
                    </div>
                </div>
            ) : null}
            {innerHtml && !loading && (
                <div
                    onClick={handleClick}
                    className="mt-2 ai-summary text-gray-800"
                    dangerouslySetInnerHTML={{ __html: innerHtml }}></div>
            )}
        </div>
    );
}
