import useRisk from '@/api/hooks/useRisk';
import useTimeSeries from '@/api/hooks/useTimeSeries';
import ChangeIndicator from '@/components/ChangeIndicator';
import AbnormalReturnsChart from '@/components/charts/abnormal-return';
import useCombinedQuotes from '@/components/hooks/useCombinedQuotes';
import { useOrganisation } from '@/components/OrganisationProvider';
import Tooltip from '@/components/Tooltip';
import { ParentSize } from '@visx/responsive';
import classNames from 'classnames';
import { useMemo } from 'react';

interface Props {
    className?: string;
    from: Date;
    to: Date;
}

export default function Metrics({ className, from, to }: Props) {
    const organisation = useOrganisation();

    const { data: timeSeries } = useTimeSeries(organisation?.selected?.entity, from, to);
    const { data: indexSeries } = useTimeSeries(
        { symbol: 'AXJO', exchange: 'ASX' },
        from,
        to
    );

    const { data: risk } = useRisk(organisation?.selected?.entity, {
        symbol: 'AXJO',
        exchange: 'ASX'
    });

    const combinedQuotes = useCombinedQuotes(indexSeries, timeSeries);

    const sharePriceChange = useMemo(() => {
        if (timeSeries && timeSeries.values.length) {
            const firstClose = timeSeries.values[0].close;
            const lastClose = timeSeries.values[timeSeries.values.length - 1].close;

            return (lastClose / firstClose - 1) * 100;
        } else {
            return undefined;
        }
    }, [timeSeries]);

    const sharePrice = useMemo(() => {
        if (timeSeries) {
            return timeSeries.values[timeSeries.values.length - 1].close;
        } else {
            return undefined;
        }
    }, [timeSeries]);

    const cumAbnormalReturns = useMemo((): number | undefined => {
        if (!risk || combinedQuotes.length < 2) return undefined; // Ensure valid data

        return combinedQuotes.reduce((cum, { index, stock }, i) => {
            if (i === 0) return cum; // Skip first entry since there's no previous day

            const prevQuote = combinedQuotes[i - 1];

            const stockReturn = Math.log(stock.close / prevQuote.stock.close);
            const indexReturn = Math.log(index.close / prevQuote.index.close);

            return cum + (stockReturn - (risk.intercept + risk.slope * indexReturn));
        }, 0);
    }, [combinedQuotes, risk]);

    return (
        <div className={classNames(className ?? '', 'flex gap-x-4')}>
            <div className="flex flex-col gap-y-4 flex-shrink-0">
                <span className="font-medium">Metrics</span>

                {cumAbnormalReturns ? (
                    <Tooltip
                        text={
                            <>
                                <p>Cumulative Abnormal Returns (CAR)</p>
                                <p className="pt-2">
                                    This measures how a company’s share price deviates
                                    from expected market performance, based on the ASX 200
                                    Index (AXJO) over a rolling 250-day period.
                                </p>
                            </>
                        }>
                        <div className="flex items-center gap-x-2 cursor-help">
                            <span
                                className={classNames(
                                    'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                                    {
                                        'text-gray-600 bg-gray-100':
                                            cumAbnormalReturns === 0,
                                        'text-green-700 bg-green-100':
                                            cumAbnormalReturns > 0,
                                        'text-red-700 bg-red-100': cumAbnormalReturns < 0
                                    }
                                )}>
                                {(cumAbnormalReturns * 100).toFixed(2)}%
                            </span>

                            <span className="text-gray-400 text-sm">
                                Cumulative abnormal return
                            </span>
                        </div>
                    </Tooltip>
                ) : undefined}

                <div className="flex flex-col gap-y-2">
                    <span className="text-4xl font-medium">
                        ${sharePrice ? sharePrice.toFixed(3) : ''}
                    </span>

                    {sharePriceChange !== undefined && (
                        <div className="flex items-center gap-x-2">
                            <span className="text-gray-400 text-sm">Share Price</span>
                            <ChangeIndicator
                                value={sharePriceChange}
                                label={`${Math.abs(sharePriceChange).toFixed(2)}%`}
                            />
                        </div>
                    )}
                </div>
            </div>
            <div className="flex-1 flex-shrink flex flex-col">
                <div className="flex-1 relative">
                    <div className="absolute top-0 left-0 right-0 bottom-0">
                        {risk && (
                            <ParentSize>
                                {({ width, height }) => (
                                    <AbnormalReturnsChart
                                        width={width}
                                        height={height}
                                        risk={risk}
                                        quotes={combinedQuotes}
                                    />
                                )}
                            </ParentSize>
                        )}
                    </div>
                </div>
                <div className="flex gap-x-3 mt-2">
                    <div className="flex items-center gap-x-1">
                        <div
                            className="w-2 h-2 rounded-full bg-indigo-200"
                            style={{ background: '#63ABFD' }}
                        />
                        <span className="text-xs text-gray-400">Share price</span>
                    </div>
                    <div className="flex items-center gap-x-1">
                        <div
                            className="w-2 h-2 rounded-full"
                            style={{ background: '#A155B9' }}
                        />
                        <span className="text-xs text-gray-400">Abnormal return</span>
                    </div>
                </div>
            </div>
        </div>
    );
}
