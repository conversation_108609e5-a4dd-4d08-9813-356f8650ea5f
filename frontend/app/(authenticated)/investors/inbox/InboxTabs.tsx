import classNames from 'classnames';
import React from 'react';

export type Tab = 'all' | 'unread' | 'flagged' | 'archived';

export default function InboxTabs({
    unread,
    tab,
    setTab
}: {
    unread: number;
    tab: string;
    setTab: React.Dispatch<React.SetStateAction<Tab>>;
}) {
    const tabs: Array<{ name: string; id: Tab }> = [
        { name: 'All', id: 'all' },
        { name: `Unread (${unread})`, id: 'unread' },
        { name: 'Flagged', id: 'flagged' },
        { name: 'Archived', id: 'archived' }
    ];

    return (
        <div className="border-b p-2 bg-white">
            <div className="sm:hidden">
                <label htmlFor="tabs" className="sr-only">
                    Select a tab
                </label>
                {/* Use an "onChange" listener to redirect the user to the selected tab URL. */}
                <select
                    id="tabs"
                    name="tabs"
                    className="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                    onChange={(e) => setTab(e.target.value as Tab)}
                    defaultValue={tabs.find((it) => it.id === tab)?.id}>
                    {tabs.map((it) => (
                        <option key={it.id} value={it.id}>
                            {it.name}
                        </option>
                    ))}
                </select>
            </div>
            <div className="hidden sm:block">
                <nav className="flex space-x-2" aria-label="Tabs">
                    {tabs.map((it) => (
                        <a
                            key={it.id}
                            onClick={() => setTab(it.id)}
                            className={classNames(
                                it.id === tab
                                    ? 'bg-indigo-100 text-indigo-700'
                                    : 'text-gray-500 hover:text-gray-700',
                                'rounded-md px-2 py-1 text-sm font-medium cursor-pointer'
                            )}>
                            {it.name}
                        </a>
                    ))}
                </nav>
            </div>
        </div>
    );
}
