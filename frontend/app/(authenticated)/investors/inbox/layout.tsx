'use client';

import React, { useEffect, useMemo, useState } from 'react';

import { useParams, usePathname, useRouter } from 'next/navigation';
import { useOrganisation } from '@/components/OrganisationProvider';
import { isChatter } from '@/api/hooks/useChatter';
import InboxActivity from '@/app/(authenticated)/investors/inbox/InboxActivity';
import InboxTabs, { Tab } from '@/app/(authenticated)/investors/inbox/InboxTabs';
import useActivityReadMutation from '@/api/hooks/mutations/useActivityReadMutation';
import useActivitiesCount from '@/api/hooks/useActivitiesCount';
import usePaginatedActivities from '@/api/hooks/usePaginatedActivities';
import { Activity } from '@quarterback/types';

export default function Layout({ children }: React.PropsWithChildren) {
    const router = useRouter();
    const pathname = usePathname();

    const organisation = useOrganisation();

    const { id } = useParams();

    const [tab, setTab] = useState<Tab>('all');
    const [sort, setSort] = useState<'newest' | 'oldest'>('newest');

    const [recentlyReadActivities, setRecentlyReadActivities] = useState<Array<Activity>>(
        []
    );

    const options = useMemo(() => {
        switch (tab) {
            case 'all':
                return { archived: false };
            case 'unread':
                return { read: false, archived: false };
            case 'flagged':
                return { flagged: true, archived: false };
            case 'archived':
                return { archived: true };
        }
    }, [tab]);

    const { read, unread } = useActivityReadMutation();

    const {
        data: activities,
        setSize,
        isLoading,
        hasReachedEnd
    } = usePaginatedActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        options
    );

    const { data: unreadCount } = useActivitiesCount(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        { read: false, provenance: 'chatter' }
    );

    const sortedActivities = useMemo(() => {
        const ids = new Set(recentlyReadActivities?.map((it) => it.id) ?? []);

        return [
            ...(activities ?? []).filter((it) => !ids.has(it.id)),
            ...recentlyReadActivities
        ]
            .filter((activity) => isChatter(activity))
            .sort((a, b) => {
                switch (sort) {
                    case 'oldest':
                        return (
                            a.posted.valueOf() - b.posted.valueOf() ||
                            a.id!.localeCompare(b.id!)
                        );
                    case 'newest':
                        return (
                            b.posted.valueOf() - a.posted.valueOf() ||
                            a.id!.localeCompare(b.id!)
                        );
                }
            });
    }, [activities, sort, organisation, recentlyReadActivities]);

    useEffect(() => {
        setRecentlyReadActivities([]);
    }, [tab, organisation]);

    useEffect(() => {
        if (
            (!id || Array.isArray(id) || !sortedActivities.some((it) => it.id === id)) &&
            sortedActivities.length
        ) {
            router.replace(`/investors/inbox/${sortedActivities[0].id}`);
        }
    }, [id, router, sortedActivities]);

    useEffect(() => {
        const activity = sortedActivities.find((it) => it.id === id);

        if (id && !Array.isArray(id) && activity && !activity.read) {
            setRecentlyReadActivities((recentlyRead) => [
                ...recentlyRead,
                { ...activity, read: true }
            ]);
            read([{ activity: id }]).then();
        }
    }, [id, sortedActivities, read]);

    return (
        <div className="flex overflow-hidden">
            <div className="bg-white border-r w-[28rem] top-0 bottom-0 lg:left-[22rem] overflow-y-scroll fixed">
                <div className="sticky top-0 z-50">
                    <InboxTabs unread={unreadCount ?? 0} tab={tab} setTab={setTab} />
                    <div className="py-2 px-3 bg-gray-50 text-gray-600 text-sm  border-b flex items-center justify-between gap-x-1">
                        <select
                            value={sort}
                            onChange={(e) =>
                                setSort(e.target.value as 'newest' | 'oldest')
                            }
                            className="border-none outline-none focus:ring-0 focus:outline-none text-sm bg-gray-50 py-0 pl-0 pr-8">
                            <option value="newest">Newest</option>
                            <option value="oldest">Oldest</option>
                        </select>
                        {/*<span className="text-xs">Mark all as read</span>*/}
                    </div>
                </div>
                <ul role="list" className="bg-gray-50">
                    {sortedActivities.map((activity, index) => (
                        <InboxActivity
                            key={activity.id}
                            activity={activity}
                            selected={pathname === `/investors/inbox/${activity.id}`}
                        />
                    ))}
                </ul>
                {!hasReachedEnd ? (
                    <div className="px-4 py-2">
                        <button
                            onClick={() => setSize((size) => size + 1)}
                            disabled={isLoading}
                            className="flex w-full items-center justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus-visible:outline-offset-0">
                            Load more
                        </button>
                    </div>
                ) : null}
            </div>
            <div className="flex-1 lg:ml-[28rem] bg-gray-100 min-h-screen">
                {children}
            </div>
        </div>
    );
}
