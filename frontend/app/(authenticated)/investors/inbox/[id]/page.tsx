'use client';

import useActivityReadMutation from '@/api/hooks/mutations/useActivityReadMutation';
import useArchiveActivityMutation from '@/api/hooks/mutations/useArchiveActivityMutation';
import useFlagActivityMutation from '@/api/hooks/mutations/useFlagActivityMutation';
import useActivity from '@/api/hooks/useActivity';
import { useActivityFiles } from '@/api/hooks/useActivityFiles';
import ActivityAction from '@/components/ActivityAction';
import ActivityDetailsCard from '@/components/ActivityDetailsCard';
import ActivityFilesList from '@/components/ActivityFilesList';
import ActivityThread from '@/components/ActivityThread';
import { useOrganisation } from '@/components/OrganisationProvider';
import useActivitySummary from '@/util/useActivitySummary';
import {
    TrashIcon as TrashIconOutline,
    EnvelopeIcon,
    EnvelopeOpenIcon,
    FlagIcon as FlagIconOutline,
    LinkIcon
} from '@heroicons/react/24/outline';
import {
    TrashIcon as TrashIconSolid,
    FlagIcon as FlagIconSolid
} from '@heroicons/react/24/solid';
import { useMemo } from 'react';

export default function ActivityDetails({ params }: { params: { id: string } }) {
    const organisation = useOrganisation();

    const { read, unread } = useActivityReadMutation();
    const { flag, unflag } = useFlagActivityMutation(organisation.selected?.organisation);
    const { archive, unarchive } = useArchiveActivityMutation(
        organisation.selected?.organisation
    );

    const { data: activity } = useActivity(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        params.id
    );
    const { url, title } = useActivitySummary(activity);
    const { files } = useActivityFiles(params.id);

    const threadId: string | undefined = useMemo(() => {
        switch (activity?.type) {
            case 'hotcopper':
                return activity?.thread?.thread ?? undefined;

            case 'tweet':
            case 'linkedIn':
                return activity?.thread ?? undefined;

            default:
                return undefined;
        }
    }, [activity]);

    return (
        <div className="mx-4">
            <div className="m-4 flex items-center space-x-4">
                <h1 className="font-semibold text-lg flex-1">{title}</h1>
                <div className="flex items-center gap-x-2">
                    <ActivityAction
                        icon={activity?.read ? EnvelopeOpenIcon : EnvelopeIcon}
                        tooltip={`Mark as ${activity?.read ? 'unread' : 'read'}`}
                        onClick={async () => {
                            if (activity?.read) {
                                await unread(new URLSearchParams({ id: params.id }));
                            } else {
                                await read([{ activity: params.id }]);
                            }
                        }}
                    />
                    <ActivityAction
                        tooltip={`${activity?.flagged ? 'Un-flag' : 'Flag'} activity`}
                        icon={activity?.flagged ? FlagIconSolid : FlagIconOutline}
                        onClick={async () => {
                            if (activity?.flagged) {
                                await unflag(new URLSearchParams({ id: params.id }));
                            } else {
                                await flag([{ activity: params.id }]);
                            }
                        }}
                    />
                    <ActivityAction
                        tooltip={`${activity?.archived ? 'Un-archive' : 'Archive'} activity`}
                        icon={activity?.archived ? TrashIconSolid : TrashIconOutline}
                        onClick={async () => {
                            if (activity?.archived) {
                                await unarchive(new URLSearchParams({ id: params.id }));
                            } else {
                                await archive([{ activity: params.id }]);
                            }
                        }}
                    />
                </div>
            </div>

            <div className="bg-white">
                {activity && threadId ? (
                    <ActivityThread threadId={threadId} selectedActivity={activity.id} />
                ) : null}

                {activity && !threadId ? (
                    <ActivityDetailsCard
                        activity={activity}
                        className="m-4 p-4"
                        show={{ title: false }}
                    />
                ) : null}
            </div>

            {files?.length > 0 && (
                <>
                    <dt className="text-sm font-semibold text-gray-500 mt-6">
                        Attachments
                    </dt>
                    <div className="mt-3 border border-gray-200 bg-white p-4 rounded-md">
                        <ActivityFilesList files={files} />
                    </div>
                </>
            )}

            <div className="mx-4 py-4">
                <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:underline flex items-center text-sm font-medium text-indigo-600 gap-x-1">
                    <LinkIcon className="size-4" />
                    <span>Open in new tab</span>
                </a>
            </div>
        </div>
    );
}
