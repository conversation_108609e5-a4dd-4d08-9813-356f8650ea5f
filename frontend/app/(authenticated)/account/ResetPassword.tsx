import React, { FormEvent, useRef, useState } from 'react';
import Alert from '@/components/Alert';
import { updatePassword } from '@/app/(authenticated)/account/actions';
import { PasswordError, SessionError } from '@/app/(authenticated)/account/errors';

export default function ResetPassword({ className }: { className?: string }) {
    const [error, setError] = useState('');
    const [message, setMessage] = useState('');

    const formRef = useRef<HTMLFormElement>(null);

    async function handleUpdatePassword(formData: FormData) {
        setError('');
        setMessage('');

        const current = formData.get('current_password')! as string;
        const updated = formData.get('new_password')! as string;
        const confirm = formData.get('confirm_password')! as string;

        if (updated !== confirm) {
            setError('Passwords do not match');
            return;
        }

        try {
            await updatePassword(current, updated);
            setMessage('Password updated successfully');
            formRef.current?.reset();
        } catch (error) {
            if (error instanceof PasswordError) {
                setError('Current password is incorrect');
            } else if (error instanceof SessionError) {
                setError('Something went wrong');
            } else {
                setError('Something went wrong');
            }
        }
    }

    return (
        <form ref={formRef} action={handleUpdatePassword} className={className}>
            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:max-w-xl sm:grid-cols-6">
                {error || message ? (
                    <div className="col-span-full">
                        {error ? (
                            <Alert status="error" text={error} dismiss={() => setError('')} />
                        ) : null}
                        {message ? (
                            <Alert status="success" text={message} dismiss={() => setMessage('')} />
                        ) : null}
                    </div>
                ) : null}

                <div className="col-span-full">
                    <label
                        htmlFor="current-password"
                        className="block text-sm font-medium leading-6">
                        Current password
                    </label>
                    <div className="mt-2">
                        <input
                            id="current-password"
                            name="current_password"
                            type="password"
                            autoComplete="current-password"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div className="col-span-full">
                    <label htmlFor="new-password" className="block text-sm font-medium leading-6">
                        New password
                    </label>
                    <div className="mt-2">
                        <input
                            id="new-password"
                            name="new_password"
                            type="password"
                            autoComplete="new-password"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div className="col-span-full">
                    <label
                        htmlFor="confirm-password"
                        className="block text-sm font-medium leading-6">
                        Confirm password
                    </label>
                    <div className="mt-2">
                        <input
                            id="confirm-password"
                            name="confirm_password"
                            type="password"
                            autoComplete="new-password"
                            required
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
            </div>

            <div className="mt-8 flex">
                <button
                    type="submit"
                    className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                    Save
                </button>
            </div>
        </form>
    );
}
