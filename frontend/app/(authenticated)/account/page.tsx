'use client';

import React from 'react';
import ResetPassword from '@/app/(authenticated)/account/ResetPassword';
import { useUser } from '@/api/hooks/useUser';
import PersonalDetails from '@/app/(authenticated)/account/PersonalDetails';

export default function Account() {
    const { data: user } = useUser();

    return (
        <div className="divide-y divide-gray-200">
            <div className="grid max-w-7xl grid-cols-1 gap-x-8 gap-y-10 px-4 py-16 sm:px-6 md:grid-cols-3 lg:px-8">
                <div>
                    <h2 className="text-base font-semibold leading-7">Profile</h2>
                    <p className="mt-1 text-sm leading-6 text-gray-500">
                        Your profile details, visible to others in your organisation.
                    </p>
                </div>

                {user && <PersonalDetails user={user} className="md:col-span-2" />}
            </div>

            <div className="grid max-w-7xl grid-cols-1 gap-x-8 gap-y-10 px-4 py-16 sm:px-6 md:grid-cols-3 lg:px-8">
                <div>
                    <h2 className="text-base font-semibold leading-7">Change password</h2>
                    <p className="mt-1 text-sm leading-6 text-gray-500">
                        Update the password associated with your account.
                    </p>
                </div>

                <ResetPassword className="md:col-span-2" />
            </div>
        </div>
    );
}
