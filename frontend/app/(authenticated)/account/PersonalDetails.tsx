import { User } from '@quarterback/types';
import React, { useState } from 'react';
import { updateName } from '@/app/(authenticated)/account/actions';
import Alert from '@/components/Alert';

export default function PersonalDetails({ user, className }: { className?: string; user: User }) {
    const [error, setError] = useState('');
    const [message, setMessage] = useState('');

    const [firstName, setFirstName] = useState(user.metadata.first_name ?? '');
    const [lastName, setLastName] = useState(user.metadata.last_name ?? '');

    async function handleUpdateName() {
        setError('');
        setMessage('');

        if (!firstName || !lastName) {
            setError('First name and last name are required');
            return;
        }

        try {
            await updateName(firstName, lastName);
            setMessage('Profile updated successfully');
        } catch (error) {
            setError('Something went wrong');
        }
    }

    return (
        <form action={handleUpdateName} className={className}>
            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:max-w-xl sm:grid-cols-6">
                {error || message ? (
                    <div className="col-span-full">
                        {error ? (
                            <Alert status="error" text={error} dismiss={() => setError('')} />
                        ) : null}
                        {message ? (
                            <Alert status="success" text={message} dismiss={() => setMessage('')} />
                        ) : null}
                    </div>
                ) : null}

                <div className="sm:col-span-3">
                    <label
                        htmlFor="first-name"
                        className="block text-sm font-medium leading-6 text-gray-900">
                        First name
                    </label>
                    <div className="mt-2">
                        <input
                            id="first-name"
                            name="first-name"
                            type="text"
                            autoComplete="given-name"
                            value={firstName}
                            onChange={(e) => setFirstName(e.target.value)}
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>

                <div className="sm:col-span-3">
                    <label htmlFor="last-name" className="block text-sm font-medium leading-6">
                        Last name
                    </label>
                    <div className="mt-2">
                        <input
                            id="last-name"
                            name="last-name"
                            type="text"
                            autoComplete="family-name"
                            value={lastName}
                            onChange={(e) => setLastName(e.target.value)}
                            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                </div>
            </div>

            <div className="mt-8 flex">
                <button
                    type="submit"
                    className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                    Save
                </button>
            </div>
        </form>
    );
}
