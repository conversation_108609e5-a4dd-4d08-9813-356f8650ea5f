'use server';

import { getSSRSessionHelper } from '@/components/getSSRSessionHelper';
import supertokens from 'supertokens-node';
import EmailPassword from 'supertokens-node/recipe/emailpassword';
import UserMetadata from 'supertokens-node/recipe/usermetadata';
import {
    PasswordError,
    PasswordPolicyError,
    SessionError
} from '@/app/(authenticated)/account/errors';

export async function updatePassword(oldPassword: string, newPassword: string) {
    const { session, error } = await getSSRSessionHelper();

    if (!session || error) {
        throw new SessionError();
    }

    const user = await supertokens.getUser(session!.getUserId());

    if (!user) {
        throw new SessionError();
    }

    const loginMethod = user.loginMethods.find(
        (it) =>
            it.recipeUserId.getAsString() === session!.getRecipeUserId().getAsString() &&
            it.recipeId === 'emailpassword'
    );

    if (!loginMethod) {
        throw new SessionError();
    }

    const isPasswordValid = await EmailPassword.verifyCredentials(
        session!.getTenantId(),
        loginMethod.email!,
        oldPassword
    );

    if (isPasswordValid.status !== 'OK') {
        throw new PasswordError();
    }

    const response = await EmailPassword.updateEmailOrPassword({
        recipeUserId: session!.getRecipeUserId(),
        password: newPassword,
        tenantIdForPasswordPolicy: session!.getTenantId()
    });

    if (response.status === 'PASSWORD_POLICY_VIOLATED_ERROR') {
        throw new PasswordPolicyError();
    }

    return;
}

export async function updateName(firstName: string, lastName: string) {
    const { session, error } = await getSSRSessionHelper();

    if (!session || error) {
        throw new SessionError();
    }

    await UserMetadata.updateUserMetadata(session!.getUserId()!, {
        first_name: firstName,
        last_name: lastName
    });
}
