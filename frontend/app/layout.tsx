import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';

import { SWRProvider } from '@/components/SWRProvider';
import { Providers as SuperTokensProvider } from '@/components/SupertokensProvider';
import PostHogProvider from '@/components/PostHogProvider';

import './globals.css';
import React from 'react';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
    title: 'Quarterback',
    description: ''
};

export default function RootLayout({ children }: Readonly<{} & React.PropsWithChildren>) {
    return (
        <html className="h-full bg-gray-50" lang="en">
            <PostHogProvider>
                <SuperTokensProvider>
                    <SWRProvider>
                        <body className={`${inter.className} h-full`}>{children}</body>
                    </SWRProvider>
                </SuperTokensProvider>
            </PostHogProvider>
        </html>
    );
}
