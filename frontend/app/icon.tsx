import { ImageResponse } from 'next/og';

// Route segment config
export const runtime = 'edge';

// Image metadata
export const size = {
    width: 32,
    height: 32
};
export const contentType = 'image/png';

// Image generation
export default function Icon() {
    return new ImageResponse(
        (
            <svg
                width="100%"
                height="100%"
                viewBox="0 0 81 81"
                fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <rect width="81" height="81" rx="17" fill="#5B45FF" />
                <rect
                    x="56.3203"
                    y="21.5156"
                    width="12.0234"
                    height="12.0234"
                    rx="3"
                    fill="white"
                />
                <rect
                    x="13.2891"
                    y="21.5156"
                    width="12.0234"
                    height="12.0234"
                    rx="3"
                    fill="white"
                />
                <rect
                    x="56.3203"
                    y="36.7031"
                    width="12.0234"
                    height="12.0234"
                    rx="3"
                    fill="white"
                />
                <rect
                    x="44.2969"
                    y="52.5234"
                    width="12.0234"
                    height="12.0234"
                    rx="3"
                    fill="white"
                />
                <rect
                    x="25.3125"
                    y="52.5234"
                    width="12.0234"
                    height="12.0234"
                    rx="3"
                    fill="white"
                />
                <rect
                    x="13.2891"
                    y="36.7031"
                    width="12.0234"
                    height="12.0234"
                    rx="3"
                    fill="white"
                />
            </svg>
        ),
        // ImageResponse JSX element

        // ImageResponse options
        {
            // For convenience, we can re-use the exported icons size metadata
            // config to also set the ImageResponse's width and height.
            ...size
        }
    );
}
