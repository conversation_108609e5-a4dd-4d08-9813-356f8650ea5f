import { ensureSuperTokensInit } from '@/config/backend';
import { NextRequest, NextResponse } from 'next/server';
import { withSession } from 'supertokens-node/nextjs';
import SuperTokens from 'supertokens-node';
import UserMetadata from 'supertokens-node/recipe/usermetadata';
import UserRoles from 'supertokens-node/recipe/userroles';

ensureSuperTokensInit();

export function GET(request: NextRequest) {
    return withSession(request, async (err, session) => {
        if (err) {
            return NextResponse.json(err, { status: 500 });
        }

        const userId = session!.getUserId();

        const user = await SuperTokens.getUser(userId);
        const { metadata } = await UserMetadata.getUserMetadata(userId);
        const { roles } = await UserRoles.getRolesForUser('public', userId);
        const permissions = (
            await Promise.all(
                (roles ?? []).map(async (role) => {
                    const permissions = await UserRoles.getPermissionsForRole(role);

                    if (permissions.status === 'OK') {
                        return permissions.permissions;
                    } else {
                        return [];
                    }
                })
            )
        ).flat();

        return NextResponse.json({
            ...user,
            metadata,
            roles,
            permissions
        });
    });
}
