import { ensureSuperTokensInit } from '@/config/backend';
import { NextRequest, NextResponse } from 'next/server';
import { withSession } from 'supertokens-node/nextjs';
import SuperTokens from 'supertokens-node';
import UserMetadata from 'supertokens-node/recipe/usermetadata';
import UserRoles from 'supertokens-node/recipe/userroles';

ensureSuperTokensInit();

export function GET(request: NextRequest) {
    return withSession(request, async (err, session) => {
        if (err) {
            return NextResponse.json(err, { status: 500 });
        }

        const userId = session!.getUserId();

        const { roles } = await UserRoles.getRolesForUser('public', userId);

        if (!(roles ?? []).includes('Admin')) {
            return NextResponse.json([]);
        } else {
            return NextResponse.json(
                await SuperTokens.getUsersNewestFirst({ tenantId: 'public' }).then((it) => it.users)
            );
        }
    });
}
