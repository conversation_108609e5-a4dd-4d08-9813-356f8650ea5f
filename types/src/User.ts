import z from 'zod';

export const User = z.object({
    id: z.string().uuid(),
    isPrimaryUser: z.boolean(),
    tenantIds: z.array(z.string()),
    emails: z.array(z.string().email()),
    phoneNumbers: z.array(z.string()),
    timeJoined: z.number(),
    metadata: z.object({
        first_name: z.string().optional(),
        last_name: z.string().optional()
    }),
    roles: z.array(z.string()),
    permissions: z.array(z.string())
});
export type User = z.infer<typeof User>;
