import z from 'zod';
import { ListedEntity } from './ListedEntity.js';

export const OrganisationUser = z.object({
    id: z.string(),
    entities: z.array(ListedEntity)
});
export type OrganisationUser = z.infer<typeof OrganisationUser>;

export const Organisation = z.object({
    id: z.string().optional(),
    name: z.string(),
    users: z.array(OrganisationUser).min(1).optional()
});
export type Organisation = z.infer<typeof Organisation>;
