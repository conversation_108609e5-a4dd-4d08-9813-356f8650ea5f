import { z } from 'zod';
import { ActivityFile, author } from '@quarterback/types';

const CommonActivityFields = {
    title: z.string().optional(),
    body: z.string().min(1, 'Required'),
    url: z.string().url('Must be a valid URL'),
    image: z.string().url('Must be a valid image URL').optional().or(z.literal('')),
    posted: z
        .string()
        .min(1, 'Required')
        .refine((val) => !val || !isNaN(Date.parse(val)), {
            message: 'Invalid date format'
        }),
    format: z.string().min(1, 'Required'),
    source: z.string().min(1, 'Required'),
    author: author.optional(),
    likes: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Likes must be a number'
        })
        .optional(),
    views: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Views must be a number'
        })
        .optional(),
    files: ActivityFile.array().optional()
};

// Base schema for social media activities (requires author)
export const BaseActivityFormData = z.object({
    ...CommonActivityFields,
    author: author.refine((val) => val !== undefined && val !== null, {
        message: 'Required'
    })
});

// Base schema for news (no author required)
export const BaseNewsActivityFormData = z.object({
    ...CommonActivityFields,
    title: z.string().min(1, 'Required')
});

export const TwitterFormData = BaseActivityFormData.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('twitter.com') || url.includes('x.com');
        },
        {
            message: 'Must be a Twitter URL'
        }
    )
});

export const HotCopperFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('hotcopper.com.au');
            },
            {
                message: 'Must be a HotCopper URL'
            }
        ),
    disclosure: z.string().min(1, 'Disclosure is required'),
    sentiment: z.string().min(1, 'Sentiment is required'),
    thread: z.string().min(1, 'Required'),
    threadViews: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Thread views must be a number'
        })
        .optional()
});

export const LinkedInFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('linkedin.com');
            },
            {
                message: 'Must be a LinkedIn URL'
            }
        ),
    comments: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Comments must be a number'
        })
        .optional()
});

export const RedditFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('reddit.com');
            },
            {
                message: 'Must be a Reddit URL'
            }
        ),
    subreddit: z.string().min(1, 'Subreddit is required'),
    score: z
        .string()
        .refine((val) => !val || !isNaN(Number(val)), {
            message: 'Score must be a number'
        })
        .optional(),
    ratio: z
        .string()
        .refine(
            (val) =>
                !val || (!isNaN(Number(val)) && Number(val) >= 0 && Number(val) <= 1),
            { message: 'Ratio must be a number between 0 and 1' }
        )
        .optional()
});

export const NewsFormData = BaseNewsActivityFormData.extend({
    newsSource: z.object({
        name: z.string().optional(),
        logo: z.string().nullable().optional(),
        url: z.string().optional()
    })
});

export const ASXFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .regex(
            /https:\/\/(?:www\.)?(?:asx\.com\.au\/(?:asxpdf\/\d+\/pdf\/[a-zA-Z0-9]+\.pdf|market-announcements\/company\/[a-zA-Z0-9]+\/[a-zA-Z0-9-]+)|announcements\.asx\.com\.au\/(?:asxpdf\/\d+\/pdf\/[a-zA-Z0-9]+\.pdf|asx\/[a-zA-Z0-9]+(?:\/[a-zA-Z0-9-]+)?)|www2\.asx\.com\.au\/markets\/company\/[a-zA-Z0-9]+\/announcements(?:\/[a-zA-Z0-9-]+)?|www2\.asx\.com\.au\/markets\/trade-our-cash-market\/announcements\.(?:[a-zA-Z0-9]+))/,
            'Invalid ASX announcement URL'
        ),
    sensitive: z.boolean().optional()
});

export const FacebookFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('facebook.com');
            },
            {
                message: 'Must be a Facebook URL'
            }
        )
});

export const InstagramFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('instagram.com');
            },
            {
                message: 'Must be an Instagram URL'
            }
        )
});

export const YouTubeFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('youtube.com') || url.includes('youtu.be');
            },
            {
                message: 'Must be a YouTube URL'
            }
        )
});

export const TikTokFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tiktok.com');
            },
            {
                message: 'Must be a TikTok URL'
            }
        )
});

export const DiscordFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('discord.com') || url.includes('discord.gg');
            },
            {
                message: 'Must be a Discord URL'
            }
        )
});

export const TelegramFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('t.me') || url.includes('telegram.me');
            },
            {
                message: 'Must be a Telegram URL'
            }
        )
});

export const MediumFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('medium.com');
            },
            {
                message: 'Must be a Medium URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const QuoraFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('quora.com');
            },
            {
                message: 'Must be a Quora URL'
            }
        )
});

export const SlackFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('slack.com');
            },
            {
                message: 'Must be a Slack URL'
            }
        )
});

export const ADVFNFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('advfn.com');
            },
            {
                message: 'Must be an ADVFN URL'
            }
        ),

    title: z.string().min(1, 'Required')
});

export const ApplePodcastFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('podcasts.apple.com');
            },
            {
                message: 'Must be an Apple Podcast URL'
            }
        )
});

export const AudibleFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('audible.com');
            },
            {
                message: 'Must be an Audible URL'
            }
        )
});

export const AussieStockForumsFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('aussiestockforums.com');
            },
            {
                message: 'Must be an Aussie Stock Forums URL'
            }
        )
});

export const CastboxFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('castbox.fm');
            },
            {
                message: 'Must be a Castbox URL'
            }
        )
});

export const ClubhouseFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('clubhouse.com');
            },
            {
                message: 'Must be a Clubhouse URL'
            }
        )
});

export const IHeartRadioFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('iheart.com');
            },
            {
                message: 'Must be an iHeartRadio URL'
            }
        )
});

export const InvestorHubFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('investorhub.com');
            },
            {
                message: 'Must be an InvestorHub URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const PinterestFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('pinterest.com');
            },
            {
                message: 'Must be a Pinterest URL'
            }
        )
});

export const SnapchatFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('snapchat.com');
            },
            {
                message: 'Must be a Snapchat URL'
            }
        )
});

export const SpotifyFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('spotify.com');
            },
            {
                message: 'Must be a Spotify URL'
            }
        )
});

export const StocktwitsFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('stocktwits.com');
            },
            {
                message: 'Must be a Stocktwits URL'
            }
        )
});

export const StrawmanFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('strawman.com');
            },
            {
                message: 'Must be a Strawman URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const TradingQnAFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tradingqna.com');
            },
            {
                message: 'Must be a TradingQnA URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const TumblrFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tumblr.com');
            },
            {
                message: 'Must be a Tumblr URL'
            }
        )
});

export const VimeoFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('vimeo.com');
            },
            {
                message: 'Must be a Vimeo URL'
            }
        )
});

export const WeChatFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('wechat.com');
            },
            {
                message: 'Must be a WeChat URL'
            }
        )
});

export const WhatsAppFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whatsapp.com');
            },
            {
                message: 'Must be a WhatsApp URL'
            }
        )
});

export const WhirlpoolFinanceFormData = BaseActivityFormData.extend({
    title: z.string().min(1, 'Required'),
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whirlpool.net.au');
            },
            {
                message: 'Must be a Whirlpool Finance URL'
            }
        )
});

export const BogleheadsFormData = BaseActivityFormData.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('bogleheads.org');
            },
            {
                message: 'Must be a Bogleheads URL'
            }
        ),
    title: z.string().min(1, 'Required')
});

export const ActivityFormData = z.discriminatedUnion('source', [
    TwitterFormData.extend({ source: z.literal('https://x.com') }),
    HotCopperFormData.extend({ source: z.literal('https://hotcopper.com.au') }),
    LinkedInFormData.extend({ source: z.literal('https://linkedin.com') }),
    RedditFormData.extend({ source: z.literal('https://reddit.com') }),
    FacebookFormData.extend({ source: z.literal('https://facebook.com') }),
    InstagramFormData.extend({ source: z.literal('https://instagram.com') }),
    YouTubeFormData.extend({ source: z.literal('https://youtube.com') }),
    TikTokFormData.extend({ source: z.literal('https://tiktok.com') }),
    DiscordFormData.extend({ source: z.literal('https://discord.com') }),
    TelegramFormData.extend({ source: z.literal('https://telegram.org') }),
    MediumFormData.extend({ source: z.literal('https://medium.com') }),
    QuoraFormData.extend({ source: z.literal('https://quora.com') }),
    SlackFormData.extend({ source: z.literal('https://slack.com') }),
    NewsFormData.extend({ source: z.literal('news') }),
    ASXFormData.extend({ source: z.literal('https://asx.com.au') }),
    ADVFNFormData.extend({ source: z.literal('https://advfn.com') }),
    ApplePodcastFormData.extend({ source: z.literal('https://podcasts.apple.com') }),
    AudibleFormData.extend({ source: z.literal('https://audible.com') }),
    AussieStockForumsFormData.extend({
        source: z.literal('https://aussiestockforums.com')
    }),
    CastboxFormData.extend({ source: z.literal('https://castbox.fm') }),
    ClubhouseFormData.extend({ source: z.literal('https://clubhouse.com') }),
    IHeartRadioFormData.extend({ source: z.literal('https://iheart.com') }),
    InvestorHubFormData.extend({ source: z.literal('https://investorhub.com') }),
    PinterestFormData.extend({ source: z.literal('https://pinterest.com') }),
    SnapchatFormData.extend({ source: z.literal('https://snapchat.com') }),
    SpotifyFormData.extend({ source: z.literal('https://spotify.com') }),
    StocktwitsFormData.extend({ source: z.literal('https://stocktwits.com') }),
    StrawmanFormData.extend({ source: z.literal('https://strawman.com') }),
    TradingQnAFormData.extend({ source: z.literal('https://tradingqna.com') }),
    TumblrFormData.extend({ source: z.literal('https://tumblr.com') }),
    VimeoFormData.extend({ source: z.literal('https://vimeo.com') }),
    WeChatFormData.extend({ source: z.literal('https://wechat.com') }),
    WhatsAppFormData.extend({ source: z.literal('https://whatsapp.com') }),
    WhirlpoolFinanceFormData.extend({
        source: z.literal('https://forums.whirlpool.net.au')
    }),
    BogleheadsFormData.extend({ source: z.literal('https://bogleheads.org') })
]);

export type BaseActivityFormData = z.infer<typeof BaseActivityFormData>;
export type TwitterFormData = z.infer<typeof TwitterFormData>;
export type HotCopperFormData = z.infer<typeof HotCopperFormData>;
export type LinkedInFormData = z.infer<typeof LinkedInFormData>;
export type RedditFormData = z.infer<typeof RedditFormData>;
export type NewsFormData = z.infer<typeof NewsFormData>;
export type ASXFormData = z.infer<typeof ASXFormData>;
export type FacebookFormData = z.infer<typeof FacebookFormData>;
export type InstagramFormData = z.infer<typeof InstagramFormData>;
export type YouTubeFormData = z.infer<typeof YouTubeFormData>;
export type TikTokFormData = z.infer<typeof TikTokFormData>;
export type DiscordFormData = z.infer<typeof DiscordFormData>;
export type TelegramFormData = z.infer<typeof TelegramFormData>;
export type MediumFormData = z.infer<typeof MediumFormData>;
export type QuoraFormData = z.infer<typeof QuoraFormData>;
export type SlackFormData = z.infer<typeof SlackFormData>;
export type ADVFNFormData = z.infer<typeof ADVFNFormData>;
export type ApplePodcastFormData = z.infer<typeof ApplePodcastFormData>;
export type AudibleFormData = z.infer<typeof AudibleFormData>;
export type AussieStockForumsFormData = z.infer<typeof AussieStockForumsFormData>;
export type CastboxFormData = z.infer<typeof CastboxFormData>;
export type ClubhouseFormData = z.infer<typeof ClubhouseFormData>;
export type IHeartRadioFormData = z.infer<typeof IHeartRadioFormData>;
export type InvestorHubFormData = z.infer<typeof InvestorHubFormData>;
export type PinterestFormData = z.infer<typeof PinterestFormData>;
export type SnapchatFormData = z.infer<typeof SnapchatFormData>;
export type SpotifyFormData = z.infer<typeof SpotifyFormData>;
export type StocktwitsFormData = z.infer<typeof StocktwitsFormData>;
export type StrawmanFormData = z.infer<typeof StrawmanFormData>;
export type TradingQnAFormData = z.infer<typeof TradingQnAFormData>;
export type TumblrFormData = z.infer<typeof TumblrFormData>;
export type VimeoFormData = z.infer<typeof VimeoFormData>;
export type WeChatFormData = z.infer<typeof WeChatFormData>;
export type WhatsAppFormData = z.infer<typeof WhatsAppFormData>;
export type WhirlpoolFinanceFormData = z.infer<typeof WhirlpoolFinanceFormData>;
export type BogleheadsFormData = z.infer<typeof BogleheadsFormData>;
export type ActivityFormData = z.infer<typeof ActivityFormData>;
