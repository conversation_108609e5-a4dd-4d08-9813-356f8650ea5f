import { z } from 'zod';
import { author } from '../Activity2.js';

const BaseAuthorFormSchema = author.extend({
    source: z.string().min(1, 'Source is required'),
    userId: z.string().min(1, 'username is required'),
    key: z.string().optional(),
    image: z.string().url().or(z.literal('')).optional(),
    followers: z
        .number()
        .min(0, 'Following must be greater than or equal to 0')
        .nullable()
        .optional(),
    following: z
        .number()
        .min(0, 'Following must be greater than or equal to 0')
        .nullable()
        .optional(),

    url: z.string().url().nullable().optional()
});

const TwitterAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('twitter.com') || url.includes('x.com');
        },
        {
            message: 'Must be a Twitter URL'
        }
    )
});

const HotCopperAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('hotcopper.com.au');
        },
        {
            message: 'Must be a HotCopper URL'
        }
    )
});

const LinkedInAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('linkedin.com');
        },
        {
            message: 'Must be a LinkedIn URL'
        }
    )
});

const RedditAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z.string().refine(
        (url) => {
            return url.includes('reddit.com');
        },
        {
            message: 'Must be a Reddit URL'
        }
    )
});

const FacebookAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('facebook.com');
            },
            {
                message: 'Must be a Facebook URL'
            }
        )
});

const InstagramAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('instagram.com');
            },
            {
                message: 'Must be an Instagram URL'
            }
        )
});

const YouTubeAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('youtube.com') || url.includes('youtu.be');
            },
            {
                message: 'Must be a YouTube URL'
            }
        )
});

const TikTokAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tiktok.com');
            },
            {
                message: 'Must be a TikTok URL'
            }
        )
});

const DiscordAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('discord.com') || url.includes('discord.gg');
            },
            {
                message: 'Must be a Discord URL'
            }
        )
});

const TelegramAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('t.me') || url.includes('telegram.me');
            },
            {
                message: 'Must be a Telegram URL'
            }
        )
});

const MediumAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('medium.com');
            },
            {
                message: 'Must be a Medium URL'
            }
        )
});

const QuoraAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('quora.com');
            },
            {
                message: 'Must be a Quora URL'
            }
        )
});

const SlackAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('slack.com');
            },
            {
                message: 'Must be a Slack URL'
            }
        )
});

const ADVFNAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('advfn.com');
            },
            {
                message: 'Must be an ADVFN URL'
            }
        )
});

const ApplePodcastAuthorFormSchema = BaseAuthorFormSchema.extend({});

const AussieStockForumsAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('aussiestockforums.com');
            },
            {
                message: 'Must be an Aussie Stock Forums URL'
            }
        )
});

const CastboxAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('castbox.fm');
            },
            {
                message: 'Must be a Castbox URL'
            }
        )
});

const ClubhouseAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('clubhouse.com');
            },
            {
                message: 'Must be a Clubhouse URL'
            }
        )
});

const IHeartRadioAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('iheart.com');
            },
            {
                message: 'Must be an iHeartRadio URL'
            }
        )
});

const InvestorHubAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('investorshub.com');
            },
            {
                message: 'Must be an InvestorHub URL'
            }
        )
});

const PinterestAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('pinterest.com');
            },
            {
                message: 'Must be a Pinterest URL'
            }
        )
});

const SnapchatAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('snapchat.com');
            },
            {
                message: 'Must be a Snapchat URL'
            }
        )
});

const SpotifyAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('spotify.com');
            },
            {
                message: 'Must be a Spotify URL'
            }
        )
});

const StocktwitsAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('stocktwits.com');
            },
            {
                message: 'Must be a Stocktwits URL'
            }
        )
});

const StrawmanAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('strawman.com');
            },
            {
                message: 'Must be a Strawman URL'
            }
        )
});

const TradingQnAAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tradingqna.com');
            },
            {
                message: 'Must be a TradingQnA URL'
            }
        )
});

const TumblrAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('tumblr.com');
            },
            {
                message: 'Must be a Tumblr URL'
            }
        )
});

const VimeoAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('vimeo.com');
            },
            {
                message: 'Must be a Vimeo URL'
            }
        )
});

const WeChatAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('wechat.com');
            },
            {
                message: 'Must be a WeChat URL'
            }
        )
});

const WhatsAppAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whatsapp.com');
            },
            {
                message: 'Must be a WhatsApp URL'
            }
        )
});

const WhirlpoolFinanceAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whirlpool.net.au');
            },
            {
                message: 'Must be a Whirlpool Finance URL'
            }
        )
});

const BogleheadsAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('bogleheads.org');
            },
            {
                message: 'Must be a Bogleheads URL'
            }
        )
});

const asxAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('asx.com.au');
            },
            {
                message: 'Must be an ASX URL'
            }
        )
});

const AudibleAuthorFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('audible.com') || url.includes('audible.com.au');
            },
            {
                message: 'Must be an Audible URL'
            }
        )
});

const WhirlpoolAuthorFinanceFormSchema = BaseAuthorFormSchema.extend({
    url: z
        .string()
        .url('Must be a valid URL')
        .refine(
            (url) => {
                return url.includes('whirlpool.net.au');
            },
            {
                message: 'Must be a Whirlpool Finance URL'
            }
        )
});

export const AuthorFormData = z.discriminatedUnion('source', [
    BaseAuthorFormSchema.merge(TwitterAuthorFormSchema).extend({
        source: z.literal('https://x.com')
    }),
    BaseAuthorFormSchema.merge(HotCopperAuthorFormSchema).extend({
        source: z.literal('https://hotcopper.com.au')
    }),
    BaseAuthorFormSchema.merge(LinkedInAuthorFormSchema).extend({
        source: z.literal('https://linkedin.com')
    }),
    BaseAuthorFormSchema.merge(RedditAuthorFormSchema).extend({
        source: z.literal('https://reddit.com')
    }),
    BaseAuthorFormSchema.merge(FacebookAuthorFormSchema).extend({
        source: z.literal('https://facebook.com')
    }),
    BaseAuthorFormSchema.merge(InstagramAuthorFormSchema).extend({
        source: z.literal('https://instagram.com')
    }),
    BaseAuthorFormSchema.merge(YouTubeAuthorFormSchema).extend({
        source: z.literal('https://youtube.com')
    }),
    BaseAuthorFormSchema.merge(TikTokAuthorFormSchema).extend({
        source: z.literal('https://tiktok.com')
    }),
    BaseAuthorFormSchema.merge(DiscordAuthorFormSchema).extend({
        source: z.literal('https://discord.com')
    }),
    BaseAuthorFormSchema.merge(TelegramAuthorFormSchema).extend({
        source: z.literal('https://telegram.org')
    }),
    BaseAuthorFormSchema.merge(MediumAuthorFormSchema).extend({
        source: z.literal('https://medium.com')
    }),
    BaseAuthorFormSchema.merge(QuoraAuthorFormSchema).extend({
        source: z.literal('https://quora.com')
    }),
    BaseAuthorFormSchema.merge(SlackAuthorFormSchema).extend({
        source: z.literal('https://slack.com')
    }),
    BaseAuthorFormSchema.merge(ADVFNAuthorFormSchema).extend({
        source: z.literal('https://advfn.com')
    }),
    BaseAuthorFormSchema.merge(ApplePodcastAuthorFormSchema).extend({
        source: z.literal('https://podcasts.apple.com')
    }),
    BaseAuthorFormSchema.merge(AudibleAuthorFormSchema).extend({
        source: z.literal('https://audible.com')
    }),
    BaseAuthorFormSchema.merge(AussieStockForumsAuthorFormSchema).extend({
        source: z.literal('https://aussiestockforums.com')
    }),
    BaseAuthorFormSchema.merge(CastboxAuthorFormSchema).extend({
        source: z.literal('https://castbox.fm')
    }),
    BaseAuthorFormSchema.merge(ClubhouseAuthorFormSchema).extend({
        source: z.literal('https://clubhouse.com')
    }),
    BaseAuthorFormSchema.merge(IHeartRadioAuthorFormSchema).extend({
        source: z.literal('https://iheart.com')
    }),
    BaseAuthorFormSchema.merge(InvestorHubAuthorFormSchema).extend({
        source: z.literal('https://investorhub.com')
    }),
    BaseAuthorFormSchema.merge(PinterestAuthorFormSchema).extend({
        source: z.literal('https://pinterest.com')
    }),
    BaseAuthorFormSchema.merge(SnapchatAuthorFormSchema).extend({
        source: z.literal('https://snapchat.com')
    }),
    BaseAuthorFormSchema.merge(SpotifyAuthorFormSchema).extend({
        source: z.literal('https://spotify.com')
    }),
    BaseAuthorFormSchema.merge(StocktwitsAuthorFormSchema).extend({
        source: z.literal('https://stocktwits.com')
    }),
    BaseAuthorFormSchema.merge(StrawmanAuthorFormSchema).extend({
        source: z.literal('https://strawman.com')
    }),
    BaseAuthorFormSchema.merge(TradingQnAAuthorFormSchema).extend({
        source: z.literal('https://tradingqna.com')
    }),
    BaseAuthorFormSchema.merge(TumblrAuthorFormSchema).extend({
        source: z.literal('https://tumblr.com')
    }),
    BaseAuthorFormSchema.merge(VimeoAuthorFormSchema).extend({
        source: z.literal('https://vimeo.com')
    }),
    BaseAuthorFormSchema.merge(WeChatAuthorFormSchema).extend({
        source: z.literal('https://wechat.com')
    }),
    BaseAuthorFormSchema.merge(WhatsAppAuthorFormSchema).extend({
        source: z.literal('https://whatsapp.com')
    }),
    BaseAuthorFormSchema.merge(WhirlpoolFinanceAuthorFormSchema).extend({
        source: z.literal('https://forums.whirlpool.net.au')
    }),
    BaseAuthorFormSchema.merge(BogleheadsAuthorFormSchema).extend({
        source: z.literal('https://bogleheads.org')
    }),
    BaseAuthorFormSchema.merge(asxAuthorFormSchema).extend({
        source: z.literal('https://asx.com.au')
    })
]);

export type AuthorFormData = z.infer<typeof AuthorFormData>;
