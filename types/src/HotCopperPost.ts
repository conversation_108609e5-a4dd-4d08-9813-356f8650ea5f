import z from 'zod';
import { Sentiment } from './Sentiment.js';

export const HotCopperSentiment = z.enum(['BUY', 'HOLD', 'SELL', 'NONE']);
export type HotCopperSentiment = z.infer<typeof HotCopperSentiment>;

export const HotCopperDisclosure = z.enum(['HELD', 'NOT_HELD', 'UNDISCLOSED']);
export type HotCopperDisclosure = z.infer<typeof HotCopperDisclosure>;

export const HotCopperThread = z.object({
    id: z.string().optional(),
    thread: z.string(),
    views: z.number()
});
export type HotCopperThread = z.infer<typeof HotCopperThread>;

export const HotCopperPost = z.object({
    id: z.string().optional(),
    thread: HotCopperThread,
    post: z.number(),
    url: z.string().url(),
    posted: z.coerce.date(),
    title: z.string(),
    description: z.string(),
    likes: z.number(),
    hotcopper: z
        .object({
            sentiment: HotCopperSentiment,
            disclosure: HotCopperDisclosure
        })
        .optional(),
    sentiment: Sentiment.optional(),
    isBroadcast: z.boolean().default(false)
});
export type HotCopperPost = z.infer<typeof HotCopperPost>;
