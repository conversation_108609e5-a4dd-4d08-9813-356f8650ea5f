import z, { string } from 'zod';
import { Sentiment } from './Sentiment.js';

export const InvestorHubPost = z.object({
    id: string(),
    title: string().nullable(),
    posted: z.coerce.date(),
    body: string(),
    image: string().nullable(),
    likes: z.number(),
    isBroadcast: z.boolean().default(false),
    sentiment: Sentiment.optional()
});

export type InvestorHubPost = z.infer<typeof InvestorHubPost>;
