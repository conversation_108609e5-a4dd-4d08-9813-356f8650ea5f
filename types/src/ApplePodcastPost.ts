import { string, z } from 'zod';
import { Sentiment } from './Sentiment.js';

export const ApplePodcastPost = z.object({
    id: string(),
    title: string(),
    posted: z.coerce.date(),
    body: string(),
    image: string().nullable(),
    likes: z.number(),
    isBroadcast: z.boolean().default(false),
    sentiment: Sentiment.optional()
});

export type ApplePodcastPost = z.infer<typeof ApplePodcastPost>;
