import z from 'zod';

export const ListedEntity = z.object({
    name: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    twitterUsername: z.string().nullable().optional(),
    twitterQuery: z.string().nullable().optional(),
    newsQuery: z.string().nullable().optional(),
    redditQuery: z.string().nullable().optional(),
    linkedInQuery: z.string().nullable().optional(),
    linkedInUsername: z.string().nullable().optional(),
    linkedInCompanyId: z.string().nullable().optional(),
    about: z.string().nullable().optional()
});
export type ListedEntity = z.infer<typeof ListedEntity>;
