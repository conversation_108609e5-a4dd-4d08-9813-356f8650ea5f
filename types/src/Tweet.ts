import z from 'zod';
import { Sentiment } from './Sentiment.js';

export const Tweet = z.object({
    id: z.string().optional(),
    posted: z.coerce.date(),
    body: z.string(),
    likes: z.number(),
    views: z.number(),
    url: z.string().url(),
    isBroadcast: z.boolean().default(false),
    image: z.string().url().nullable().optional(),
    thread: z.string().nullable().optional(),
    sentiment: Sentiment.optional()
});
export type Tweet = z.infer<typeof Tweet>;
