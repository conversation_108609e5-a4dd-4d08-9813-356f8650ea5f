import z, { string } from 'zod';
import { Sentiment } from './Sentiment.js';

export const IHeartRadioPost = z.object({
    id: string(),
    title: string(),
    posted: z.coerce.date(),
    body: string(),
    image: string().nullable(),
    likes: z.number(),
    isBroadcast: z.boolean().default(false),
    sentiment: Sentiment.optional()
});

export type IHeartRadioPost = z.infer<typeof IHeartRadioPost>;
