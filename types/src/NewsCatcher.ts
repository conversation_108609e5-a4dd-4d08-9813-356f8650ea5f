import { z } from 'zod';

export const NewsCatcherSearchParams = z.object({
    q: z.string(),
    lang: z.string(),
    page: z.number().min(1).optional(),
    page_size: z.number().min(1).max(1000).optional(),
    sort_by: z.enum(['relevancy', 'date', 'rank']).optional(),
    country: z.string().optional(),
    from_: z.string().optional(),
    to_: z.string().optional(),
    ranked_only: z.boolean().optional(),
    ORG_entity_name: z.string().optional(),
    PER_entity_name: z.string().optional(),
    LOC_entity_name: z.string().optional(),
    MISC_entity_name: z.string().optional(),
    exclude_duplicates: z.boolean().optional(),
    published_date_precision: z.enum(['full', 'timezone unknown', 'date']).optional(),
    by_parse_date: z.boolean().optional()
});

const Article = z.object({
    title: z.string(),
    content: z.string(),
    domain_url: z.string(),
    full_domain_url: z.string(),
    name_source: z.string().nullable().optional(),
    published_date: z.string(),
    parent_url: z.string(),
    link: z.string(),
    media: z.string().nullable().optional()
});

export const NewsCatcherResponse = z.object({
    status: z.literal('ok'),
    page: z.number(),
    total_pages: z.number(),
    page_size: z.number(),
    total_hits: z.number(),
    articles: Article.array()
});

export type NewsCatcherResponse = z.infer<typeof NewsCatcherResponse>;
export type NewsCatcherSearchParams = z.infer<typeof NewsCatcherSearchParams>;
