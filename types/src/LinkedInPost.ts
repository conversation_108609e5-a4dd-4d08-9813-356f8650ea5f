import z, { string } from 'zod';
import { Sentiment } from './Sentiment.js';

export const LinkedInPost = z.object({
    id: string(),
    title: string().nullable(),
    posted: z.coerce.date(),
    thread: z.string().nullable().optional(),
    body: string(),
    image: string().nullable(),
    comments: z.number(),
    likes: z.number(),
    isBroadcast: z.boolean().default(false),
    commentUrn: string().nullable().optional(),
    sentiment: Sentiment.optional()
});

export type LinkedInPost = z.infer<typeof LinkedInPost>;
