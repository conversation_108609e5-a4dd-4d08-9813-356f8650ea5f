import z from 'zod';

import { ASXAnnouncement } from './ASXAnnouncement.js';
import { HotCopperPost } from './HotCopperPost.js';
import { LinkedInPost } from './LinkedInPost.js';
import { NewsArticle } from './NewsArticle.js';
import { Sentiment } from './Sentiment.js';
import { Tweet } from './Tweet.js';
import { author } from './Activity2.js';
import { CastboxPost } from './CastboxPost.js';
import { ClubhousePost } from './ClubhousePost.js';
import { DiscordPost } from './DiscordPost.js';
import { FacebookPost } from './FacebookPost.js';
import { IHeartRadioPost } from './IHeartRadioPost.js';
import { InstagramPost } from './InstagramPost.js';
import { InvestorHubPost } from './InvestorHubPost.js';
import { MediumPost } from './MediumPost.js';
import { PinterestPost } from './PinterestPost.js';
import { QuoraPost } from './QuoraPost.js';
import { SlackPost } from './SlackPost.js';
import { SnapchatPost } from './SnapchatPost.js';
import { SpotifyPost } from './SpotifyPost.js';
import { StocktwitsPost } from './StocktwitsPost.js';
import { StrawmanPost } from './StrawmanPost.js';
import { TelegramPost } from './TelegramPost.js';
import { TikTokPost } from './TikTokPost.js';
import { TradingQnAPost } from './TradingQnAPost.js';
import { TumblrPost } from './TumblrPost.js';
import { VimeoPost } from './VimeoPost.js';
import { WeChatPost } from './WeChatPost.js';
import { WhatsAppPost } from './WhatsAppPost.js';
import { WhirlpoolFinancePost } from './WhirlpoolFinancePost.js';
import { BogleHeadsPost } from './BogleHeadsPost.js';
import { youtubePost } from './youtubePost.js';
import { ADVFNPost } from './ADVFNPost.js';
import { ApplePodcastPost } from './ApplePodcastPost.js';
import { AudiblePost } from './AudiblePost.js';
import { AussieStockForumsPost } from './AussieStockForumsPost.js';

const RedditPost = z.object({
    id: z.string().optional(),
    posted: z.coerce.date(),
    title: z.string(),
    body: z.string(),
    score: z.number(),
    ratio: z.number(),
    sentiment: Sentiment.optional(),
    image: z.string().url().nullable().optional(),
    post: z.string().optional()
});

const RedditComment = z.object({
    id: z.string().optional(),
    post: z.string().optional(),
    parent: z.string().optional(),
    score: z.number().optional(),
    posted: z.coerce.date(),
    body: z.string()
});

type RedditPost = z.infer<typeof RedditPost>;

const BaseActivity = z.object({
    id: z.string().optional(),
    posted: z.coerce.date().optional(),
    url: z.string().url().optional(),
    sentiment: Sentiment.optional(),
    read: z.boolean().optional(),
    flagged: z.boolean().optional(),
    archived: z.boolean().optional(),
    author: author.optional()
});

export const Activity = z.discriminatedUnion('type', [
    BaseActivity.merge(ASXAnnouncement).extend({ type: z.literal('asx-announcement') }),
    BaseActivity.merge(NewsArticle).extend({ type: z.literal('news') }),
    BaseActivity.merge(HotCopperPost).extend({ type: z.literal('hotcopper') }),
    BaseActivity.merge(Tweet).extend({ type: z.literal('tweet') }),
    BaseActivity.merge(RedditPost).extend({ type: z.literal('reddit') }),
    BaseActivity.merge(RedditComment).extend({ type: z.literal('redditComment') }),
    BaseActivity.merge(LinkedInPost).extend({ type: z.literal('linkedIn') }),
    BaseActivity.merge(CastboxPost).extend({ type: z.literal('castbox') }),
    BaseActivity.merge(ClubhousePost).extend({ type: z.literal('clubhouse') }),
    BaseActivity.merge(DiscordPost).extend({ type: z.literal('discord') }),
    BaseActivity.merge(FacebookPost).extend({ type: z.literal('facebook') }),
    BaseActivity.merge(IHeartRadioPost).extend({ type: z.literal('iheartradio') }),
    BaseActivity.merge(InstagramPost).extend({ type: z.literal('instagram') }),
    BaseActivity.merge(InvestorHubPost).extend({ type: z.literal('investorhub') }),
    BaseActivity.merge(MediumPost).extend({ type: z.literal('medium') }),
    BaseActivity.merge(PinterestPost).extend({ type: z.literal('pinterest') }),
    BaseActivity.merge(QuoraPost).extend({ type: z.literal('quora') }),
    BaseActivity.merge(SlackPost).extend({ type: z.literal('slack') }),
    BaseActivity.merge(SnapchatPost).extend({ type: z.literal('snapchat') }),
    BaseActivity.merge(SpotifyPost).extend({ type: z.literal('spotify') }),
    BaseActivity.merge(StocktwitsPost).extend({ type: z.literal('stocktwits') }),
    BaseActivity.merge(StrawmanPost).extend({ type: z.literal('strawman') }),
    BaseActivity.merge(TelegramPost).extend({ type: z.literal('telegram') }),
    BaseActivity.merge(TikTokPost).extend({ type: z.literal('tiktok') }),
    BaseActivity.merge(TradingQnAPost).extend({ type: z.literal('tradingqna') }),
    BaseActivity.merge(TumblrPost).extend({ type: z.literal('tumblr') }),
    BaseActivity.merge(VimeoPost).extend({ type: z.literal('vimeo') }),
    BaseActivity.merge(WeChatPost).extend({ type: z.literal('wechat') }),
    BaseActivity.merge(WhatsAppPost).extend({ type: z.literal('whatsapp') }),
    BaseActivity.merge(WhirlpoolFinancePost).extend({
        type: z.literal('whirlpoolfinance')
    }),
    BaseActivity.merge(BogleHeadsPost).extend({ type: z.literal('bogleheads') }),
    BaseActivity.merge(youtubePost).extend({ type: z.literal('youtube') }),
    BaseActivity.merge(ADVFNPost).extend({ type: z.literal('advfn') }),
    BaseActivity.merge(ApplePodcastPost).extend({ type: z.literal('applepodcast') }),
    BaseActivity.merge(AudiblePost).extend({ type: z.literal('audible') }),
    BaseActivity.merge(AussieStockForumsPost).extend({
        type: z.literal('aussiestockforums')
    })
]);
export type Activity = z.infer<typeof Activity>;

export const ActivityRead = z.object({
    user: z.string().optional(),
    activity: z.string().optional()
});
export type ActivityRead = z.infer<typeof ActivityRead>;

export const ActivityArchived = z.object({
    organisation: z.string().optional(),
    activity: z.string().optional()
});
export type ActivityArchived = z.infer<typeof ActivityArchived>;

export const ActivityFlagged = z.object({
    organisation: z.string().optional(),
    activity: z.string().optional()
});
export type ActivityFlagged = z.infer<typeof ActivityFlagged>;
