import z from 'zod';

export const Alert = z.object({
    id: z.string().optional(),
    field: z.enum(['SENTIMENT', 'SHARE_PERCENT', 'ACTIVITY', 'SEARCH']),
    comparator: z.enum(['GTE', 'LTE']),
    threshold: z.number(),
    organisation: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    emails: z.string().email().array(),
    interval: z.union([z.enum(['DAY', 'WEEK', 'MONTH']), z.null()]),
    searchTerm: z.union([z.string(), z.null()])
});

export type AlertField = z.infer<typeof Alert.shape.field>;
export type AlertConfig = z.infer<typeof Alert>;
export type ActivityAlertInterval = z.infer<typeof Alert.shape.interval>;

export type AlertThreshold = {
    comparator: z.infer<typeof Alert.shape.comparator>;
    threshold: z.infer<typeof Alert.shape.threshold>;
};

export const AlertNotification = z.object({
    alert: z.string(),
    at: z.date()
});

export const searchTermRelevanceText: Map<number, string> = new Map([
    [0.48, 'very'],
    [0.41, 'moderately'],
    [0.3, 'somewhat']
]);

export type AlertNotification = z.infer<typeof AlertNotification>;
