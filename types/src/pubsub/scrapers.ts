import z from 'zod';

import { ASXAnnouncement } from '../ASXAnnouncement.js';
import { NewsArticle } from '../NewsArticle.js';
import { HotCopperPost } from '../HotCopperPost.js';
import { Tweet } from '../Tweet.js';
import { TwitterProfileSnapshot } from '../TwitterProfileSnapshot.js';
import { Activity2 } from '../Activity2.js';

export const PubSubMessage2 = z.discriminatedUnion('type', [
    z.object({
        type: z.literal('activities'),
        activities: z.array(Activity2)
    }),
    z.object({
        type: z.literal('twitter-profiles'),
        profiles: z.array(TwitterProfileSnapshot)
    }),
    z.object({
        type: z.literal('linkedIn-profiles'),
        profiles: z.array(TwitterProfileSnapshot)
    })
]);
export type PubSubMessage2 = z.infer<typeof PubSubMessage2>;

export const PubSubMessage = z.discriminatedUnion('type', [
    z.object({
        type: z.literal('asx-announcements'),
        symbol: z.string(),
        exchange: z.string(),
        announcements: z.array(ASXAnnouncement)
    }),
    z.object({
        type: z.literal('google-news'),
        symbol: z.string(),
        exchange: z.string(),
        articles: z.array(NewsArticle)
    }),
    z.object({
        type: z.literal('hotcopper'),
        symbol: z.string(),
        exchange: z.string(),
        posts: z.array(HotCopperPost)
    }),
    z.object({
        type: z.literal('twitter'),
        symbol: z.string(),
        exchange: z.string(),
        tweets: z.array(Tweet)
    }),
    z.object({
        type: z.literal('twitter-profile'),
        symbol: z.string(),
        exchange: z.string(),
        profiles: z.array(TwitterProfileSnapshot)
    })
]);

export type PubSubMessage = z.infer<typeof PubSubMessage>;
