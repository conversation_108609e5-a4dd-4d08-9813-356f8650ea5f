import z from 'zod';

export const LinkedInCookie = z.object({
    email: z.string(),
    cookie: z.array(
        z.object({
            domain: z.string(),
            expirationDate: z.number(),
            hostOnly: z.boolean(),
            httpOnly: z.boolean(),
            name: z.string(),
            path: z.string(),
            sameSite: z.string(),
            secure: z.boolean(),
            session: z.boolean(),
            storeId: z.string().nullable(),
            value: z.string()
        })
    ),
    lastUsed: z.date().nullable()
});
export type LinkedInCookie = z.infer<typeof LinkedInCookie>;
