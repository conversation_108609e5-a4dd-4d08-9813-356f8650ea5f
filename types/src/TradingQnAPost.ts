import z, { string } from 'zod';
import { Sentiment } from './Sentiment.js';

export const TradingQnAPost = z.object({
    id: string(),
    title: string(),
    posted: z.coerce.date(),
    body: string(),
    image: string().nullable(),
    likes: z.number(),
    isBroadcast: z.boolean().default(false),
    sentiment: Sentiment.optional()
});

export type TradingQnAPost = z.infer<typeof TradingQnAPost>;
