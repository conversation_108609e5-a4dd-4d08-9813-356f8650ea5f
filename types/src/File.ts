import { z } from 'zod';

export const ActivityFile = z.object({
    activity: z.string().optional(),
    fileName: z.string(),
    fileSize: z.number(),
    fileType: z.string(),
    storagePath: z.string().optional(),
    signedUrl: z.string().optional(),
    uploadedAt: z.date().optional()
});

export type ActivityFile = z.infer<typeof ActivityFile>;

export const ActivityFileUploadRequest = z.object({
    fileName: z.string().min(1),
    fileType: z.string().min(1),
    fileSize: z.number().positive()
});

export type ActivityFileUploadRequest = z.infer<typeof ActivityFileUploadRequest>;

export const ActivityFileDownloadResponse = z.object({
    signedUrl: z.string(),
    fileName: z.string(),
    fileType: z.string()
});

export type ActivityFileDownloadResponse = z.infer<typeof ActivityFileDownloadResponse>;
