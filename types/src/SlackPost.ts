import z, { string } from 'zod';
import { Sentiment } from './Sentiment.js';

export const SlackPost = z.object({
    id: string(),
    posted: z.coerce.date(),
    body: string(),
    image: string().nullable(),
    likes: z.number(),
    thread: string().nullable().optional(),
    isBroadcast: z.boolean().default(false),
    sentiment: Sentiment.optional()
});

export type SlackPost = z.infer<typeof SlackPost>;
