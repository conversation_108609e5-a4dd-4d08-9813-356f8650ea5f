import { z } from 'zod';

export const ApifyLinkedInPost = z
    .array(
        z.object({
            posts: z
                .array(
                    z.object({
                        share_url: z.string(),
                        commentary: z
                            .string()
                            .optional()
                            .transform((it) => it || ''),
                        actor: z
                            .object({
                                actor_image: z
                                    .string()
                                    .optional()
                                    .transform((it) => (it !== 'na' ? it : undefined)),
                                actor_name: z.string().optional(),
                                actor_navigationContext: z.string().optional()
                            })
                            .optional(),
                        social_details: z
                            .object({
                                numComments: z.number(),
                                numLikes: z.number()
                            })
                            .optional(),
                        articleComponent: z
                            .object({
                                title: z.string().optional()
                            })
                            .optional(),
                        postedAt: z.string(),
                        imageComponent: z.array(z.string()).optional(),
                        commentsUrn: z.string().optional(),
                        reactionsUrn: z.string()
                    })
                )
                .optional(),
            total_posts: z.number().optional(),
            error: z.string().optional()
        })
    )
    .transform((it) => it.filter((it) => !it.error).flatMap((it) => it.posts ?? []));

export type ApifyLinkedInPost = z.infer<typeof ApifyLinkedInPost>;
