import z from 'zod';

export const ApifyTweet = z.union([
    z.object({
        createdAt: z.string(),
        text: z.string(),
        url: z.string(),
        conversationId: z.string().optional(),
        extendedEntities: z
            .object({
                media: z
                    .array(
                        z.object({
                            type: z.string().optional(),
                            media_url_https: z.string().optional()
                        })
                    )
                    .optional()
            })
            .optional(),
        author: z.object({
            url: z.string().optional(),
            userName: z.string(),
            name: z.string().optional(),
            profilePicture: z.string().optional(),
            followers: z.number().optional(),
            following: z.number().optional()
        }),
        viewCount: z.number().optional(),
        likeCount: z.number().optional()
    }),
    z.object({
        noResults: z.literal(true)
    })
]);
export type ApifyTweet = z.infer<typeof ApifyTweet>;
