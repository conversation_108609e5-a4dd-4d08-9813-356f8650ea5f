import { z } from 'zod';

export const ApifyLinkedInComment = z
    .array(
        z.object({
            comments: z
                .array(
                    z.object({
                        timestamp: z.number(),
                        permalink: z.string(),
                        text: z
                            .string()
                            .optional()
                            .transform((it) => it || ''),
                        commenter: z.object({
                            image: z.string().optional(),
                            title: z.string(),
                            navigationUrl: z.string()
                        })
                    })
                )
                .optional(),
            error: z.string().optional()
        })
    )
    .transform((it) => it.filter((it) => !it.error).flatMap((it) => it.comments ?? []));

export type ApifyLinkedInComment = z.infer<typeof ApifyLinkedInComment>;
