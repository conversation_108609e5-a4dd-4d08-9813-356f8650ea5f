import z from 'zod';

const BaseSchema = z.object({
    id: z.string(),
    parsedId: z.string(),
    url: z.string().url(),
    username: z.string(),
    userId: z.string(),
    communityName: z.string(),
    body: z.string(),
    html: z.string(),
    createdAt: z.string().datetime(),
    scrapedAt: z.string().datetime(),
    upVotes: z.number()
});

const PostSchema = BaseSchema.extend({
    dataType: z.literal('post'),
    title: z.string(),
    parsedCommunityName: z.string(),
    link: z.string().url().optional(),
    numberOfComments: z.number(),
    flair: z.string().optional(),
    upVoteRatio: z.number().min(0).max(1),
    isVideo: z.boolean(),
    isAd: z.boolean(),
    over18: z.boolean(),
    thumbnailUrl: z.string().url().optional(),
    imageUrls: z.array(z.string().url()).optional()
});

const CommentSchema = BaseSchema.extend({
    dataType: z.literal('comment'),
    postId: z.string().startsWith('t3_'),
    parentId: z.string(),
    category: z.string(),
    numberOfreplies: z.number()
});

export const ApifyRedditItem = z.discriminatedUnion('dataType', [
    PostSchema,
    CommentSchema
]);

export type ApifyRedditItem = z.infer<typeof ApifyRedditItem>;
