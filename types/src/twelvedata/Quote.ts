import { z } from 'zod';

export const Quote = z.object({
    symbol: z.string(),
    exchange: z.string(),

    datetime: z.string(),

    open: z.coerce.number(),
    high: z.coerce.number(),
    low: z.coerce.number(),
    close: z.coerce.number(),
    volume: z.coerce.number(),
    change: z.coerce.number(),
    percent_change: z.coerce.number(),

    is_market_open: z.boolean()
});

export type Quote = z.infer<typeof Quote>;
