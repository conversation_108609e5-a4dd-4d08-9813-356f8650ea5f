import z from 'zod';
import { Sentiment } from './Sentiment.js';
import { ActivityFile } from './File.js';

export const HotCopper = z.object({
    id: z.number().optional(),
    sentiment: z.string().optional(),
    disclosure: z.string().optional(),
    thread: z
        .object({
            id: z.string().optional(),
            views: z.number().optional()
        })
        .optional()
});

export const author = z.object({
    key: z.string(),
    userId: z.string(),
    name: z.string(),
    image: z.string().nullable().optional(),
    url: z.string().url().nullable().optional(),
    followers: z.number().nullable().optional(),
    following: z.number().nullable().optional()
});

export const ASX = z.object({
    sensitive: z.boolean().optional()
});

export const Source = z.object({
    name: z.string().optional(),
    domain: z.string().optional()
});

export const News = z.object({
    image: z.string().optional(),
    source: Source.optional()
});

export const Tweet = z.object({});
export const LinkedIn = z.object({});
export const Castbox = z.object({});
export const Clubhouse = z.object({});
export const Discord = z.object({});
export const Facebook = z.object({});
export const IHeartRadio = z.object({});
export const Instagram = z.object({});
export const InvestorHub = z.object({});
export const Medium = z.object({});
export const Pinterest = z.object({});
export const Quora = z.object({});
export const Slack = z.object({});
export const Snapchat = z.object({});
export const Spotify = z.object({});
export const Stocktwits = z.object({});
export const Strawman = z.object({});
export const Telegram = z.object({});
export const TikTok = z.object({});
export const TradingQnA = z.object({});
export const Tumblr = z.object({});
export const Vimeo = z.object({});
export const WeChat = z.object({});
export const WhatsApp = z.object({});
export const WhirlpoolFinance = z.object({});
export const YouTube = z.object({});
export const AussieStockForums = z.object({});
export const BogleHeads = z.object({});
export const ADVFN = z.object({});
export const ApplePodcast = z.object({});
export const Audible = z.object({});

export const RedditPost = z.object({
    id: z.string().optional(), // Reddit post id, e.g. t3_123456
    subreddit: z.string().optional(),
    score: z.number().optional(),
    ratio: z.number().optional(),
    image: z.string().optional()
});

export const RedditComment = z.object({
    id: z.string().optional(), // Reddit comment id, e.g. t1_123456
    post: z.string().optional(), // Reddit post id, e.g. t3_123456
    parent: z.string().optional(), // Reddit ID of the parent, either a post or a comment
    score: z.number().optional()
});

export const Summary = z.object({
    activity: z.string().optional(),
    body: z.string().optional(),
    at: z.coerce.date().optional()
});
export type Summary = z.infer<typeof Summary>;

export const Archive = z.object({
    activity: z.string().optional(),
    organisation: z.string().optional()
});
export type Archive = z.infer<typeof Archive>;

export const Read = z.object({
    user: z.string().optional(),
    activity: z.string().optional(),
    at: z.date().optional()
});
export type Read = z.infer<typeof Read>;

export const Flag = z.object({
    activity: z.string().optional(),
    organisation: z.string().optional()
});
export type Flag = z.infer<typeof Flag>;

export const Activity2 = z.object({
    id: z.string().optional(),
    parent: z.string().optional(),

    posted: z.coerce.date().optional(),
    updated: z.coerce.date().optional(),

    url: z.string().url().optional(),

    title: z.string().optional(),
    body: z.string().optional(),
    author: author.optional(),

    sentiment: Sentiment.optional(),
    summary: Summary.optional(),
    likes: z.number().optional(),
    views: z.number().optional(),
    comments: z.number().optional(),
    image: z.string().optional(),
    symbol: z.string().optional(),
    exchange: z.string().optional(),
    thread: z.string().optional(),

    archives: Archive.array().optional(),
    flags: Flag.array().optional(),
    reads: Read.array().optional(),
    commentUrn: z.string().optional(),
    files: ActivityFile.array().optional(),

    hotcopper: HotCopper.optional(),
    asx: ASX.optional(),
    news: News.optional(),
    tweet: Tweet.optional(),
    linkedIn: LinkedIn.optional(),
    redditPost: RedditPost.optional(),
    redditComment: RedditComment.optional(),
    castbox: Castbox.optional(),
    clubhouse: Clubhouse.optional(),
    discord: Discord.optional(),
    facebook: Facebook.optional(),
    iheartradio: IHeartRadio.optional(),
    instagram: Instagram.optional(),
    investorhub: InvestorHub.optional(),
    medium: Medium.optional(),
    pinterest: Pinterest.optional(),
    quora: Quora.optional(),
    slack: Slack.optional(),
    snapchat: Snapchat.optional(),
    spotify: Spotify.optional(),
    stocktwits: Stocktwits.optional(),
    strawman: Strawman.optional(),
    telegram: Telegram.optional(),
    tiktok: TikTok.optional(),
    tradingqna: TradingQnA.optional(),
    tumblr: Tumblr.optional(),
    vimeo: Vimeo.optional(),
    wechat: WeChat.optional(),
    whatsapp: WhatsApp.optional(),
    whirlpoolfinance: WhirlpoolFinance.optional(),
    youtube: YouTube.optional(),
    aussiestockforums: AussieStockForums.optional(),
    advfn: ADVFN.optional(),
    bogleheads: BogleHeads.optional(),
    applePodcast: ApplePodcast.optional(),
    audible: Audible.optional(),
    isBroadcast: z.boolean().optional()
});

export type Activity2 = z.infer<typeof Activity2>;
export type Author = z.infer<typeof author>;
export type Source = z.infer<typeof Source>;
