import { JestConfigWithTsJest } from 'ts-jest';

function project(name: string, path: string) {
    return {
        preset: 'ts-jest',
        displayName: name,
        extensionsToTreatAsEsm: ['.ts', '.tsx'],
        testMatch: [`<rootDir>${path}/**/*.test.ts`],
        transform: {
            '\\.[jt]sx?$': [
                'ts-jest',
                {
                    tsconfig: `<rootDir>${path}/tsconfig.json`,
                    useESM: true
                }
            ]
        } as Record<string, [string, { tsconfig: string; useESM: boolean }]>,
        transformIgnorePatterns: ['.*.js'],

        // https://github.com/kulshekhar/ts-jest/issues/1057#issuecomment-1441733977
        moduleNameMapper: {
            '^(\\.\\.?\\/.+)\\.js$': '$1'
        }
    };
}

const config: JestConfigWithTsJest = {
    testEnvironment: 'node',
    projects: [
        project('ASX Scraper', '/scrapers/asx-announcements'),
        project('Google News Scraper', '/scrapers/google-news'),
        project('News Scraper', '/scrapers/news'),
        project('HotCopper Scraper', '/scrapers/hotcopper'),
        project('Reddit Scraper', '/scrapers/reddit'),
        project('Backend', '/backend')
    ]
};

export default config;
