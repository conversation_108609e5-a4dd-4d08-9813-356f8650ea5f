import { Har, Entry } from 'har-format';
import J<PERSON><PERSON><PERSON> from 'jszip';
import fs from 'node:fs/promises';
import path from 'node:path';

export interface Fetcher {
    fetch(input: string | URL | Request, init?: RequestInit): Promise<Response>;

    // Handle any clean up actions
    flush(): Promise<void>;
}

export class <PERSON>ttpFetcher implements Fetcher {
    async fetch(input: string | URL | Request, init?: RequestInit): Promise<Response> {
        return fetch(input, init);
    }

    async flush() {}
}

export class HarRecordingFetcher implements Fetcher {
    private path: string;
    private har: Har = {
        log: {
            version: '1.2',
            creator: { name: 'Quarterback', version: '1.0' },
            entries: []
        }
    };

    constructor(path: string) {
        this.path = path;
    }

    async flush() {
        const zip = new JSZip();
        const folder = zip.folder(path.parse(this.path).name)!;

        folder.file('har.har', JSON.stringify(this.har));

        await fs.writeFile(
            this.path,
            await zip.generateAsync({
                type: 'nodebuffer',
                compression: 'DEFLATE',
                compressionOptions: { level: 7 }
            })
        );
    }

    async fetch(input: string | URL | Request, init?: RequestInit): Promise<Response> {
        const startTime = Date.now();
        const response = await fetch(input, init);
        const endTime = Date.now();

        this.har.log.entries.push({
            startedDateTime: new Date(startTime).toISOString(),
            time: endTime - startTime,
            request: {
                method: init?.method || 'GET',
                url: input.toString(),
                headers: [], // init?.headers ? (init.headers as Record<string, string>) : {},
                httpVersion: 'HTTP/2.0',
                cookies: [],
                queryString: [],
                headersSize: 1,
                bodySize: 1
            },
            response: {
                status: response.status,
                statusText: response.statusText,
                httpVersion: 'HTTP/2.0',
                cookies: [],
                redirectURL: '',
                headersSize: 0,
                bodySize: 0,
                headers: [], // response.headers,
                content: {
                    mimeType: response.headers.get('content-type') || '',
                    text: await response.clone().text(), // Clone to preserve original response body
                    size: 0
                }
            },
            cache: {},
            timings: { dns: 0, connect: 0, ssl: 0, send: 0, wait: 0, receive: 0 }
        });

        return response;
    }
}

export class HarFetcher implements Fetcher {
    private path: string;
    private har: Har | undefined;

    constructor(path: string) {
        this.path = path;
    }

    async fetch(input: string | URL | Request, init?: RequestInit): Promise<Response> {
        // TODO: Probs clean this up and index by url

        if (!this.har) {
            this.har = JSON.parse(
                await JSZip.loadAsync(await fs.readFile(this.path)).then((zip) =>
                    zip.files[`${path.parse(this.path).name}/har.har`].async('text')
                )
            );
        }

        const request = this.har?.log.entries.find((it) => it.request.url === input.toString());

        if (request) {
            return new Response(request.response.content.text, { status: request.response.status });
        } else {
            return new Response('', { status: 404 });
        }
    }

    async flush() {}
}
