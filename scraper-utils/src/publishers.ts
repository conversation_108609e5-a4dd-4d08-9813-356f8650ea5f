import pubsub from '@google-cloud/pubsub';
import { log } from '@quarterback/util/gcp';

export abstract class Publisher<T> {
    topic: string;

    constructor(topic: string) {
        this.topic = topic;
    }

    abstract publish(message: T): Promise<void>;
}

export class PubSubPublisher<T> extends Publisher<T> {
    client = new pubsub.PubSub();

    constructor(topic: string) {
        super(topic);
    }

    async publish(message: T) {
        const topic = this.client.topic(this.topic);

        await topic.publishMessage({
            json: message
        });
    }
}

export class LoggingPublisher<T> extends Publisher<T> {
    async publish(message: T) {
        log('INFO', 'Published message', { body: message });
    }
}

export class MockPublisher<T> extends Publisher<T> {
    messages: Record<string, Array<T>> = {};

    async publish(message: T) {
        if (!(this.topic in this.messages)) {
            this.messages[this.topic] = [];
        }

        this.messages[this.topic].push(message);
    }

    clear() {
        this.messages = {};
    }
}
