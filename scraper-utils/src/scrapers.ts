import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';
import { Publisher } from './publishers.js';
import { Activity2 } from '@quarterback/types';

export abstract class Scraper {
    symbol: string;
    exchange: string;

    publisher: Publisher<PubSubMessage2>;

    constructor(symbol: string, exchange: string, publisher: Publisher<PubSubMessage2>) {
        this.symbol = symbol;
        this.exchange = exchange;
        this.publisher = publisher;
    }

    abstract chunk(): AsyncGenerator<Array<Activity2>, void, void>;

    async run() {
        for await (const chunk of this.chunk()) {
            await this.publisher.publish({
                type: 'activities',
                activities: chunk
            });
        }
    }
}
