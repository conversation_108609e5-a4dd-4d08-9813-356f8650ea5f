# Quarterback Platform Handover Documentation

This documentation is intended to cover the currently outstanding work and give a high level overview of the Quarterback platform and setup. The code is largely self documenting and will be straightforward for an experienced software engineer to understand.

---

## Overview
This repo encapsulates the Quarterback platform as a monorepo. The repo is broken up into a number of directories:

### ./backend
The backend REST API infrastructure and database connections. Anything that involves touching the database should go here.

### ./frontend
The NextJS frontend for the Quarterback platform. Changes to the UI are made here.

### ./extractotron
Extractotron encapsulates the Appendix 5B extraction and parsing code.

### ./infrastructure
The infrastructure directory houses all Pulumi config relating to the Quarterback platform. DO NOT USE CLICKOPS. EVER. Learn Pulumi.

### ./scrapers
Scrapers contains a number of sub directories for each scraper. These are largely set up the same and deployed to GCP.

---

## SAAS Tools
We use the following SAAS tools for various parts of our infrastructure:

| Name                  | Description                                                                           | URL                                |
|-----------------------|---------------------------------------------------------------------------------------|------------------------------------|
| GitHub                | Code repositories and continuous integration                                          | https://github.com/                |
| Sentry                | Error monitoring and reporting                                                        | https://sentry.io/                 |
| Supertokens           | User authentication and management                                                    | https://supertokens.com/           |
| Resend                | Transactional emails                                                                  | https://resend.com/                |
| Pulumi                | Infrastructure as code                                                                | https://pulumi.com/                |
| PostHog               | User tracking and event monitoring                                                    | https://posthog.com/               |
| Google Cloud Platform | Infrastructure                                                                        | https://clonsole.cloud.google.com/ |
| Pinecone              | Storing text embeddings and facilitating cosine distance lookups                      | https://pinecone.io/               |
| OpenAI                | Computing text embeddings for AI search, and text completion requests for AI insights | https://openai.com/api             |

---

## Running Locally
Running the platform locally is straightforward. Start by creating a copy of both `./backend/.env.example.` and `./frontend/.env.example`, naming them `.env.development.local` `.env.local` respectively. Populate these with the relevant environment variables.

Start by running `pnpm install` from the top level directory.

You'll need to install the GCP database proxy and gcloud command line tools. Check that both `gcloud` and `cloud-sql-proxy` are available.

You'll then need to run the backends GCP proxy, `proxy.dev.sh`. The script will handle authentication for you.

You can then run both the backend and frontend codebases from their respective directories with `pnpm dev`. The frontend will automatically recompile on change. `pnpm dev` will need to be restarted on the backend after each change.

Scrapers can be run either locally with `pnpm start`, ensuring you pass the relevant command line arguments. Arguments can be found in each scrapers `index.ts` file. When running scrapers locally they will not communicate with GCP and will simply log output to the stdout.

---

## Deploying
We have a deploy pipeline set up with GitHub Actions. Currently deploys are run as manual workflows from the GitHub Actions UI.

Each component of the platform is bundled into a Docker image, uploaded to the GCP artifact repository, and configured to run on cloud run, either as a job, or, in the case of the backend, a service.

Deploys can be run from https://github.com/quarterback-au/platform/actions.

---

## How stuff works
### Scrapers
We use two types of scrapers. Custom scrapers that run on GCP, and Apify scrapers.

The custom scrapers are set up as GCP cloud run jobs. These are triggered by cloud schedulers. The /scrapers backend endpoint is hit with a JSON POST body specifying the scrapers to start. The backend then uses the GCP API to trigger cloud run jobs over a period of 15 mins.

Apify scrapers use a similar mechanism, whereby Apify jobs are started as the result of a `/scrapers` endpoint call. An Apify job is run, via the Apify API, as a result.

Apify scrapers then hit the Apify webhook endpoint on completion with results being converted to the Activity2 type and forwarded to pub/sub.

Custom scrapers report their results as Activity2s directly to pub/sub.

The pub/sub endpoint will then accept scraper results, inserting them into the database and creating the relevant text embeddings and sentiment scores.

### Users/Supertokens
Users are managed via Supertokens. There are 2 logins. One for the Supertokens instance management dashboard. One for the user management dashboard.

The user management dashboard is available at https://app.qback.au/api/auth/dashboard. Sign in with your user management credentials or create new ones with:

```bash
curl --location --request POST 'https://st-prod-875fa940-39d7-11ef-adb4-41bd164204b4.aws.supertokens.io/recipe/dashboard/user' \
--header 'rid: dashboard' \
--header 'api-key: API_KEY' \
--header 'Content-Type: application/json' \
--data-raw '{"email": "EMAIL_ADDRESS","password": "PASSWORD"}'
```

The user management dashboard runs as part of the frontend repo. The backend makes calls to the frontend to verify auth tokens via JWKS.

--- 

## BDO/Appendix 5B
The BDO project consists of a Google Document AI model, found in GCP, and a number of scripts to pull down quarterly report PDFs and extract the relevant 5B pages. Refer to the Google Cloud Platform Document AI documentation for retraining and adding to the document parsing model. Everything related to this project can be found under the Appendix 5B GCP project.

### Running the BDO project
Running analysis over the quarterly reports requires a number of manual steps and configurations.

Start by running `pnpm download` from the `./extractotron` directory. This will download the entire PDF quarterly reports for all ASX listed companies into the `./extractotron/reports` directory.

You'll then need to run the `convert.py` script to find the specific Appendix 5B page numbers. Copy this output to the default object exported from the `pages.ts` file, converting it to the appropriate JSON structure. I'd recommend becoming very familiar with your IDEs multi-cursor tools for this.

*WARNING:* It costs about $150 AUD to run Document AI over 800 reports.

You can now run `pnpm extract`. Manually updating the chunk values in `extract.ts` after each run. This will extract the relevant pages and submit them in parallel to GCP for inference. Output will be saved to the `./extractotron/appendices` directory. Each symbol will have its own .json file with raw inference output.

To conver the JSON files to CSV, run `pnpm csvify`. Again ensuring you update the chunk range values in `csvify.ts`.

This can then be uploaded to Google Cloud Platform.

---

## In-progress
### Activities data structure migration
The Activity Zod model is currently in the process of migrating to Activity2. The new structure has a number of shared fields previously not present on Activity, and differentiates between activity types with the presence of a field for each type, rather than a specific type field. e.g. `{ hotcopper: {} }`, vs `{ type: 'hotcopper' }`. This makes it significantly easier to work with Activities on the frontend and reduces boilerplate.

### Repository migration
The og Activity type is managed with a rather gross monolithic repo. Selecting activities and activity counts is performed with a single query with a number of dynamic parameters. The old repository is located at `./backend/src/db/repositories/activities.ts`.

For Activity2 the repository has been migrated to `./backend/src/db/repositories/activities/*`. The new activities repository features more modular queries that can be expanded upon. The goal being better testability, with dependency injection and the ability for IO to be easily mocked. The new repository is more modular too, simplifying work required to add fields to activities and add new activity types.

### CSV tool normalisation
We're using a few different CSV tools across the stack. This should be normalised into a single CSV library.

### Testing
Part of the migration to Activity2 and recently completed normalisation of scraper types has been to allow for testability, mocking, and dependency injection. Test suites should be added to as bugs are discovered and the product is expanded.

### LinkedIn
LinkedIn is currently in progress, with the frontend OAuth flow largely complete (minus linkedin Page selection/connection).

Integrating with LinkedIn requires a number of weirdly specific workarounds. Images from LinkedIn, for example, must be authenticated and have a short lifespan. The /organisations endpoint has a very low rate limit and can only be hit a few times per day.

Remaining LinkedIn work includes:
- Storing the OAuth refresh/auth tokens in our database and keeping the tokens fresh and up to date.
- Listing the users pages so they may choose with which pages to connect.
- Listening to the LinkedIn activity webhook, aggregating the data, and storing in the databse.
- Frontend work to correctly format and display LinkedIn activities in activity cards and lists.
