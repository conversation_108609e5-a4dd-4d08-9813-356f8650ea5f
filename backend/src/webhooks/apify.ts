import { Router } from 'express';
import z, { ZodError } from 'zod';
import pubsub from '@google-cloud/pubsub';

import { log } from '@quarterback/util/gcp';
import {
    ApifyWebhook,
    ApifyTweet,
    ApifyTwitterProfile,
    ApifyLinkedInPost,
    ApifyLinkedinProfile,
    ApifyRedditItem,
    ApifyLinkedInComment
} from '@quarterback/types';

import Actor from '../schedulers/Actor.js';
import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';
import { parse } from 'date-fns';
import { isDefined } from '@quarterback/util';

const router: Router = Router();

const QuerySchema = z.object({
    symbol: z.string(),
    exchange: z.string(),
    actor: z.nativeEnum(Actor),
    twitterUsername: z.string().optional(),
    linkedInUsername: z.string().optional(),
    key: z.string()
});

async function fetchActorDataset(run: string): Promise<any> {
    const response = await fetch(
        `https://api.apify.com/v2/actor-runs/${run}/dataset/items?token=${process.env['APIFY_TOKEN']}`
    );
    if (!response.ok) {
        throw Error(`Invalid response ${response.status}`);
    } else {
        return await response.json();
    }
}

const client = new pubsub.PubSub();

async function publish(message: PubSubMessage2) {
    const id = await client.topic(process.env['SCRAPERS_PUBSUB_TOPIC']!).publishMessage({
        json: message
    });
    log('INFO', `Published apify results to pubsub with id ${id}`);
}

router.post('/', async (request, response) => {
    try {
        // TODO validate with key
        const { symbol, exchange, actor, key, twitterUsername, linkedInUsername } =
            QuerySchema.parse({
                ...request.query,
                actor: decodeURIComponent(request.query.actor as string) // FUCK apify is retarded
            });

        const {
            createdAt,
            eventData: { actorRunId }
        } = ApifyWebhook.parse(request.body);

        switch (actor) {
            case Actor.twitter:
                await publish({
                    type: 'activities',
                    activities: z
                        .array(ApifyTweet)
                        .parse(await fetchActorDataset(actorRunId))
                        .map((tweet) => {
                            if ('noResults' in tweet) {
                                return undefined;
                            } else {
                                return {
                                    posted: parse(
                                        tweet.createdAt,
                                        'EEE MMM dd HH:mm:ss xx yyyy',
                                        new Date()
                                    ),
                                    author: {
                                        key: `tweet__${tweet.author.userName}`,
                                        userId: tweet.author.userName,
                                        name: tweet.author.name ?? tweet.author.userName,
                                        image: tweet.author.profilePicture,
                                        url: tweet.author.url,
                                        followers: tweet.author.followers,
                                        following: tweet.author.following
                                    },
                                    body: tweet.text,
                                    image: tweet?.extendedEntities?.media?.filter(
                                        (it) => it.type === 'photo'
                                    )?.[0]?.media_url_https,
                                    url: tweet.url,
                                    thread: tweet?.conversationId ?? undefined,
                                    likes: tweet.likeCount ?? 0,
                                    views: tweet.viewCount ?? 0,
                                    isBroadcast:
                                        twitterUsername === tweet.author.userName,
                                    symbol,
                                    exchange,
                                    tweet: {}
                                };
                            }
                        })
                        .filter(isDefined)
                });

                break;
            case Actor.twitterFollowers:
                await publish({
                    type: 'twitter-profiles',
                    profiles: z
                        .array(ApifyTwitterProfile)
                        .parse(await fetchActorDataset(actorRunId))
                        .map((profile) => ({
                            at: createdAt,
                            followers: profile.legacy.followers_count,
                            username: profile.legacy.screen_name,
                            symbol,
                            exchange
                        }))
                });

                break;

            case Actor.linkedinFollowers:
                await publish({
                    type: 'linkedIn-profiles',
                    profiles: z
                        .array(ApifyLinkedinProfile)
                        .parse(await fetchActorDataset(actorRunId))
                        .map((profile) => ({
                            at: createdAt,
                            followers: profile.followerCount,
                            username:
                                new URL(profile.url)?.pathname.split('/')?.[2] ?? '',
                            symbol,
                            exchange
                        }))
                        .filter(isDefined)
                });

                break;

            case Actor.linkedin:
                const dataset: ApifyLinkedInPost | ApifyLinkedInComment =
                    await fetchActorDataset(actorRunId);

                if (ApifyLinkedInPost.parse(dataset).length > 0) {
                    await publish({
                        type: 'activities',
                        activities: ApifyLinkedInPost.parse(dataset)
                            ?.map((it) => {
                                const userId =
                                    it.actor?.actor_navigationContext?.match(
                                        /linkedin\.com\/(?:company|in|school|groups|pulse|jobs|learning|services|showcase)\/([^\/\?]+)/
                                    )?.[1] ?? '';

                                return {
                                    posted: parse(
                                        it.postedAt,
                                        "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
                                        new Date()
                                    ),
                                    author: {
                                        key: `linkedin__${userId}`,
                                        name: it?.actor?.actor_name!,
                                        userId: userId!,
                                        image: it?.actor?.actor_image,
                                        url: it?.actor?.actor_navigationContext
                                    },
                                    title: it?.articleComponent?.title,
                                    thread: it.reactionsUrn.split(':')?.[3]!,
                                    body: it?.commentary,
                                    url:
                                        new URL(it.share_url).origin +
                                        new URL(it.share_url).pathname,
                                    image: it?.imageComponent?.filter((url) =>
                                        /shrink_(800|1280|2048)/.test(url)
                                    )?.[0],
                                    likes: it?.social_details?.numLikes ?? 0,
                                    comments: it?.social_details?.numComments ?? 0,
                                    isBroadcast: linkedInUsername === userId,
                                    symbol,
                                    exchange,
                                    commentUrn: it.commentsUrn,
                                    linkedIn: {}
                                };
                            })
                            .filter(isDefined)
                    });
                }

                if (ApifyLinkedInComment.parse(dataset).length > 0) {
                    await publish({
                        type: 'activities',
                        activities: ApifyLinkedInComment.parse(dataset)
                            ?.map((it) => {
                                const userId =
                                    it.commenter?.navigationUrl?.match(
                                        /linkedin\.com\/(?:company|in|school|groups|pulse|jobs|learning|services|showcase)\/([^\/\?]+)/
                                    )?.[1] ?? '';

                                return {
                                    posted: new Date(it.timestamp),
                                    author: {
                                        key: `linkedin__${userId}`,
                                        name: it?.commenter?.title!,
                                        userId: userId!,
                                        image: it?.commenter?.image,
                                        url: it?.commenter?.navigationUrl
                                    },
                                    thread: it.permalink.match(
                                        /(?:ugcPost|urn:li:activity|share|article|pulse|post|comment|groupPost|repost|update|activity|video|event|discussion|status-update|ad):(\d+)/
                                    )?.[1],
                                    body: it?.text,
                                    url: it.permalink,
                                    likes: 0,
                                    comments: 0,
                                    symbol,
                                    exchange,
                                    linkedIn: {}
                                };
                            })
                            .filter(isDefined)
                    });
                }

                break;

            case Actor.reddit:
                await publish({
                    type: 'activities',
                    activities: z
                        .array(ApifyRedditItem)
                        .parse(await fetchActorDataset(actorRunId))
                        .map((post) => {
                            if (post.dataType === 'post') {
                                return {
                                    url: post.url,
                                    title: post.title,
                                    posted: new Date(post.createdAt),
                                    body: post.body,
                                    image: post?.imageUrls?.[0]
                                        ? decodeURIComponent(
                                              post?.imageUrls?.[0]
                                          )?.replace(/&amp;/g, '&')
                                        : undefined,
                                    author: {
                                        key: `reddit__${post.username}`,
                                        name: post.username,
                                        userId: post.userId
                                    },
                                    redditPost: {
                                        id: post.id,
                                        subreddit: post.parsedCommunityName,
                                        score: post.upVotes,
                                        ratio: post.upVoteRatio
                                    },
                                    isBroadcast: false,
                                    symbol,
                                    exchange
                                };
                            }

                            if (post.dataType === 'comment') {
                                return {
                                    url: post.url,
                                    posted: new Date(post.createdAt),
                                    body: post.body,
                                    author: {
                                        key: `reddit__${post.username}`,
                                        name: post.username,
                                        userId: post.userId
                                    },
                                    redditComment: {
                                        id: post.id,
                                        post: post.postId,
                                        parent: post.parentId,
                                        score: post.upVotes
                                    },
                                    symbol,
                                    exchange
                                };
                            }
                        })
                        .filter(isDefined)
                });

                break;
        }

        return response.status(200).json({ message: 'Many success' });
    } catch (error) {
        if (error instanceof ZodError) {
            log('INFO', `Bad request ${error}`, { body: request.body });
            return response.status(400).json('Bad request');
        } else {
            log('INFO', `Error: ${error}`);
            return response.status(400).json('Bad request');
        }
    }
});

export default router;
