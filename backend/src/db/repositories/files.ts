import { PgDatabase } from 'drizzle-orm/pg-core';
import { eq } from 'drizzle-orm';
import { activityFiles } from '../schema/activities.js';
import { Activity2, ActivityFile } from '@quarterback/types';

export function hasFiles(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'files'>> {
    return 'files' in activity && !!activity.files && activity.files.length > 0;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'files'>>>
): Promise<ActivityFile[]> {
    if (!activities.length) return [];

    const values = activities.reduce((acc, activity) => {
        return acc.concat(
            activity.files.map((file) => ({
                storagePath: file.storagePath!,
                fileName: file.fileName!,
                fileSize: file.fileSize!,
                fileType: file.fileType!,
                activity: activity.id!
            }))
        );
    }, [] as ActivityFile[]);

    const inserted = await db
        .insert(activityFiles)
        .values(
            values.map((file) => ({
                storagePath: file.storagePath!,
                fileName: file.fileName!,
                fileSize: file.fileSize!,
                fileType: file.fileType!,
                activity: file.activity!
            }))
        )
        .returning();

    return inserted;
}

export async function getByActivity(
    db: PgDatabase<any>,
    activityId: string
): Promise<ActivityFile[]> {
    return await db
        .select()
        .from(activityFiles)
        .where(eq(activityFiles.activity, activityId));
}

export async function getByStoragePath(
    db: PgDatabase<any>,
    path: string
): Promise<ActivityFile | undefined> {
    const results = await db
        .select()
        .from(activityFiles)
        .where(eq(activityFiles.storagePath, path))
        .limit(1);

    return results[0];
}

export async function deleteByPath(db: PgDatabase<any>, path: string): Promise<void> {
    await db.delete(activityFiles).where(eq(activityFiles.storagePath, path));
}
