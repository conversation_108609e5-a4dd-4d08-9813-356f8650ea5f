import { PgDatabase } from 'drizzle-orm/pg-core';
import { linkedInCookies } from '../../schema/activities.js';
import { desc, eq } from 'drizzle-orm';
import { LinkedInCookie } from '@quarterback/types';

export async function selectCookie(db: PgDatabase<any>): Promise<LinkedInCookie> {
    const cookies: LinkedInCookie[] = (await db.transaction(async (tx) => {
        return tx.select().from(linkedInCookies).orderBy(desc(linkedInCookies.lastUsed));
    })) as LinkedInCookie[];

    if (cookies.length === 0) {
        throw new Error('No cookies available');
    }

    // if we have more than one cookie, pick among the ones that has not been last used
    // or else select the first one

    const randomIndex =
        cookies.length > 1 ? Math.floor(Math.random() * (cookies.length - 1)) + 1 : 0;
    const selectedCookie = cookies[randomIndex];
    selectedCookie.lastUsed = new Date();

    await db.transaction(async (tx) => {
        await tx
            .update(linkedInCookies)
            .set({ lastUsed: selectedCookie.lastUsed })
            .where(eq(linkedInCookies.email, selectedCookie.email));
    });

    return selectedCookie;
}
