export function broadcastSummary(context: string) {
    return `You are an expert in summarising social media chatter about companies listed on stock exchanges.
We are using this investor chatter to help the company improve their investor relations strategy by understanding what investors care about.

Below you find a set of social media posts about a listed company.
---
${context}
---
Total output will be a brief and concise 2 paragraph summary of the above chatter, identifying main topics of chatter, concerns, and negative sentiment differentiated between criticism directed at the company and frustrations with external factors. 
Start with an overall summary and keep the paragraphs brief and to the point. Highlight the key phrases in bold that a user would likely click on to perform a search. Only highlight phrases that are specific, actionable, or central to the topic, ensuring they would yield relevant search results. No waffling.
`;
}

export function chatterSummary(
    context: string,
    about?: string | null | undefined,
    limit?: number,
    type: string = 'mixed'
) {
    return `You are an expert in summarising social media chatter about companies listed on stock exchanges.
We are using this investor chatter to help the company improve their investor relations strategy by understanding what investors care about.

Below you find a set of social media posts about a listed company.
---
${context}
---

${
    about?.length
        ? `In addition to the investor chatter, you know the following information about the company taken from their website:
---
${about}
---`
        : ''
}

Total output will be a brief and concise ${limit && limit > 0 ? limit + ' words (broken down into paragraphs where required)' : '2 paragraph'} summary of the above chatter, identifying main topics of chatter, concerns, and negative sentiment differentiated between criticism directed at the company and frustrations with external factors. 
Start with an overall summary and keep the paragraphs brief and to the point. Highlight the key phrases in bold that a user would likely click on to perform a search. Only highlight phrases that are specific, actionable, or central to the topic, ensuring they would yield relevant search results. No waffling. 

The type of the posts you are reading is ${type}. You may only mention the type in the summary if the type is not 'mixed'. If the type is 'mixed', avoid phrases like social media, chatter, broadcasts, etc.
`;
}

export function sentimentAnalysis(data: string) {
    return `You are an expert in sentiment analysis for financial data and social media chatter about publicly listed companies. Analyze the sentiment of the following social media post strictly in the context of financial markets, where negative wording (e.g., price drops, market volatility) may still indicate positive sentiment if it suggests opportunity (e.g., undervaluation, buying signals, confidence in recovery). Focus on intent, financial implications, and inferred investor outlook—not just emotional tone.
-----
${data}
-----
Return the result with **only** this exact JSON format, with double quotes on all property names and values where appropriate.Do NOT include any code block markers
{"score":<decimal between -1.00 and +1.00 with two decimal places>,"magnitude":<positive decimal>,"reason":"<short explanation of why this score and magnitude were assigned, based on financial context and implied sentiment>"}`;
}
