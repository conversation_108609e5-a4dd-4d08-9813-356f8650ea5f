import { Activity2 } from '@quarterback/types';
import { PgDatabase } from 'drizzle-orm/pg-core';
import { isSummarised } from '../summaries/index.js';
import OpenAI from 'openai';
import crypto from 'crypto';
import { insights } from '../../schema/ai.js';
import { and, eq, sql } from 'drizzle-orm';
import { encoding_for_model } from 'tiktoken';
import * as prompts from './prompts.js';
import { listedEntity } from '../../schema/market.js';

const encoding = encoding_for_model('gpt-4o');

// export async function broadcasts(
//     db: PgDatabase<any>,
//     openAi: OpenAI,
//     organisation: string,
//     activities: Array<Activity2 & Required<Pick<Activity2, 'asx'>>>
// ): Promise<string> {
//     const summaries = activities
//         .filter(isSummarised)
//         .map((activity) => activity.summary!.body)
//         .join('\n\n');

//     return await fetchInsights(
//         db,
//         openAi,
//         organisation,
//         '',
//         summaries,
//         prompts.broadcastSummary
//     );
// }

export async function chatter(
    db: PgDatabase<any>,
    openAi: OpenAI,
    organisation: string,
    symbol: string,
    activities: Array<Activity2>,
    limit?: number,
    type: string = 'mixed'
): Promise<string> {
    const content = activities
        .map(
            (activity) =>
                `${activity.title} - ${activity.author?.userId ?? ''}\n${activity.body}`
        )
        .join('\n\n');

    return await fetchInsights(
        db,
        openAi,
        organisation,
        symbol,
        content,
        prompts.chatterSummary,
        limit,
        type
    );
}

/**
 * Given a prompt factory and context string, either fetch a cached insight
 * or generate a new one
 */
async function fetchInsights(
    db: PgDatabase<any>,
    openAi: OpenAI,
    organisation: string,
    symbolWithExchange: string,
    context: string,
    promptFactory: (
        context: string,
        about?: string | null | undefined,
        limt?: number,
        type?: string
    ) => string,
    limit?: number,
    type: string = 'mixed'
): Promise<string> {
    const truncated = new TextDecoder().decode(
        encoding.decode(encoding.encode(context).slice(-50000))
    );

    const [symbol, exchange] = symbolWithExchange.split(':');

    const about = await db
        .select({
            about: listedEntity.about
        })
        .from(listedEntity)
        .where(and(eq(listedEntity.symbol, symbol), eq(listedEntity.exchange, exchange)))
        .then((it) => it[0]?.about);

    const prompt = promptFactory(truncated, about, limit, type);
    const hash = crypto.createHash('sha256').update(prompt).digest('hex');

    const [existing] = await db
        .select()
        .from(insights)
        .where(and(eq(insights.hash, hash), eq(insights.organisation, organisation)));

    if (existing) {
        return existing.summary;
    } else {
        const summary = await openAi.chat.completions.create({
            messages: [{ role: 'user', content: prompt }],
            model: 'gpt-4o-mini'
        });

        await db
            .insert(insights)
            .values([
                {
                    organisation,
                    hash,
                    summary: summary.choices[0].message.content!
                }
            ])
            .onConflictDoUpdate({
                target: [insights.hash, insights.organisation],
                set: { summary: sql`excluded.summary` }
            });

        return summary.choices[0].message.content!;
    }
}
