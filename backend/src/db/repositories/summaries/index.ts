import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2, Summary } from '@quarterback/types';
import { summary as activitySummary } from '../../schema/activities.js';
import OpenAI from 'openai';
import { isAsx } from '../activities/asx.js';
import { encoding_for_model } from 'tiktoken';
import { query } from '../activities/query.js';
import { eq, exists } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';

export function isSummarised(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'summary'>> {
    return 'summary' in activity;
}

function announcementSummaryPrompt(text: string) {
    return `
You are an expert in summarising announcements from companies listed on the stock exchange.
We use these announcements to understand communications made to the market.

Below you will find an announcement made by a listed company. 
---
${text}
---
Total output will be a summary of the announcement, including key points and any important information relevant to continuous disclosure obligations and any future commitments, promises, or timelines including dates.
Start with a brief overall summary of the announcement, followed by bullet points with comprehensive key details from the announcement. No waffling.
`;
}

const encoding = encoding_for_model('gpt-4o');

/**
 * Creates an AI summary for each activity. This is particularly useful when we want to
 * understand the content of large activities like news articles and ASX announcements.
 */
export async function create(
    openAi: OpenAI,
    db: PgDatabase<any>,
    activities: Array<Activity2>
) {
    const announcements = activities.filter(isAsx);

    if (announcements.length > 0) {
        for (const announcement of announcements) {
            const { body } = announcement;

            if (body && body.length) {
                const truncated = new TextDecoder().decode(
                    encoding.decode(encoding.encode(body).slice(-60000))
                );

                const summary = await openAi.chat.completions.create({
                    messages: [
                        { role: 'user', content: announcementSummaryPrompt(truncated) }
                    ],
                    model: 'gpt-4o-mini'
                });

                await db.insert(activitySummary).values([
                    {
                        summary: summary.choices[0].message.content!,
                        activity: announcement.id!
                    }
                ]);
            }
        }
    }
}

export const SelectSummarySchema = createSelectSchema(activitySummary);
export type SelectSummarySchema = z.infer<typeof SelectSummarySchema>;

function asModel(record: SelectSummarySchema): Summary {
    return {
        body: record.summary,
        activity: record.activity,
        at: record.at
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const summaries = await db
        .select()
        .from(activitySummary)
        .where(
            exists(
                db
                    .with(cte)
                    .select()
                    .from(cte)
                    .where(eq(activitySummary.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    summaries.forEach((summary) => {
        const activity = activities.get(summary.activity!);

        activities.set(summary.activity!, {
            ...activity,
            summary: {
                body: summary.body,
                at: summary.at
            }
        });
    });
}
