import { PgDatabase } from 'drizzle-orm/pg-core';
import { query } from './query.js';
import { Activity2, Flag } from '@quarterback/types';
import { flagged } from '../../schema/activities.js';
import { and, eq, exists } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { FlagFilter } from './index.js';

export const SelectFlagSchema = createSelectSchema(flagged);
export type SelectFlagSchema = z.infer<typeof SelectFlagSchema>;

function asModel(record: SelectFlagSchema): Flag {
    return {
        activity: record.activity,
        organisation: record.organisation
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    filter: FlagFilter | undefined,
    activities: Map<string, Activity2>
) {
    const flags = await db
        .select()
        .from(flagged)
        .where(
            exists(
                db
                    .with(cte)
                    .select()
                    .from(cte)
                    .where(
                        and(
                            eq(flagged.activity, cte.id),
                            filter?.organisation
                                ? eq(flagged.organisation, filter.organisation)
                                : undefined
                        )
                    )
            )
        )
        .then((it) => it.map(asModel));

    for (const flag of flags) {
        const activity = activities.get(flag.activity!);

        activities.set(flag.activity!, {
            ...activity,
            flags: [...(activity?.flags ?? []), flag]
        });
    }
}
