import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import {
    author,
    aussiestockforumsPost,
    advfnPost,
    applePodcastPost
} from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectApplePodcastSchema = createSelectSchema(applePodcastPost);
export type SelectApplePodcastSchema = z.infer<typeof SelectApplePodcastSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isApplePodcastPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'applePodcast'>> {
    return 'applePodcast' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'applePodcast'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(applePodcastPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image,
                    isBroadcast: activity.isBroadcast ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    ApplePodcastPost: SelectApplePodcastSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.ApplePodcastPost.activity,
        title: record.ApplePodcastPost.title,
        body: record.ApplePodcastPost.body,
        isBroadcast: record.ApplePodcastPost.isBroadcast,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        applePodcast: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const applePodcastPosts = await db
        .select()
        .from(applePodcastPost)
        .innerJoin(author, eq(applePodcastPost.user, author.key))
        .where(
            exists(
                db
                    .with(cte)
                    .select()
                    .from(cte)
                    .where(eq(applePodcastPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    applePodcastPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
