import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, telegramPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectTelegramSchema = createSelectSchema(telegramPost);
export type SelectTelegramSchema = z.infer<typeof SelectTelegramSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isTelegramPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'telegram'>> {
    return 'telegram' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'telegram'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(telegramPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image,
                    isBroadcast: activity.isBroadcast ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    TelegramPost: SelectTelegramSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.TelegramPost.activity,
        body: record.TelegramPost.body,
        isBroadcast: record.TelegramPost.isBroadcast,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        telegram: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const telegramPosts = await db
        .select()
        .from(telegramPost)
        .innerJoin(author, eq(telegramPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(telegramPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    telegramPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}