import { PgDatabase } from 'drizzle-orm/pg-core';
import { query } from './query.js';
import { Activity2, Archive } from '@quarterback/types';
import { archived } from '../../schema/activities.js';
import { eq, exists } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';

export const SelectArchivedSchema = createSelectSchema(archived);
export type SelectArchivedSchema = z.infer<typeof SelectArchivedSchema>;

function asModel(record: SelectArchivedSchema): Archive {
    return {
        activity: record.activity,
        organisation: record.organisation
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const archives = await db
        .select()
        .from(archived)
        .where(
            exists(db.with(cte).select().from(cte).where(eq(archived.activity, cte.id)))
        )
        .then((it) => it.map(asModel));

    for (const archive of archives) {
        const activity = activities.get(archive.activity!);

        activities.set(archive.activity!, {
            ...activity,
            archives: [...(activity?.archives ?? []), archive]
        });
    }
}
