import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, facebookPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectFacebookSchema = createSelectSchema(facebookPost);
export type SelectFacebookSchema = z.infer<typeof SelectFacebookSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isFacebookPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'facebook'>> {
    return 'facebook' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'facebook'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(facebookPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image,
                    isBroadcast: activity.isBroadcast ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    FacebookPost: SelectFacebookSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.FacebookPost.activity,
        body: record.FacebookPost.body,
        isBroadcast: record.FacebookPost.isBroadcast,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        facebook: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const facebookPosts = await db
        .select()
        .from(facebookPost)
        .innerJoin(author, eq(facebookPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(facebookPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    facebookPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}