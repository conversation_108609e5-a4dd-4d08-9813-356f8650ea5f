import { PgDatabase, PgSelect } from 'drizzle-orm/pg-core';
import { activity, asxAnnouncement, read } from '../../schema/activities.js';
import { and, desc, eq, exists, gt, inArray, lte, SQL } from 'drizzle-orm';
import { ActivityFetchFilter } from './index.js';
import withFilter from '../withFilter.js';

function withLimit<T extends PgSelect>(qb: T, limit: number | undefined) {
    if (limit !== undefined) {
        return qb.limit(limit);
    }

    return qb;
}

export function query(db: PgDatabase<any>, filter?: ActivityFetchFilter) {
    // TODO: if filter.hotcopper, for example, add where(exists(...))

    return db.$with('filtered-activities').as(
        withLimit(
            db
                .select()
                .from(activity)
                .where(
                    and(
                        withFilter(filter?.from, (it) => gt(activity.posted, it)),
                        withFilter(filter?.to, (it) => lte(activity.posted, it)),
                        withFilter(filter?.symbol, (it) => eq(activity.symbol, it)),
                        withFilter(filter?.exchange, (it) => eq(activity.exchange, it)),
                        withFilter(filter?.source, (it) => {
                            switch (it) {
                                case 'asx':
                                    return exists(
                                        db
                                            .select()
                                            .from(asxAnnouncement)
                                            .where(
                                                eq(activity.id, asxAnnouncement.activity)
                                            )
                                    );
                            }
                        }),
                        withFilter(filter?.ids, (it) => inArray(activity.id, it))
                    )
                )
                .orderBy(desc(activity.posted))
                .$dynamic(),
            filter?.limit
        )
    );
}
