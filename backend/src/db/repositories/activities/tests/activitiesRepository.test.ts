import { describe, it, expect } from '@jest/globals';
// import { InsertRedditCommentSchema, mapActivitiesToFlatComments } from '../reddit.js';
import { Activity2, RedditComment } from '@quarterback/types';
import * as z from 'zod';

describe('Activities repository', () => {
    it('flattens nested reddit comments', async () => {
        // const activities: Array<Activity2 & Required<Pick<Activity2, 'redditPost'>>> = [
        //     {
        //         id: 'activity-1',
        //         redditPost: {
        //             comments: [
        //                 {
        //                     id: 'comment-1',
        //                     body: 'Comment 1',
        //                     user: 'user-1',
        //                     score: 3,
        //                     replies: [
        //                         {
        //                             id: 'comment-2',
        //                             user: 'user-2',
        //                             body: 'Comment 2',
        //                             score: 2
        //                         }
        //                     ]
        //                 },
        //                 {
        //                     id: 'comment-3',
        //                     body: 'Comment 3',
        //                     user: 'user-2',
        //                     score: 4
        //                 }
        //             ]
        //         }
        //     },
        //     {
        //         id: 'activity-2',
        //         reddit: {
        //             comments: [
        //                 {
        //                     id: 'comment-4',
        //                     body: 'Comment 4',
        //                     score: 5,
        //                     user: 'user-2',
        //                     replies: [
        //                         {
        //                             id: 'comment-5',
        //                             user: 'user-3',
        //                             body: 'Comment 5',
        //                             score: 1
        //                         }
        //                     ]
        //                 },
        //                 {
        //                     id: 'comment-6',
        //                     body: 'Comment 6',
        //                     score: 0,
        //                     user: 'user-1'
        //                 }
        //             ]
        //         }
        //     }
        // ];
        // const comments = mapActivitiesToFlatComments(activities);
        // expect(
        //     z.array(InsertRedditCommentSchema).safeParse(comments).success
        // ).toBeTruthy();
        // expect(comments).toEqual([
        //     {
        //         id: 'comment-1',
        //         body: 'Comment 1',
        //         activity: 'activity-1',
        //         parent: undefined,
        //         score: 3,
        //         user: 'user-1'
        //     },
        //     {
        //         id: 'comment-2',
        //         body: 'Comment 2',
        //         activity: 'activity-1',
        //         parent: 'comment-1',
        //         score: 2,
        //         user: 'user-2'
        //     },
        //     {
        //         id: 'comment-3',
        //         body: 'Comment 3',
        //         activity: 'activity-1',
        //         parent: undefined,
        //         score: 4,
        //         user: 'user-2'
        //     },
        //     {
        //         id: 'comment-4',
        //         body: 'Comment 4',
        //         activity: 'activity-2',
        //         parent: undefined,
        //         score: 5,
        //         user: 'user-2'
        //     },
        //     {
        //         id: 'comment-5',
        //         body: 'Comment 5',
        //         activity: 'activity-2',
        //         parent: 'comment-4',
        //         score: 1,
        //         user: 'user-3'
        //     },
        //     {
        //         id: 'comment-6',
        //         body: 'Comment 6',
        //         activity: 'activity-2',
        //         parent: undefined,
        //         score: 0,
        //         user: 'user-1'
        //     }
        // ]);
    });
});
