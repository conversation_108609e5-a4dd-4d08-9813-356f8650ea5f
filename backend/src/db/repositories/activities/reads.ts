import { PgDatabase } from 'drizzle-orm/pg-core';
import { query } from './query.js';
import { Activity2, Read } from '@quarterback/types';
import { read } from '../../schema/activities.js';
import { eq, exists } from 'drizzle-orm';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';

export const SelectReadSchema = createSelectSchema(read);
export type SelectReadSchema = z.infer<typeof SelectReadSchema>;

function asModel(record: SelectReadSchema): Read {
    return {
        activity: record.activity,
        user: record.user,
        at: record.at
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const reads = await db
        .select()
        .from(read)
        .where(exists(db.with(cte).select().from(cte).where(eq(read.activity, cte.id))))
        .then((it) => it.map(asModel));

    for (const read of reads) {
        const activity = activities.get(read.activity!);

        activities.set(read.activity!, {
            ...activity,
            reads: [...(activity?.reads ?? []), read]
        });
    }
}
