import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, iheartradioPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectIheartradioSchema = createSelectSchema(iheartradioPost);
export type SelectIheartradioSchema = z.infer<typeof SelectIheartradioSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isIheartradioPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'iheartradio'>> {
    return 'iheartradio' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'iheartradio'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(iheartradioPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image,
                    isBroadcast: activity.isBroadcast ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    iHeartRadioPost: SelectIheartradioSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.iHeartRadioPost.activity,
        body: record.iHeartRadioPost.body,
        isBroadcast: record.iHeartRadioPost.isBroadcast,
        title: record.iHeartRadioPost.title,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        iheartradio: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const iheartradioPosts = await db
        .select()
        .from(iheartradioPost)
        .innerJoin(author, eq(iheartradioPost.user, author.key))
        .where(
            exists(
                db
                    .with(cte)
                    .select()
                    .from(cte)
                    .where(eq(iheartradioPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    iheartradioPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
