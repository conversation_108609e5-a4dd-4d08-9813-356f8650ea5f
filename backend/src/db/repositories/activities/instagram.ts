import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, instagramPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectInstagramSchema = createSelectSchema(instagramPost);
export type SelectInstagramSchema = z.infer<typeof SelectInstagramSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isInstagramPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'instagram'>> {
    return 'instagram' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'instagram'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(instagramPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image,
                    isBroadcast: activity.isBroadcast ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    InstagramPost: SelectInstagramSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.InstagramPost.activity,
        body: record.InstagramPost.body,
        isBroadcast: record.InstagramPost.isBroadcast,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        instagram: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const instagramPosts = await db
        .select()
        .from(instagramPost)
        .innerJoin(author, eq(instagramPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(instagramPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    instagramPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}