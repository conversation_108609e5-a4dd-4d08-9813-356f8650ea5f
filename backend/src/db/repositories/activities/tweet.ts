import { Activity2 } from '@quarterback/types';
import { eq, exists, sql } from 'drizzle-orm';
import { PgDatabase } from 'drizzle-orm/pg-core';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { author, twitterPost } from '../../schema/activities.js';
import { query } from './query.js';

export const SelectTweetSchema = createSelectSchema(twitterPost);
export type SelectTweetSchema = z.infer<typeof SelectTweetSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isTweet(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'tweet'>> {
    return 'tweet' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'tweet'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(twitterPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author?.key!,
                    likes: activity.likes ?? 0,
                    views: activity.views ?? 0,
                    body: activity.body!,
                    thread: activity.thread,
                    image: activity.image,
                    isBroadcast: activity.isBroadcast ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    TwitterPost: SelectTweetSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.TwitterPost.activity,
        body: record.TwitterPost.body,
        thread: record?.TwitterPost?.thread ?? undefined,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            url: record.Author.url,
            followers: record.Author.followers,
            following: record.Author.following
        },
        tweet: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const tweets = await db
        .select()
        .from(twitterPost)
        .innerJoin(author, eq(twitterPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(twitterPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    tweets.forEach((tweet) => {
        const activity = activities.get(tweet.id!);

        activities.set(tweet.id!, {
            ...activity,
            ...tweet
        });
    });
}
