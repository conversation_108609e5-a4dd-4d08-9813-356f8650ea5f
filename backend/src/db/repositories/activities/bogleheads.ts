import { PgDatabase } from 'drizzle-orm/pg-core';
import { Activity2 } from '@quarterback/types';
import { author, bogleheadsPost } from '../../schema/activities.js';
import { createSelectSchema } from 'drizzle-zod';
import * as z from 'zod';
import { query } from './query.js';
import { eq, exists, sql } from 'drizzle-orm';

export const SelectBogleheadsSchema = createSelectSchema(bogleheadsPost);
export type SelectBogleheadsSchema = z.infer<typeof SelectBogleheadsSchema>;

export const SelectAuthorSchema = createSelectSchema(author);
export type SelectAuthorSchema = z.infer<typeof SelectAuthorSchema>;

export function isBogleheadsPost(
    activity: Activity2
): activity is Activity2 & Required<Pick<Activity2, 'bogleheads'>> {
    return 'bogleheads' in activity;
}

export async function upsert(
    db: PgDatabase<any>,
    activities: Array<Activity2 & Required<Pick<Activity2, 'bogleheads'>>>
) {
    if (activities.length) {
        await db
            .insert(author)
            .values(
                activities.map((activity) => ({
                    key: activity.author?.key!,
                    userId: activity.author?.userId!,
                    name: activity?.author?.name,
                    image: activity.author!.image,
                    url: activity.author!.url,
                    followers: activity.author!.followers,
                    following: activity.author!.following
                }))
            )
            .onConflictDoNothing();

        await db
            .insert(bogleheadsPost)
            .values(
                activities.map((activity) => ({
                    activity: activity.id!,
                    user: activity.author!.key!,
                    title: activity.title!,
                    body: activity.body!,
                    likes: activity.likes ?? 0,
                    image: activity.image,
                    isBroadcast: activity.isBroadcast ?? false
                }))
            )
            .onConflictDoNothing();
    }
}

function asModel(record: {
    BogleHeads: SelectBogleheadsSchema;
    Author: SelectAuthorSchema;
}): Activity2 {
    return {
        id: record.BogleHeads.activity,
        title: record.BogleHeads.title,
        body: record.BogleHeads.body,
        isBroadcast: record.BogleHeads.isBroadcast,
        author: {
            userId: record.Author.userId!,
            name: record.Author.name!,
            key: record.Author.key!,
            image: record.Author.image,
            followers: record.Author.followers,
            following: record.Author.following
        },
        bogleheads: {}
    };
}

export async function expand(
    db: PgDatabase<any>,
    cte: ReturnType<typeof query>,
    activities: Map<string, Activity2>
) {
    const bogleheadsPosts = await db
        .select()
        .from(bogleheadsPost)
        .innerJoin(author, eq(bogleheadsPost.user, author.key))
        .where(
            exists(
                db.with(cte).select().from(cte).where(eq(bogleheadsPost.activity, cte.id))
            )
        )
        .then((it) => it.map(asModel));

    bogleheadsPosts.forEach((post) => {
        const activity = activities.get(post.id!);

        activities.set(post.id!, {
            ...activity,
            ...post
        });
    });
}
