import { PgDatabase } from 'drizzle-orm/pg-core';
import { read } from '../schema/activities.js';
import { and, eq, inArray, or } from 'drizzle-orm';

export async function activityRead(db: PgDatabase<any>, user: string, activities: Array<string>) {
    return await db.transaction(async (tx) => {
        return tx
            .insert(read)
            .values(activities.map((activity) => ({ activity, user: user })))
            .onConflictDoNothing()
            .returning();
    });
}

export async function activityUnread(db: PgDatabase<any>, user: string, activities: Array<string>) {
    return await db.transaction(async (tx) => {
        return tx
            .delete(read)
            .where(and(eq(read.user, user), inArray(read.activity, activities)))
            .returning();
    });
}
