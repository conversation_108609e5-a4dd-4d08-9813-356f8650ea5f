import * as z from 'zod';
import { Quote } from '@quarterback/types';

export const quote = {
    // for a quote, get the date, key the notification on the date
    async fetch(symbol: string, exchange: string): Promise<Quote> {
        const params = new URLSearchParams({
            symbol: `${symbol}:${exchange}`,
            apikey: '0175eda078884359b03ba1c47d5da744'
        });

        const response = await fetch(
            `https://api.twelvedata.com/quote?${params.toString()}`
        );

        if (response.ok) {
            return Quote.parse(await response.json());
        } else {
            throw new Error(`Error fetching quote: ${response.statusText}`);
        }
    }
};

const quotesSchema = z.record(z.string().regex(/^[A-Z0-9]{3}:[A-Z0-9]{3}$/), Quote);
export const quotes = {
    async fetch(entities: Array<{ symbol: string; exchange: string }>) {
        const params = new URLSearchParams({
            symbol: Array.from(
                new Set(entities.map((it) => `${it.symbol}:${it.exchange}`))
            ).join(','),
            apikey: '0175eda078884359b03ba1c47d5da744'
        });

        const response = await fetch(
            `https://api.twelvedata.com/quote?${params.toString()}`
        );

        if (response.ok) {
            return quotesSchema.parse(await response.json());
        } else {
            throw new Error(`Error fetching quote: ${response.statusText}`);
        }
    }
};
