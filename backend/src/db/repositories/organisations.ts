import { PgDatabase } from 'drizzle-orm/pg-core';
import id from '../schema/id.js';
import { organisation, organisationUserEntity } from '../schema/organisation.js';
import { and, eq, inArray } from 'drizzle-orm';
import { ActivityArchived, ListedEntity, Organisation } from '@quarterback/types';
import { listedEntity } from '../schema/market.js';
import { archived, flagged } from '../schema/activities.js';

export async function write(db: PgDatabase<any>, values: Array<Organisation>) {
    const organisations = new Map(values.map((it) => [it.id || id(), it]));

    await db.transaction(async (tx) => {
        await tx
            .insert(organisation)
            .values(
                [...organisations.entries()].map(([id, organisation]) => ({
                    id: id,
                    name: organisation.name
                }))
            )
            .onConflictDoNothing();

        await tx.delete(organisationUserEntity).where(
            inArray(
                organisationUserEntity.organisation,
                [...organisations.entries()].map((it) => it[0])
            )
        );

        await tx.insert(organisationUserEntity).values(
            [...organisations.entries()].flatMap(
                ([id, organisation]) =>
                    organisation.users?.flatMap(
                        (user) =>
                            user.entities?.map((entity) => ({
                                organisation: id,
                                user: user.id,
                                symbol: entity.symbol!,
                                exchange: entity.exchange!
                            })) ?? []
                    ) ?? []
            )
        );
    });
}

export async function read(
    db: PgDatabase<any>,
    options: { users: Array<string> } | undefined = undefined
): Promise<Array<Organisation>> {
    const organisations = await db
        .select()
        .from(organisation)
        .innerJoin(
            organisationUserEntity,
            and(
                eq(organisationUserEntity.organisation, organisation.id),
                options?.users && inArray(organisationUserEntity.user, options.users)
            )
        )
        .innerJoin(
            listedEntity,
            and(
                eq(listedEntity.symbol, organisationUserEntity.symbol),
                eq(listedEntity.exchange, organisationUserEntity.exchange)
            )
        );

    const combined = organisations.reduce(
        (acc, row) => {
            const organisation = row.Organisation;
            const userEntity = row.OrganisationUserEntity;
            const entity = row.ListedEntity;

            if (!acc[organisation.id]) {
                acc[organisation.id] = {
                    id: organisation.id,
                    name: organisation.name,
                    users: {}
                };
            }

            if (userEntity) {
                if (!acc[organisation.id].users[userEntity.user]) {
                    acc[organisation.id].users[userEntity.user] = {
                        id: userEntity.user,
                        entities: []
                    };
                }

                if (entity) {
                    acc[organisation.id].users[userEntity.user].entities.push({
                        ...entity,
                        twitterQuery: entity.twitterQuery ?? undefined,
                        twitterUsername: entity.twitterUsername ?? undefined,
                        newsQuery: entity.newsQuery ?? undefined,
                        redditQuery: entity.redditQuery ?? undefined,
                        linkedInQuery: entity.linkedInQuery ?? undefined,
                        linkedInUsername: entity.linkedInUsername ?? undefined,
                        linkedInCompanyId: entity.linkedInCompanyId ?? undefined,
                        about: entity.about ?? undefined
                    });
                }
            }

            return acc;
        },
        {} as Record<
            string,
            {
                id: string;
                name: string;
                users: Record<string, { id: string; entities: Array<ListedEntity> }>;
            }
        >
    );

    return Object.values(combined).map((combined) => ({
        ...combined,
        users: Object.values(combined.users)
    }));
}

export async function flagActivity(
    db: PgDatabase<any>,
    organisation: string,
    activities: Array<string>
): Promise<Array<ActivityArchived>> {
    return await db.transaction(async (tx) => {
        return tx
            .insert(flagged)
            .values(activities.map((activity) => ({ activity, organisation })))
            .onConflictDoNothing()
            .returning();
    });
}

export async function unflagActivity(
    db: PgDatabase<any>,
    organisation: string,
    activities: Array<string>
): Promise<Array<ActivityArchived>> {
    return await db.transaction(async (tx) => {
        return tx
            .delete(flagged)
            .where(
                and(
                    eq(flagged.organisation, organisation),
                    inArray(flagged.activity, activities)
                )
            )
            .returning();
    });
}

export async function archiveActivity(
    db: PgDatabase<any>,
    organisation: string,
    activities: Array<string>
): Promise<Array<ActivityArchived>> {
    return await db.transaction(async (tx) => {
        return tx
            .insert(archived)
            .values(activities.map((activity) => ({ activity, organisation })))
            .onConflictDoNothing()
            .returning();
    });
}

export async function unarchiveActivity(
    db: PgDatabase<any>,
    organisation: string,
    activities: Array<string>
): Promise<Array<ActivityArchived>> {
    return await db.transaction(async (tx) => {
        return tx
            .delete(archived)
            .where(
                and(
                    eq(archived.organisation, organisation),
                    inArray(archived.activity, activities)
                )
            )
            .returning();
    });
}
