import { PgDatabase } from 'drizzle-orm/pg-core';
import { TwitterProfileSnapshot } from '@quarterback/types';
import UpsertResult from '../UpsertResult.js';
import { linkedInProfileSnapshot } from '../../schema/socials.js';

export async function upsert(
    db: PgDatabase<any>,
    snapshots: Array<TwitterProfileSnapshot>
): Promise<UpsertResult<TwitterProfileSnapshot>> {
    const created = await db
        .insert(linkedInProfileSnapshot)
        .values(
            snapshots.map((snapshot) => ({
                at: snapshot.at,
                username: snapshot.username,
                followers: snapshot.followers,

                symbol: snapshot.symbol!,
                exchange: snapshot.exchange!
            }))
        )
        .onConflictDoNothing()
        .returning();

    return { created, updated: [] };
}
