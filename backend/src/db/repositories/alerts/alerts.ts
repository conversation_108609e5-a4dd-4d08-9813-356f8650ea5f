import { PgDatabase } from 'drizzle-orm/pg-core';
import { alertConfig, alertEmail, alertNotification } from '../../schema/alerts.js';
import { AlertConfig, AlertNotification } from '@quarterback/types';
import id from '../../schema/id.js';
import { and, eq, gte, inArray, notExists, sql } from 'drizzle-orm';
import { groupBy } from '@quarterback/util';
import * as z from 'zod';
import withFilter from '../withFilter.js';
import { createSelectSchema } from 'drizzle-zod';

const alertEmailToMap = (alertEmails: Array<{ alert: string; email: string }>) =>
    alertEmails.reduce(
        (acc, { alert, email }) => {
            if (alert && email) {
                (acc[alert] ||= []).push(email);
            }

            return acc;
        },
        {} as Record<string, string[]>
    );

export async function getAlertEmailsById(
    db: PgDatabase<any>,
    ids: string[]
): Promise<Record<string, string[]>> {
    const alertEmails = await db
        .select()
        .from(alertEmail)
        .where(inArray(alertEmail.alert, ids));

    return alertEmailToMap(alertEmails);
}

export async function getAlertConfigs(
    db: PgDatabase<any>,
    organisation: string,
    symbol: string
): Promise<AlertConfig[]> {
    const alertConfigs = await db
        .select()
        .from(alertConfig)
        .where(
            and(
                eq(alertConfig.symbol, symbol),
                eq(alertConfig.organisation, organisation)
            )
        );

    /* 
     So drizzle doesn't shit itself with an empty alertIds array
     Apparently fixed in drizzle-orm 0.32.1: 
     https://github.com/drizzle-team/drizzle-orm/releases/tag/0.32.1 
    */
    if (alertConfigs.length == 0) return [];

    const alertIds = alertConfigs.map(({ id }) => id);

    const emailsByAlertId = groupBy(
        await db.select().from(alertEmail).where(inArray(alertEmail.alert, alertIds)),
        (it) => it.alert
    );

    return alertConfigs.map((it) => ({
        ...it,
        emails: emailsByAlertId[it.id].map((it) => it.email) ?? []
    }));
}

export async function getAlertConfigsById(db: PgDatabase<any>, ids: string[]) {
    const alertConfigs = await db
        .select()
        .from(alertConfig)
        .where(inArray(alertConfig.id, ids));

    const alertEmailsById = await getAlertEmailsById(
        db,
        alertConfigs.map(({ id }) => id)
    );

    return alertConfigs.map((it) => ({
        ...it,
        emails: alertEmailsById[it.id]
    }));
}

export async function updateAlertConfigs(
    db: PgDatabase<any>,
    values: Array<AlertConfig>
) {
    const alertConfigById = new Map(values.map((it) => [it.id || id(), it]));
    const alertEmailById = [...alertConfigById.entries()].flatMap(([id, it]) =>
        it.emails.map((email) => [id, email])
    );

    return await db.transaction(async (tx) => {
        const alertConfigs = await tx
            .insert(alertConfig)
            .values(
                [...alertConfigById.entries()].map(
                    ([id, { emails, ...alertConfig }]) => ({
                        id: id,
                        ...alertConfig
                    })
                )
            )
            .onConflictDoUpdate({
                target: [alertConfig.id],
                set: {
                    field: sql`excluded.field`,
                    comparator: sql`excluded.comparator`,
                    threshold: sql`excluded.threshold`
                }
            })
            .returning();

        const alertEmails = await tx
            .insert(alertEmail)
            .values(
                alertEmailById.map(([id, email]) => ({
                    alert: id,
                    email
                }))
            )
            .onConflictDoUpdate({
                target: [alertEmail.alert, alertEmail.email],
                set: {
                    email: sql`excluded.email`
                }
            })
            .returning();

        const emailsByAlertId = alertEmailToMap(alertEmails);

        return alertConfigs.map((it) => ({
            ...it,
            emails: emailsByAlertId[it.id]
        }));
    });
}

export async function deleteAlertConfigs(
    db: PgDatabase<any>,
    values: Array<Pick<AlertConfig, 'id'>>
) {
    const ids = values.map((it) => it.id).filter((it) => it != undefined);

    return await db.transaction(async (tx) => {
        const alertConfigs = await tx
            .delete(alertConfig)
            .where(inArray(alertConfig.id, ids))
            .returning();

        const alertEmails = await tx
            .delete(alertEmail)
            .where(inArray(alertEmail.alert, ids))
            .returning();

        const emailsByAlertId = alertEmailToMap(alertEmails);

        return alertConfigs.map((it) => ({
            ...it,
            emails: emailsByAlertId[it.id]
        }));
    });
}

export async function updateAlertNotifications(
    db: PgDatabase<any>,
    values: Array<AlertNotification>
) {
    return await db.transaction(async (tx) => {
        return tx.insert(alertNotification).values(values).returning();
    });
}

export const SelectAlertConfigSchema = createSelectSchema(alertConfig);
export type SelectAlertConfigSchema = z.infer<typeof SelectAlertConfigSchema>;

function asModel(record: SelectAlertConfigSchema): AlertConfig {
    return {
        id: record.id,
        exchange: record.exchange,
        symbol: record.symbol,
        organisation: record.organisation,
        threshold: record.threshold,
        emails: [],
        field: record.field,
        comparator: record.comparator,
        interval: record.interval,
        searchTerm: record.searchTerm
    };
}

const AlertRulesFilter = z.object({
    type: z.enum(['SENTIMENT', 'SHARE_PERCENT', 'ACTIVITY', 'SEARCH']),
    history: z.object({
        notExists: z.object({
            from: z.date().optional()
        })
    })
});
type AlertRulesFilter = z.infer<typeof AlertRulesFilter>;

export const rules = {
    async fetch(
        db: PgDatabase<any>,
        filter?: AlertRulesFilter
    ): Promise<Array<AlertConfig>> {
        const rules = await db
            .select()
            .from(alertConfig)
            .where(
                and(
                    withFilter(filter?.type, (type) => eq(alertConfig.field, type)),
                    withFilter(filter?.history?.notExists?.from, (from) =>
                        notExists(
                            db
                                .select()
                                .from(alertNotification)
                                .where(
                                    and(
                                        eq(alertNotification.alert, alertConfig.id),
                                        gte(alertNotification.at, from)
                                    )
                                )
                        )
                    )
                )
            )
            .then((it) => it.map(asModel))
            .then((it) => new Map(it.map((it) => [it.id!, it])));

        if ([...rules.keys()].length > 0) {
            const emails = await db
                .select()
                .from(alertEmail)
                .where(inArray(alertEmail.alert, [...rules.keys()]));

            for (const email of emails) {
                const rule = rules.get(email.alert);

                if (rule) {
                    if (!rule.emails) rule.emails = [];
                    rule.emails.push(email.email);
                }
            }
        }

        return [...rules.values()];
    },
    async upsert() {
        return;
    }
};

const AlertHistoryFilter = z.object({
    from: z.date().optional()
});
type AlertHistoryFilter = z.infer<typeof AlertHistoryFilter>;

export const history = {
    async fetch(db: PgDatabase<any>, filter?: AlertHistoryFilter) {
        await db
            .select()
            .from(alertNotification)
            .where(
                and(
                    eq(alertConfig.id, alertNotification.alert),
                    withFilter(filter?.from, (it) => gte(alertNotification.at, it))
                )
            );
        return;
    },
    upsert() {
        return;
    }
};
