import { foreignKey, pgTable, primaryKey, varchar } from 'drizzle-orm/pg-core';
import id from './id.js';
import { listedEntity } from './market.js';

export const organisation = pgTable('Organisation', {
    id: varchar('id', { length: 32 }).primaryKey().$defaultFn(id),
    name: varchar('name', { length: 256 }).notNull()
});

export const organisationUserEntity = pgTable(
    'OrganisationUserEntity',
    {
        user: varchar('user', { length: 36 }).notNull(),
        organisation: varchar('organisation', { length: 32 }).notNull(),

        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({
            columns: [table.user, table.organisation, table.symbol, table.exchange]
        }),
        organisation: foreignKey({
            columns: [table.organisation],
            foreignColumns: [organisation.id]
        }),
        listedEntity: foreignKey({
            columns: [table.symbol, table.exchange],
            foreignColumns: [listedEntity.symbol, listedEntity.exchange]
        })
    })
);
