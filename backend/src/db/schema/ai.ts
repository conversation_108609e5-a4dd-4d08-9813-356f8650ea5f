import { foreignKey, pgTable, primaryKey, text, varchar } from 'drizzle-orm/pg-core';
import { organisation } from './organisation.js';
import { listedEntity } from './market.js';

export const insights = pgTable(
    'Insight',
    {
        hash: varchar('hash', { length: 64 }).notNull(),
        summary: text('summary').notNull(),

        organisation: varchar('organisation', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({
            columns: [table.hash, table.organisation]
        }),
        organisation: foreignKey({
            columns: [table.organisation],
            foreignColumns: [organisation.id]
        }).onDelete('cascade')
    })
);
