import {
    boolean,
    doublePrecision,
    foreignKey,
    pgEnum,
    pgTable,
    primaryKey,
    timestamp,
    varchar
} from 'drizzle-orm/pg-core';
import { organisation } from './organisation.js';
import { listedEntity } from './market.js';

export const fieldEnum = pgEnum('field', [
    'SENTIMENT',
    'SHARE_PERCENT',
    'ACTIVITY',
    'SEARCH'
]);

export const intervalEnum = pgEnum('intervalEnum', ['DAY', 'WEEK', 'MONTH']);

export const comparatorEnum = pgEnum('comparator', ['GTE', 'LTE']);

export const alertConfig = pgTable(
    'AlertConfig',
    {
        id: varchar('id', { length: 32 }).primaryKey(),
        field: fieldEnum('field').notNull(),
        comparator: comparatorEnum('comparator').notNull(),
        threshold: doublePrecision('threshold').notNull(),
        interval: intervalEnum('interval'),
        enabled: boolean('enabled').notNull().default(true),
        organisation: varchar('organisation', { length: 32 }).notNull(),
        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull(),
        searchTerm: varchar('searchTerm', { length: 256 })
    },
    (table) => ({
        listedEntity: foreignKey({
            columns: [table.symbol, table.exchange],
            foreignColumns: [listedEntity.symbol, listedEntity.exchange]
        }).onDelete('cascade'),
        organisation: foreignKey({
            columns: [table.organisation],
            foreignColumns: [organisation.id]
        }).onDelete('cascade')
    })
);

export const alertEmail = pgTable(
    'AlertEmail',
    {
        alert: varchar('alert', { length: 32 }).notNull(),
        email: varchar('email', { length: 256 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.alert, table.email] })
    })
);

export const alertNotification = pgTable(
    'AlertNotification',
    {
        alert: varchar('alert', { length: 32 }).notNull(),
        at: timestamp('at', { withTimezone: true }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.alert, table.at] }),
        alert: foreignKey({
            columns: [table.alert],
            foreignColumns: [alertConfig.id]
        }).onDelete('cascade')
    })
);
