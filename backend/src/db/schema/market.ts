import { pgTable, primaryKey, text, varchar } from 'drizzle-orm/pg-core';

export const listedEntity = pgTable(
    'ListedEntity',
    {
        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull(),

        name: varchar('name', { length: 128 }).notNull(),

        twitterUsername: varchar('twitterUsername', { length: 128 }).default(''),
        twitterQuery: text('twitterQuery').default(''),
        newsQuery: text('newsQuery').default(''),
        redditQuery: text('redditQuery').default(''),
        linkedInQuery: text('linkedInQuery').default(''),
        linkedInUsername: text('linkedInUsername').default(''),
        linkedInCompanyId: varchar('linkedInCompanyId', { length: 32 }),
        about: text('about')
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.symbol, table.exchange] })
    })
);
