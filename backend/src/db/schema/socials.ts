import { foreignKey, integer, pgTable, primaryKey, timestamp, varchar } from 'drizzle-orm/pg-core';
import { listedEntity } from './market.js';
import { organisation } from './organisation.js';

export const twitterProfileSnapshot = pgTable(
    'TwitterProfileSnapshot',
    {
        at: timestamp('at', { withTimezone: true }).notNull(),
        username: varchar('username', { length: 128 }).notNull(),
        followers: integer('followers').notNull(),

        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.at, table.username] }),
        listedEntity: foreignKey({
            columns: [table.symbol, table.exchange],
            foreignColumns: [listedEntity.symbol, listedEntity.exchange]
        })
    })
);

export const linkedInProfileSnapshot = pgTable(
    'LinkedInProfileSnapshot',
    {
        at: timestamp('at', { withTimezone: true }).notNull(),
        username: varchar('username', { length: 128 }).notNull(),
        followers: integer('followers').notNull(),

        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({ columns: [table.at, table.username] }),
        listedEntity: foreignKey({
            columns: [table.symbol, table.exchange],
            foreignColumns: [listedEntity.symbol, listedEntity.exchange]
        })
    })
);

export const mailchimpConfiguration = pgTable(
    'MailchimpConfiguration',
    {
        token: varchar('token', { length: 128 }).notNull(),
        audience: varchar('audience', { length: 128 }).notNull(),

        organisation: varchar('organisation', { length: 32 }).notNull(),

        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({
            columns: [table.organisation, table.symbol, table.exchange]
        }),
        organisation: foreignKey({
            columns: [table.organisation],
            foreignColumns: [organisation.id]
        }),
        listedEntity: foreignKey({
            columns: [table.symbol, table.exchange],
            foreignColumns: [listedEntity.symbol, listedEntity.exchange]
        })
    })
);

export const mailchimpSubscriberSnapshot = pgTable(
    'MailchimpSubscriberSnapshot',
    {
        at: timestamp('at', { withTimezone: true }).notNull(),
        audience: varchar('audience', { length: 128 }).notNull(),
        subscribers: integer('subscribers').notNull(),

        organisation: varchar('organisation', { length: 32 }).notNull(),

        symbol: varchar('symbol', { length: 32 }).notNull(),
        exchange: varchar('exchange', { length: 32 }).notNull()
    },
    (table) => ({
        primaryKey: primaryKey({
            columns: [table.organisation, table.symbol, table.exchange, table.audience, table.at]
        }),
        organisation: foreignKey({
            columns: [table.organisation],
            foreignColumns: [organisation.id]
        }),
        listedEntity: foreignKey({
            columns: [table.symbol, table.exchange],
            foreignColumns: [listedEntity.symbol, listedEntity.exchange]
        })
    })
);
