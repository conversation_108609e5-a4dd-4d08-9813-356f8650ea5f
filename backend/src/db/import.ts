import arg from 'arg';
import * as csv from '@fast-csv/parse';

import db from './index.js';
import * as activitiesRepository from './repositories/activities/index.js';
import { Activity2 } from '@quarterback/types';
import { parse as parseDate } from 'date-fns';
import * as embeddingsRepository from './repositories/embeddings/index.js';
import { pinecone } from './pinecone.js';

import OpenAI from 'openai';

interface NewsRow {
    Title: string;
    URL: string;
    Content: string;
    'Image URL': string;
    'Source Domain': string;
    'Source Name': string;
    Datetime: string;
}

const args = arg({
    '--symbol': String,
    '--exchange': String,

    // CSV parsing options
    '--path': String,
    '--headers': Boolean,

    // Activity options
    '--news': arg.COUNT,

    // Options
    '--dry': Boolean
});

if (!args['--symbol']) throw new Error('missing required argument: --symbol');
if (!args['--exchange']) throw new Error('missing required argument: --exchange');
if (!args['--path']) throw new Error('missing required argument: --path');
if (!args['--news'])
    throw new Error(
        'type not specified, missing one of: --news, --twitter, --hotcopper, --reddit'
    );

async function parseNews(path: string): Promise<Array<Activity2>> {
    const activities: Array<Activity2> = [];
    const headers = args['--headers']
        ? true
        : [
              'Title',
              'URL',
              'Content',
              'Image URL',
              'Source Domain',
              'Source Name',
              'Datetime'
          ];

    const stream = csv
        .parseFile<NewsRow, Activity2>(path, { headers })
        .transform((row: NewsRow): Activity2 => {
            if (!row['URL'])
                throw new Error(`Activities must have a URL. ${row['Title']}.`);

            return {
                title: row['Title'].trim(),
                posted: parseDate(row['Datetime'], 'yyyy-MM-dd H:mm', new Date()),
                url: row['URL'].trim(),
                body: row['Content'].trim(),
                news: {
                    ...(row['Image URL'] ? { image: row['Image URL'] } : {}),
                    source: {
                        name: row['Source Name'].trim(),
                        domain: row['Source Domain'].trim()
                    }
                },
                symbol: args['--symbol'],
                exchange: args['--exchange']
            };
        });

    for await (const activity of stream) {
        activities.push(activity);
    }

    return activities;
}

async function parse(path: string): Promise<Array<Activity2>> {
    if (args['--news']) {
        return await parseNews(path);
    } else {
        throw new Error(
            'type not specified, missing one of: --news, --twitter, --hotcopper, --reddit'
        );
    }
}

if (args['--dry']) {
    console.log(JSON.stringify(await parse(args['--path']!), undefined, 4));
} else {
    const openAi = new OpenAI();

    const { created } = await activitiesRepository.upsert(
        db,
        await parse(args['--path']!)
    );
    await embeddingsRepository.create(pinecone, openAi, created);

    console.log('Done!');
}
