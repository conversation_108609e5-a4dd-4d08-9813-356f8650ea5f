ALTER TABLE "ADVFNPost" DROP CONSTRAINT "ADVFNPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "ApplePodcastPost" DROP CONSTRAINT "ApplePodcastPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "AudiblePost" DROP CONSTRAINT "AudiblePost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "AussieStockForums" DROP CONSTRAINT "AussieStockForums_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "BogleHeads" DROP CONSTRAINT "BogleHeads_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "CastboxPost" DROP CONSTRAINT "CastboxPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "ClubhousePost" DROP CONSTRAINT "ClubhousePost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "DiscordPost" DROP CONSTRAINT "DiscordPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "FacebookPost" DROP CONSTRAINT "FacebookPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "iHeartRadioPost" DROP CONSTRAINT "iHeartRadioPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "InstagramPost" DROP CONSTRAINT "InstagramPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "InvestorHubPost" DROP CONSTRAINT "InvestorHubPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "MediumPost" DROP CONSTRAINT "MediumPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "PinterestPost" DROP CONSTRAINT "PinterestPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "QuoraPost" DROP CONSTRAINT "QuoraPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "SlackPost" DROP CONSTRAINT "SlackPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "SnapchatPost" DROP CONSTRAINT "SnapchatPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "SpotifyPost" DROP CONSTRAINT "SpotifyPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "StocktwitsPost" DROP CONSTRAINT "StocktwitsPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "StrawmanPost" DROP CONSTRAINT "StrawmanPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "TelegramPost" DROP CONSTRAINT "TelegramPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "TikTokPost" DROP CONSTRAINT "TikTokPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "TradingQnAPost" DROP CONSTRAINT "TradingQnAPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "TumblrPost" DROP CONSTRAINT "TumblrPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "VimeoPost" DROP CONSTRAINT "VimeoPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "WeChatPost" DROP CONSTRAINT "WeChatPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "WhatsAppPost" DROP CONSTRAINT "WhatsAppPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "WhirlpoolFinancePost" DROP CONSTRAINT "WhirlpoolFinancePost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "YouTubePost" DROP CONSTRAINT "YouTubePost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "ADVFNPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "ApplePodcastPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "AudiblePost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "AussieStockForums" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "BogleHeads" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "CastboxPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "ClubhousePost" ADD COLUMN "title" varchar(256);--> statement-breakpoint
ALTER TABLE "iHeartRadioPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "InvestorHubPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "MediumPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "PinterestPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "QuoraPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "SlackPost" ADD COLUMN "title" varchar(256);--> statement-breakpoint
ALTER TABLE "SpotifyPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "StrawmanPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "TradingQnAPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "TumblrPost" ADD COLUMN "title" varchar(256);--> statement-breakpoint
ALTER TABLE "VimeoPost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "WeChatPost" ADD COLUMN "title" varchar(256);--> statement-breakpoint
ALTER TABLE "WhirlpoolFinancePost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
ALTER TABLE "YouTubePost" ADD COLUMN "title" varchar(256) NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ADVFNPost" ADD CONSTRAINT "ADVFNPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ApplePodcastPost" ADD CONSTRAINT "ApplePodcastPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AudiblePost" ADD CONSTRAINT "AudiblePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AussieStockForums" ADD CONSTRAINT "AussieStockForums_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "BogleHeads" ADD CONSTRAINT "BogleHeads_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CastboxPost" ADD CONSTRAINT "CastboxPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ClubhousePost" ADD CONSTRAINT "ClubhousePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DiscordPost" ADD CONSTRAINT "DiscordPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "FacebookPost" ADD CONSTRAINT "FacebookPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "iHeartRadioPost" ADD CONSTRAINT "iHeartRadioPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "InstagramPost" ADD CONSTRAINT "InstagramPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "InvestorHubPost" ADD CONSTRAINT "InvestorHubPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MediumPost" ADD CONSTRAINT "MediumPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PinterestPost" ADD CONSTRAINT "PinterestPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "QuoraPost" ADD CONSTRAINT "QuoraPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SlackPost" ADD CONSTRAINT "SlackPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SnapchatPost" ADD CONSTRAINT "SnapchatPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SpotifyPost" ADD CONSTRAINT "SpotifyPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "StocktwitsPost" ADD CONSTRAINT "StocktwitsPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "StrawmanPost" ADD CONSTRAINT "StrawmanPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TelegramPost" ADD CONSTRAINT "TelegramPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TikTokPost" ADD CONSTRAINT "TikTokPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TradingQnAPost" ADD CONSTRAINT "TradingQnAPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TumblrPost" ADD CONSTRAINT "TumblrPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "VimeoPost" ADD CONSTRAINT "VimeoPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WeChatPost" ADD CONSTRAINT "WeChatPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WhatsAppPost" ADD CONSTRAINT "WhatsAppPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WhirlpoolFinancePost" ADD CONSTRAINT "WhirlpoolFinancePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "YouTubePost" ADD CONSTRAINT "YouTubePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
