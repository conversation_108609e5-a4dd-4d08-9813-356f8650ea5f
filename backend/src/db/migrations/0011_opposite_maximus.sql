CREATE TABLE IF NOT EXISTS "OrganisationUserEntity" (
	"user" varchar(32) NOT NULL,
	"organisation" varchar(32) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "OrganisationUserEntity_user_organisation_symbol_exchange_pk" PRIMARY KEY("user","organisation","symbol","exchange")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OrganisationUserEntity" ADD CONSTRAINT "OrganisationUserEntity_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OrganisationUserEntity" ADD CONSTRAINT "OrganisationUserEntity_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
