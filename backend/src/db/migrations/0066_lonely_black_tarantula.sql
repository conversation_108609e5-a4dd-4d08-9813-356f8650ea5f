UPDATE "TwitterPost" AS t
SET "isBroadcast" = true
FROM "Activity" act
JOIN "ListedEntity" le ON act."symbol" = le."symbol" AND act."exchange" = le."exchange"
JOIN "Author" a ON TRUE  
WHERE t."activity" = act."id"
  AND a."key" = t."user"
  AND a."userId" = le."twitterUsername";


UPDATE "LinkedInPost" AS t
SET "isBroadcast" = true
FROM "Activity" act
JOIN "ListedEntity" le ON act."symbol" = le."symbol" AND act."exchange" = le."exchange"
JOIN "Author" a ON TRUE  
WHERE t."activity" = act."id"
  AND a."key" = t."user"
  AND a."userId" = le."linkedInUsername";

UPDATE "HotCopperPost" AS t
SET "isBroadcast" = true
FROM "Activity" act
JOIN "Author" a ON TRUE  
WHERE t."activity" = act."id"
  AND a."key" = t."user"
  AND a."userId" = 'ASX News';




