CREATE TABLE IF NOT EXISTS "ADVFNPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ApplePodcastPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AudiblePost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AussieStockForums" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "BogleHeads" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "CastboxPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ClubhousePost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DiscordPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "FacebookPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);

--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "iHeartRadioPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "InstagramPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "InvestorHubPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);

--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "MediumPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "PinterestPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "QuoraPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "SlackPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "SnapchatPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "SpotifyPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "StocktwitsPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "StrawmanPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "TelegramPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "TikTokPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "TradingQnAPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "TumblrPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "VimeoPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "WeChatPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "WhatsAppPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "WhirlpoolFinancePost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "YouTubePost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL,
	"image" varchar(1024),
	"isBroadcast" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
ALTER TABLE "TwitterPost" DROP CONSTRAINT "TwitterPost_activity_Activity_id_fk";
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ADVFNPost" ADD CONSTRAINT "ADVFNPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ADVFNPost" ADD CONSTRAINT "ADVFNPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ApplePodcastPost" ADD CONSTRAINT "ApplePodcastPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ApplePodcastPost" ADD CONSTRAINT "ApplePodcastPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AudiblePost" ADD CONSTRAINT "AudiblePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AudiblePost" ADD CONSTRAINT "AudiblePost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AussieStockForums" ADD CONSTRAINT "AussieStockForums_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AussieStockForums" ADD CONSTRAINT "AussieStockForums_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "BogleHeads" ADD CONSTRAINT "BogleHeads_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "BogleHeads" ADD CONSTRAINT "BogleHeads_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CastboxPost" ADD CONSTRAINT "CastboxPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CastboxPost" ADD CONSTRAINT "CastboxPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ClubhousePost" ADD CONSTRAINT "ClubhousePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ClubhousePost" ADD CONSTRAINT "ClubhousePost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DiscordPost" ADD CONSTRAINT "DiscordPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DiscordPost" ADD CONSTRAINT "DiscordPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "FacebookPost" ADD CONSTRAINT "FacebookPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "FacebookPost" ADD CONSTRAINT "FacebookPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "iHeartRadioPost" ADD CONSTRAINT "iHeartRadioPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "iHeartRadioPost" ADD CONSTRAINT "iHeartRadioPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "InstagramPost" ADD CONSTRAINT "InstagramPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "InstagramPost" ADD CONSTRAINT "InstagramPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "InvestorHubPost" ADD CONSTRAINT "InvestorHubPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "InvestorHubPost" ADD CONSTRAINT "InvestorHubPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MediumPost" ADD CONSTRAINT "MediumPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MediumPost" ADD CONSTRAINT "MediumPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PinterestPost" ADD CONSTRAINT "PinterestPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PinterestPost" ADD CONSTRAINT "PinterestPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "QuoraPost" ADD CONSTRAINT "QuoraPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "QuoraPost" ADD CONSTRAINT "QuoraPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SlackPost" ADD CONSTRAINT "SlackPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SlackPost" ADD CONSTRAINT "SlackPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SnapchatPost" ADD CONSTRAINT "SnapchatPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SnapchatPost" ADD CONSTRAINT "SnapchatPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SpotifyPost" ADD CONSTRAINT "SpotifyPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SpotifyPost" ADD CONSTRAINT "SpotifyPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "StocktwitsPost" ADD CONSTRAINT "StocktwitsPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "StocktwitsPost" ADD CONSTRAINT "StocktwitsPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "StrawmanPost" ADD CONSTRAINT "StrawmanPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "StrawmanPost" ADD CONSTRAINT "StrawmanPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TelegramPost" ADD CONSTRAINT "TelegramPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TelegramPost" ADD CONSTRAINT "TelegramPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TikTokPost" ADD CONSTRAINT "TikTokPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TikTokPost" ADD CONSTRAINT "TikTokPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TradingQnAPost" ADD CONSTRAINT "TradingQnAPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TradingQnAPost" ADD CONSTRAINT "TradingQnAPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TumblrPost" ADD CONSTRAINT "TumblrPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TumblrPost" ADD CONSTRAINT "TumblrPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "VimeoPost" ADD CONSTRAINT "VimeoPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "VimeoPost" ADD CONSTRAINT "VimeoPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WeChatPost" ADD CONSTRAINT "WeChatPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WeChatPost" ADD CONSTRAINT "WeChatPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WhatsAppPost" ADD CONSTRAINT "WhatsAppPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WhatsAppPost" ADD CONSTRAINT "WhatsAppPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WhirlpoolFinancePost" ADD CONSTRAINT "WhirlpoolFinancePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WhirlpoolFinancePost" ADD CONSTRAINT "WhirlpoolFinancePost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "YouTubePost" ADD CONSTRAINT "YouTubePost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "YouTubePost" ADD CONSTRAINT "YouTubePost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TwitterPost" ADD CONSTRAINT "TwitterPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
