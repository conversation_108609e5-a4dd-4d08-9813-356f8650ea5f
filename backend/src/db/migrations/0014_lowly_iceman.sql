CREATE TABLE IF NOT EXISTS "ActivityRead" (
	"activity" varchar(32) NOT NULL,
	"user" varchar(36) NOT NULL,
	CONSTRAINT "ActivityRead_activity_user_pk" PRIMARY KEY("activity","user")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityRead" ADD CONSTRAINT "ActivityRead_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
