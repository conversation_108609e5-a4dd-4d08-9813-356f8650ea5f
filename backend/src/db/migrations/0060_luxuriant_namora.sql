DO $$ BEGIN
 ALTER TABLE "HotCopperPost" ADD CONSTRAINT "HotCopperPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "LinkedInPost" ADD CONSTRAINT "LinkedInPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "RedditComment" ADD CONSTRAINT "RedditComment_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "RedditPost" ADD CONSTRAINT "RedditPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TwitterPost" ADD CONSTRAINT "TwitterPost_user_Author_key_fk" FOREIGN KEY ("user") REFERENCES "public"."Author"("key") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
