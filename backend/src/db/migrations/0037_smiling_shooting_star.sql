DO $$ BEGIN
 CREATE TYPE "public"."intervalEnum" AS ENUM('DAY', 'WEEK', 'MONTH');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."OAuthProvider" AS ENUM('linkedin');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."OAuthResourceType" AS ENUM('urn:li:organization');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TYPE "field" ADD VALUE 'ACTIVITY';--> statement-breakpoint
ALTER TYPE "field" ADD VALUE 'SEARCH';--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "OAuthConnection" (
	"provider" "OAuthProvider" NOT NULL,
	"user" varchar(128) NOT NULL,
	"expires" timestamp NOT NULL,
	"scope" text NOT NULL,
	"accessToken" text NOT NULL,
	"refreshToken" text NOT NULL,
	CONSTRAINT "OAuthConnection_user_provider_pk" PRIMARY KEY("user","provider")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "OAuthResource" (
	"provider" "OAuthProvider",
	"user" varchar(128) NOT NULL,
	"expires" timestamp NOT NULL,
	"type" "OAuthResourceType",
	"id" varchar(256) NOT NULL,
	CONSTRAINT "OAuthResource_type_id_provider_user_unique" UNIQUE("type","id","provider","user")
);
--> statement-breakpoint
ALTER TABLE "AlertConfig" ADD COLUMN "interval" "intervalEnum";--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OAuthResource" ADD CONSTRAINT "OAuthResource_provider_user_OAuthConnection_provider_user_fk" FOREIGN KEY ("provider","user") REFERENCES "public"."OAuthConnection"("provider","user") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "OAuthConnection_provider_index" ON "OAuthConnection" USING btree ("provider");