-- Custom SQL migration file, put you code below! --

-- Populate Author table with data from <PERSON><PERSON><PERSON>
INSERT INTO "Author" ("key", "userId", "name")
SELECT 
    CONCAT('tweet__', "user") AS "key",
    "user" AS "userId",
    "user" AS "name"
FROM "TwitterPost"
WHERE NOT EXISTS (
    SELECT 1 FROM "Author" WHERE "key" = CONCAT('tweet__', "TwitterPost"."user")
)
ON CONFLICT ("key") DO NOTHING;

-- Populate Author table withs data from LinkedInPost
INSERT INTO "Author" ("key", "userId", "name")
SELECT 
    CONCAT('linkedIn__', "userId") AS "key",
    "userId" AS "userId",
    "user" AS "name"
FROM "LinkedInPost"
WHERE "userId" IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM "Author" WHERE "key" = CONCAT('linkedIn__', "LinkedInPost"."userId")
)
ON CONFLICT ("key") DO NOTHING;

-- Populate Author table with data from RedditPost
INSERT INTO "Author" ("key", "userId", "name")
SELECT 
    CONCAT('reddit__', "user") AS "key",
    "user" AS "userId",
    "user" AS "name"
FROM "RedditPost"
WHERE NOT EXISTS (
    SELECT 1 FROM "Author" WHERE "key" = CONCAT('reddit__', "RedditPost"."user")
)
ON CONFLICT ("key") DO NOTHING;

-- Populate Author table with data from RedditComment
INSERT INTO "Author" ("key", "userId", "name")
SELECT 
    CONCAT('reddit__', "user") AS "key",
    "user" AS "userId",
    "user" AS "name"
FROM "RedditComment"
WHERE NOT EXISTS (
    SELECT 1 FROM "Author" WHERE "key" = CONCAT('reddit__', "RedditComment"."user")
)
ON CONFLICT ("key") DO NOTHING;

-- Populate Author table with data from HotCopperPost
INSERT INTO "Author" ("key", "userId", "name")
SELECT 
    CONCAT('hotcopper__', "user") AS "key",
    "user" AS "userId",
    "user" AS "name"
FROM "HotCopperPost"
WHERE NOT EXISTS (
    SELECT 1 FROM "Author" WHERE "key" = CONCAT('hotcopper__', "HotCopperPost"."user")
)
ON CONFLICT ("key") DO NOTHING;


-- Update TwitterPost users
UPDATE "TwitterPost"
SET "user" = CONCAT('tweet__', "user")
WHERE "user" is not null;

-- Update LinkedInPost users
UPDATE "LinkedInPost"
SET "user" = CONCAT('linkedIn__', "userId")
WHERE "user" is not null;

-- Update RedditPost users
UPDATE "RedditPost"
SET "user" = CONCAT('reddit__', "user")
WHERE "user" is not null;

-- Update RedditComment users
UPDATE "RedditComment"
SET "user" = CONCAT('reddit__', "user")
WHERE "user" is not null;

-- Update HotCopperPost users
UPDATE "HotCopperPost"
SET "user" = CONCAT('hotcopper__', "user")
WHERE "user" is not null;