CREATE TABLE IF NOT EXISTS "ActivitySentiment" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"magnitude" integer NOT NULL,
	"score" integer NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivitySentiment" ADD CONSTRAINT "ActivitySentiment_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
