CREATE TABLE IF NOT EXISTS "AlertConfig" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"field" varchar(128) NOT NULL,
	"comparator" varchar(128) NOT NULL,
	"threshold" double precision NOT NULL,
	"organisation" varchar(32) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AlertNotification" (
	"alert" varchar(32),
	"at" timestamp with time zone NOT NULL,
	CONSTRAINT "AlertNotification_alert_at_pk" PRIMARY KEY("alert","at")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "MailchimpConfiguration" (
	"token" varchar(128) NOT NULL,
	"audience" varchar(128) NOT NULL,
	"organisation" varchar(32) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "MailchimpConfiguration_organisation_symbol_exchange_pk" PRIMARY KEY("organisation","symbol","exchange")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "MailchimpSubscriberSnapshot" (
	"at" timestamp with time zone NOT NULL,
	"audience" varchar(128) NOT NULL,
	"subscribers" integer NOT NULL,
	"organisation" varchar(32) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "MailchimpSubscriberSnapshot_organisation_symbol_exchange_audience_at_pk" PRIMARY KEY("organisation","symbol","exchange","audience","at")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AlertConfig" ADD CONSTRAINT "AlertConfig_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AlertConfig" ADD CONSTRAINT "AlertConfig_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AlertNotification" ADD CONSTRAINT "AlertNotification_alert_AlertConfig_id_fk" FOREIGN KEY ("alert") REFERENCES "public"."AlertConfig"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MailchimpConfiguration" ADD CONSTRAINT "MailchimpConfiguration_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MailchimpConfiguration" ADD CONSTRAINT "MailchimpConfiguration_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MailchimpSubscriberSnapshot" ADD CONSTRAINT "MailchimpSubscriberSnapshot_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MailchimpSubscriberSnapshot" ADD CONSTRAINT "MailchimpSubscriberSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
