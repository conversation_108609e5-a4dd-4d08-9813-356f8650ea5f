DO $$ BEGIN
 CREATE TYPE "public"."comparator" AS ENUM('GTE', 'LTE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."field" AS ENUM('SENTIMENT', 'SHARE_PERCENT', 'SHARE_PRICE');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "AlertConfig" ALTER COLUMN "field" SET DATA TYPE varchar;--> statement-breakpoint
ALTER TABLE "AlertConfig" ALTER COLUMN "comparator" SET DATA TYPE varchar;