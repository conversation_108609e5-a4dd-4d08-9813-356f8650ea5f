ALTER TABLE "ActivityArchived" DROP CONSTRAINT "ActivityArchived_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "ActivityFlagged" DROP CONSTRAINT "ActivityFlagged_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "ActivityRead" DROP CONSTRAINT "ActivityRead_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "ActivitySentiment" DROP CONSTRAINT "ActivitySentiment_activity_Activity_id_fk";
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityArchived" ADD CONSTRAINT "ActivityArchived_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityFlagged" ADD CONSTRAINT "ActivityFlagged_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityRead" ADD CONSTRAINT "ActivityRead_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivitySentiment" ADD CONSTRAINT "ActivitySentiment_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
