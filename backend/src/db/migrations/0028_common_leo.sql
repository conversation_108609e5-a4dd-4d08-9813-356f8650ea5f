CREATE TABLE IF NOT EXISTS "AlertConfig" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"field" "field" NOT NULL,
	"comparator" "comparator" NOT NULL,
	"threshold" double precision NOT NULL,
	"organisation" varchar(32) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AlertEmail" (
	"alert" varchar(32),
	"email" varchar(256),
	CONSTRAINT "AlertEmail_alert_email_pk" PRIMARY KEY("alert","email")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AlertNotification" (
	"alert" varchar(32),
	"at" timestamp with time zone NOT NULL,
	CONSTRAINT "AlertNotification_alert_at_pk" PRIMARY KEY("alert","at")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AlertConfig" ADD CONSTRAINT "AlertConfig_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AlertConfig" ADD CONSTRAINT "AlertConfig_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
