CREATE TABLE IF NOT EXISTS "AlertEmail" (
	"alert" varchar(32),
	"email" varchar(256),
	CONSTRAINT "AlertEmail_alert_email_pk" PRIMARY KEY("alert","email")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AlertEmail" ADD CONSTRAINT "AlertEmail_alert_AlertConfig_id_fk" FOREIGN KEY ("alert") REFERENCES "public"."AlertConfig"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
