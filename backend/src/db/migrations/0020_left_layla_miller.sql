CREATE TABLE IF NOT EXISTS "RedditComment" (
	"id" varchar(64) NOT NULL,
	"activity" varchar(32),
	"parent" varchar(64),
	"body" text NOT NULL,
	"score" integer NOT NULL,
	"subreddit" varchar(64),
	"user" varchar(128) NOT NULL,
	CONSTRAINT "RedditComment_id_activity_pk" PRIMARY KEY("id","activity")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "RedditPost" (
	"id" varchar(64) NOT NULL,
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"title" varchar(1024) NOT NULL,
	"body" text NOT NULL,
	"score" integer NOT NULL,
	"user" varchar(128) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "ActivityRead" ADD COLUMN "at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "ListedEntity" ADD COLUMN "redditQuery" text;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "RedditComment" ADD CONSTRAINT "RedditComment_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "RedditPost" ADD CONSTRAINT "RedditPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
