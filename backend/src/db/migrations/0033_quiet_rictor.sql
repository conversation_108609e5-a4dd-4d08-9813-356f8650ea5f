CREATE TABLE IF NOT EXISTS "Insight" (
	"hash" varchar(32) NOT NULL,
	"summary" text NOT NULL,
	"organisation" varchar(32) NOT NULL,
	CONSTRAINT "Insight_hash_organisation_pk" PRIMARY KEY("hash","organisation")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Insight" ADD CONSTRAINT "Insight_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
