CREATE TABLE IF NOT EXISTS "AISummary" (
	"hash" varchar(32) NOT NULL,
	"summary" text NOT NULL,
	"organisation" varchar(32) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "AISummary_hash_organisation_symbol_exchange_pk" PRIMARY KEY("hash","organisation","symbol","exchange")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AISummary" ADD CONSTRAINT "AISummary_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AISummary" ADD CONSTRAINT "AISummary_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
