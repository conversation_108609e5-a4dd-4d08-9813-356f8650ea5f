CREATE TABLE IF NOT EXISTS "ActivityFiles" (
	"storagePath" varchar(512) PRIMARY KEY NOT NULL,
	"activity" varchar(32) NOT NULL,
	"fileName" varchar(256) NOT NULL,
	"fileSize" integer NOT NULL,
	"fileType" varchar(128) NOT NULL,
	"uploadedAt" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
DROP TABLE IF EXISTS "OAuthConnection";--> statement-breakpoint
DROP TABLE IF EXISTS "OAuthResource";--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityFiles" ADD CONSTRAINT "ActivityFiles_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "ActivityFiles_activity_idx" ON "ActivityFiles" USING btree ("activity");