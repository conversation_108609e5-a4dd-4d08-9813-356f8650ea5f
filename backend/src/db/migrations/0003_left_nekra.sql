CREATE TABLE IF NOT EXISTS "TwitterPost" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"posted" timestamp with time zone NOT NULL,
	"url" varchar(1024) NOT NULL,
	"user" varchar(128) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "TwitterProfileSnapshot" (
	"at" timestamp with time zone NOT NULL,
	"username" varchar(128) NOT NULL,
	"followers" integer NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "TwitterProfileSnapshot_at_username_pk" PRIMARY KEY("at","username")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TwitterPost" ADD CONSTRAINT "TwitterPost_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TwitterProfileSnapshot" ADD CONSTRAINT "TwitterProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
