CREATE TABLE IF NOT EXISTS "Activity" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"key" varchar(1024) NOT NULL,
	"posted" timestamp with time zone NOT NULL,
	"url" varchar(1024) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "Activity_key_symbol_exchange_unique" UNIQUE("key","symbol","exchange")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ASXAnnouncement" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"title" varchar(256) NOT NULL,
	"priceSensitive" boolean NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "HotCopperPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"post" integer NOT NULL,
	"body" text NOT NULL,
	"user" varchar(128) NOT NULL,
	"likes" integer NOT NULL,
	"sentiment" varchar(64),
	"disclosure" varchar(64),
	"thread" integer NOT NULL,
	CONSTRAINT "HotCopperPost_post_unique" UNIQUE("post")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "HotCopperThread" (
	"thread" integer PRIMARY KEY NOT NULL,
	"title" varchar(256) NOT NULL,
	"views" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "NewsArticle" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"title" varchar(256) NOT NULL,
	"summary" text,
	"image" varchar(1024),
	"source" varchar(1024)
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "NewsSource" (
	"url" varchar(1024) PRIMARY KEY NOT NULL,
	"name" varchar(256) NOT NULL,
	"logo" varchar(1024)
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "TwitterPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"views" integer DEFAULT 0 NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Activity" ADD CONSTRAINT "Activity_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ASXAnnouncement" ADD CONSTRAINT "ASXAnnouncement_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "HotCopperPost" ADD CONSTRAINT "HotCopperPost_thread_HotCopperThread_thread_fk" FOREIGN KEY ("thread") REFERENCES "public"."HotCopperThread"("thread") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "HotCopperPost" ADD CONSTRAINT "HotCopperPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "NewsArticle" ADD CONSTRAINT "NewsArticle_source_NewsSource_url_fk" FOREIGN KEY ("source") REFERENCES "public"."NewsSource"("url") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "NewsArticle" ADD CONSTRAINT "NewsArticle_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TwitterPost" ADD CONSTRAINT "TwitterPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
