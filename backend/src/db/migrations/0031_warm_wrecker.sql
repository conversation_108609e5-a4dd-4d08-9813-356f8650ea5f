CREATE TABLE IF NOT EXISTS "RedditComment" (
	"id" varchar(64) NOT NULL,
	"activity" varchar(32) NOT NULL,
	"parent" varchar(64) NOT NULL,
	"body" text NOT NULL,
	"score" integer NOT NULL,
	"user" varchar(128) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "RedditPost" DROP CONSTRAINT "RedditPost_activity_Activity_id_fk";
--> statement-breakpoint
ALTER TABLE "RedditPost" ADD COLUMN "subreddit" varchar(64);--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "RedditComment" ADD CONSTRAINT "RedditComment_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "RedditPost" ADD CONSTRAINT "RedditPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
