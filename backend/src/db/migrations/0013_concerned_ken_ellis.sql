CREATE TABLE IF NOT EXISTS "ActivityArchived" (
	"activity" varchar(32) NOT NULL,
	"organisation" varchar(32) NOT NULL,
	CONSTRAINT "ActivityArchived_activity_organisation_pk" PRIMARY KEY("activity","organisation")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ActivityFlagged" (
	"activity" varchar(32) NOT NULL,
	"organisation" varchar(32) NOT NULL,
	CONSTRAINT "ActivityFlagged_activity_organisation_pk" PRIMARY KEY("activity","organisation")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityArchived" ADD CONSTRAINT "ActivityArchived_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityArchived" ADD CONSTRAINT "ActivityArchived_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityFlagged" ADD CONSTRAINT "ActivityFlagged_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ActivityFlagged" ADD CONSTRAINT "ActivityFlagged_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
