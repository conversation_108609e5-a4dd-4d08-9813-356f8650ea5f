CREATE TABLE IF NOT EXISTS "HotCopperPost" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"post" integer NOT NULL,
	"posted" timestamp with time zone NOT NULL,
	"url" varchar(1024) NOT NULL,
	"body" text NOT NULL,
	"user" varchar(128) NOT NULL,
	"likes" integer NOT NULL,
	"sentiment" varchar(64),
	"disclosure" varchar(64),
	"thread" integer NOT NULL,
	CONSTRAINT "HotCopperPost_post_unique" UNIQUE("post")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "HotCopperThread" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"thread" integer NOT NULL,
	"title" varchar(256) NOT NULL,
	"views" integer NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "HotCopperThread_thread_unique" UNIQUE("thread")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "NewsArticle" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"posted" timestamp with time zone NOT NULL,
	"title" varchar(256) NOT NULL,
	"summary" text,
	"url" varchar(1024) NOT NULL,
	"image" varchar(1024),
	"source" varchar(1024),
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "NewsSource" (
	"url" varchar(1024) PRIMARY KEY NOT NULL,
	"name" varchar(256) NOT NULL,
	"logo" varchar(1024)
);
--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" ALTER COLUMN "id" SET DATA TYPE varchar(32);--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" ALTER COLUMN "posted" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" ALTER COLUMN "title" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" ALTER COLUMN "pdf" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" ALTER COLUMN "priceSensitive" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" ALTER COLUMN "symbol" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "ASXAnnouncement" ALTER COLUMN "exchange" SET NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "HotCopperPost" ADD CONSTRAINT "HotCopperPost_thread_HotCopperThread_thread_fk" FOREIGN KEY ("thread") REFERENCES "public"."HotCopperThread"("thread") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "HotCopperThread" ADD CONSTRAINT "HotCopperThread_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "NewsArticle" ADD CONSTRAINT "NewsArticle_source_NewsSource_url_fk" FOREIGN KEY ("source") REFERENCES "public"."NewsSource"("url") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "NewsArticle" ADD CONSTRAINT "NewsArticle_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
