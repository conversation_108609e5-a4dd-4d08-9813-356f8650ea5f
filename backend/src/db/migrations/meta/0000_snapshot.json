{"id": "09b7f497-7ec1-4ce9-9d75-4fc4e0ac0b4b", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.ASXAnnouncement": {"name": "ASXAnnouncement", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": true, "notNull": true}, "posted": {"name": "posted", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "pdf": {"name": "pdf", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "priceSensitive": {"name": "priceSensitive", "type": "boolean", "primaryKey": false, "notNull": false}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"ASXAnnouncement_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "ASXAnnouncement_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "ASXAnnouncement", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ASXAnnouncement_pdf_unique": {"name": "ASXAnnouncement_pdf_unique", "nullsNotDistinct": false, "columns": ["pdf"]}}}, "public.ListedEntity": {"name": "ListedEntity", "schema": "", "columns": {"symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "twitterUsername": {"name": "twitterUsername", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "twitterQuery": {"name": "twitterQuery", "type": "text", "primaryKey": false, "notNull": false}, "newsQuery": {"name": "newsQuery", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"ListedEntity_symbol_exchange_pk": {"name": "ListedEntity_symbol_exchange_pk", "columns": ["symbol", "exchange"]}}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}