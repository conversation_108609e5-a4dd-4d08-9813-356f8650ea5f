{"id": "79f0b087-8bd8-4a09-889a-8517168d2f1c", "prevId": "7789ea18-f93c-4423-8b87-ef7ad6391e24", "version": "7", "dialect": "postgresql", "tables": {"public.Activity": {"name": "Activity", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": true}, "posted": {"name": "posted", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Activity_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "Activity_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "Activity", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Activity_key_symbol_exchange_unique": {"name": "Activity_key_symbol_exchange_unique", "nullsNotDistinct": false, "columns": ["key", "symbol", "exchange"]}}}, "public.ActivityArchived": {"name": "ActivityArchived", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ActivityArchived_activity_Activity_id_fk": {"name": "ActivityArchived_activity_Activity_id_fk", "tableFrom": "ActivityArchived", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ActivityArchived_organisation_Organisation_id_fk": {"name": "ActivityArchived_organisation_Organisation_id_fk", "tableFrom": "ActivityArchived", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"ActivityArchived_activity_organisation_pk": {"name": "ActivityArchived_activity_organisation_pk", "columns": ["activity", "organisation"]}}, "uniqueConstraints": {}}, "public.ASXAnnouncement": {"name": "ASXAnnouncement", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "priceSensitive": {"name": "priceSensitive", "type": "boolean", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"ASXAnnouncement_activity_Activity_id_fk": {"name": "ASXAnnouncement_activity_Activity_id_fk", "tableFrom": "ASXAnnouncement", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Author": {"name": "Author", "schema": "", "columns": {"key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": false}, "following": {"name": "following", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivityFlagged": {"name": "ActivityFlagged", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ActivityFlagged_activity_Activity_id_fk": {"name": "ActivityFlagged_activity_Activity_id_fk", "tableFrom": "ActivityFlagged", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ActivityFlagged_organisation_Organisation_id_fk": {"name": "ActivityFlagged_organisation_Organisation_id_fk", "tableFrom": "ActivityFlagged", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"ActivityFlagged_activity_organisation_pk": {"name": "ActivityFlagged_activity_organisation_pk", "columns": ["activity", "organisation"]}}, "uniqueConstraints": {}}, "public.HotCopperPost": {"name": "HotCopperPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "post": {"name": "post", "type": "integer", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true}, "sentiment": {"name": "sentiment", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "disclosure": {"name": "disclosure", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"HotCopperPost_thread_HotCopperThread_thread_fk": {"name": "HotCopperPost_thread_HotCopperThread_thread_fk", "tableFrom": "HotCopperPost", "tableTo": "HotCopperThread", "columnsFrom": ["thread"], "columnsTo": ["thread"], "onDelete": "no action", "onUpdate": "no action"}, "HotCopperPost_activity_Activity_id_fk": {"name": "HotCopperPost_activity_Activity_id_fk", "tableFrom": "HotCopperPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "HotCopperPost_user_Author_key_fk": {"name": "HotCopperPost_user_Author_key_fk", "tableFrom": "HotCopperPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"HotCopperPost_post_unique": {"name": "HotCopperPost_post_unique", "nullsNotDistinct": false, "columns": ["post"]}}}, "public.HotCopperThread": {"name": "HotCopperThread", "schema": "", "columns": {"thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.LinkedInCookies": {"name": "LinkedInCookies", "schema": "", "columns": {"email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": true, "notNull": true}, "cookie": {"name": "cookie", "type": "json", "primaryKey": false, "notNull": true}, "lastUsed": {"name": "lastUsed", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.LinkedInPost": {"name": "LinkedInPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "comments": {"name": "comments", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "commentUrn": {"name": "commentUrn", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"LinkedInPost_activity_Activity_id_fk": {"name": "LinkedInPost_activity_Activity_id_fk", "tableFrom": "LinkedInPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "LinkedInPost_user_Author_key_fk": {"name": "LinkedInPost_user_Author_key_fk", "tableFrom": "LinkedInPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.NewsArticle": {"name": "NewsArticle", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"NewsArticle_source_NewsSource_url_fk": {"name": "NewsArticle_source_NewsSource_url_fk", "tableFrom": "NewsArticle", "tableTo": "NewsSource", "columnsFrom": ["source"], "columnsTo": ["url"], "onDelete": "no action", "onUpdate": "no action"}, "NewsArticle_activity_Activity_id_fk": {"name": "NewsArticle_activity_Activity_id_fk", "tableFrom": "NewsArticle", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.NewsSource": {"name": "NewsSource", "schema": "", "columns": {"url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "logo": {"name": "logo", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivityRead": {"name": "ActivityRead", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ActivityRead_activity_Activity_id_fk": {"name": "ActivityRead_activity_Activity_id_fk", "tableFrom": "ActivityRead", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"ActivityRead_activity_user_pk": {"name": "ActivityRead_activity_user_pk", "columns": ["activity", "user"]}}, "uniqueConstraints": {}}, "public.RedditComment": {"name": "RedditComment", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "parent": {"name": "parent", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "post": {"name": "post", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"RedditComment_activity_Activity_id_fk": {"name": "RedditComment_activity_Activity_id_fk", "tableFrom": "RedditComment", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "RedditComment_user_Author_key_fk": {"name": "RedditComment_user_Author_key_fk", "tableFrom": "RedditComment", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.RedditPost": {"name": "RedditPost", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "ratio": {"name": "ratio", "type": "double precision", "primaryKey": false, "notNull": true}, "subreddit": {"name": "subreddit", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"RedditPost_activity_Activity_id_fk": {"name": "RedditPost_activity_Activity_id_fk", "tableFrom": "RedditPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "RedditPost_user_Author_key_fk": {"name": "RedditPost_user_Author_key_fk", "tableFrom": "RedditPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivitySentiment": {"name": "ActivitySentiment", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "magnitude": {"name": "magnitude", "type": "double precision", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "double precision", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"ActivitySentiment_activity_Activity_id_fk": {"name": "ActivitySentiment_activity_Activity_id_fk", "tableFrom": "ActivitySentiment", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ActivitySummary": {"name": "ActivitySummary", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ActivitySummary_activity_Activity_id_fk": {"name": "ActivitySummary_activity_Activity_id_fk", "tableFrom": "ActivitySummary", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.TwitterPost": {"name": "TwitterPost", "schema": "", "columns": {"activity": {"name": "activity", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "thread": {"name": "thread", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}}, "indexes": {"thread_idx": {"name": "thread_idx", "columns": [{"expression": "thread", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"TwitterPost_activity_Activity_id_fk": {"name": "TwitterPost_activity_Activity_id_fk", "tableFrom": "TwitterPost", "tableTo": "Activity", "columnsFrom": ["activity"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "TwitterPost_user_Author_key_fk": {"name": "TwitterPost_user_Author_key_fk", "tableFrom": "TwitterPost", "tableTo": "Author", "columnsFrom": ["user"], "columnsTo": ["key"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Insight": {"name": "Insight", "schema": "", "columns": {"hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"Insight_organisation_Organisation_id_fk": {"name": "Insight_organisation_Organisation_id_fk", "tableFrom": "Insight", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"Insight_hash_organisation_pk": {"name": "Insight_hash_organisation_pk", "columns": ["hash", "organisation"]}}, "uniqueConstraints": {}}, "public.AlertConfig": {"name": "Alert<PERSON>onfig", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "field": {"name": "field", "type": "field", "typeSchema": "public", "primaryKey": false, "notNull": true}, "comparator": {"name": "comparator", "type": "comparator", "typeSchema": "public", "primaryKey": false, "notNull": true}, "threshold": {"name": "threshold", "type": "double precision", "primaryKey": false, "notNull": true}, "interval": {"name": "interval", "type": "intervalEnum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "searchTerm": {"name": "searchTerm", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"AlertConfig_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "AlertConfig_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "Alert<PERSON>onfig", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "cascade", "onUpdate": "no action"}, "AlertConfig_organisation_Organisation_id_fk": {"name": "AlertConfig_organisation_Organisation_id_fk", "tableFrom": "Alert<PERSON>onfig", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AlertEmail": {"name": "AlertEmail", "schema": "", "columns": {"alert": {"name": "alert", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"AlertEmail_alert_email_pk": {"name": "AlertEmail_alert_email_pk", "columns": ["alert", "email"]}}, "uniqueConstraints": {}}, "public.AlertNotification": {"name": "AlertNotification", "schema": "", "columns": {"alert": {"name": "alert", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"AlertNotification_alert_AlertConfig_id_fk": {"name": "AlertNotification_alert_AlertConfig_id_fk", "tableFrom": "AlertNotification", "tableTo": "Alert<PERSON>onfig", "columnsFrom": ["alert"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"AlertNotification_alert_at_pk": {"name": "AlertNotification_alert_at_pk", "columns": ["alert", "at"]}}, "uniqueConstraints": {}}, "public.ListedEntity": {"name": "ListedEntity", "schema": "", "columns": {"symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "twitterUsername": {"name": "twitterUsername", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "default": "''"}, "twitterQuery": {"name": "twitterQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "newsQuery": {"name": "newsQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "redditQuery": {"name": "redditQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "linkedInQuery": {"name": "linkedInQuery", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "linkedInUsername": {"name": "linkedInUsername", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "linkedInCompanyId": {"name": "linkedInCompanyId", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"ListedEntity_symbol_exchange_pk": {"name": "ListedEntity_symbol_exchange_pk", "columns": ["symbol", "exchange"]}}, "uniqueConstraints": {}}, "public.OAuthConnection": {"name": "OAuthConnection", "schema": "", "columns": {"provider": {"name": "provider", "type": "OAuth<PERSON><PERSON><PERSON>", "typeSchema": "public", "primaryKey": false, "notNull": true}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": true}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": true}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false}, "refreshTokenExpires": {"name": "refreshTokenExpires", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"OAuthConnection_provider_index": {"name": "OAuthConnection_provider_index", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"OAuthConnection_user_provider_pk": {"name": "OAuthConnection_user_provider_pk", "columns": ["user", "provider"]}}, "uniqueConstraints": {}}, "public.OAuthResource": {"name": "OAuthResource", "schema": "", "columns": {"provider": {"name": "provider", "type": "OAuth<PERSON><PERSON><PERSON>", "typeSchema": "public", "primaryKey": false, "notNull": false}, "user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "OAuthResourceType", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"OAuthResource_provider_user_OAuthConnection_provider_user_fk": {"name": "OAuthResource_provider_user_OAuthConnection_provider_user_fk", "tableFrom": "OAuthResource", "tableTo": "OAuthConnection", "columnsFrom": ["provider", "user"], "columnsTo": ["provider", "user"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"OAuthResource_type_id_provider_user_unique": {"name": "OAuthResource_type_id_provider_user_unique", "nullsNotDistinct": false, "columns": ["type", "id", "provider", "user"]}}}, "public.Organisation": {"name": "Organisation", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.OrganisationUserEntity": {"name": "OrganisationUserEntity", "schema": "", "columns": {"user": {"name": "user", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"OrganisationUserEntity_organisation_Organisation_id_fk": {"name": "OrganisationUserEntity_organisation_Organisation_id_fk", "tableFrom": "OrganisationUserEntity", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "OrganisationUserEntity_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "OrganisationUserEntity_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "OrganisationUserEntity", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"OrganisationUserEntity_user_organisation_symbol_exchange_pk": {"name": "OrganisationUserEntity_user_organisation_symbol_exchange_pk", "columns": ["user", "organisation", "symbol", "exchange"]}}, "uniqueConstraints": {}}, "public.LinkedInProfileSnapshot": {"name": "LinkedInProfileSnapshot", "schema": "", "columns": {"at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"LinkedInProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "LinkedInProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "LinkedInProfileSnapshot", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"LinkedInProfileSnapshot_at_username_pk": {"name": "LinkedInProfileSnapshot_at_username_pk", "columns": ["at", "username"]}}, "uniqueConstraints": {}}, "public.MailchimpConfiguration": {"name": "MailchimpConfiguration", "schema": "", "columns": {"token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "audience": {"name": "audience", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"MailchimpConfiguration_organisation_Organisation_id_fk": {"name": "MailchimpConfiguration_organisation_Organisation_id_fk", "tableFrom": "MailchimpConfiguration", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "MailchimpConfiguration_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "MailchimpConfiguration_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "MailchimpConfiguration", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"MailchimpConfiguration_organisation_symbol_exchange_pk": {"name": "MailchimpConfiguration_organisation_symbol_exchange_pk", "columns": ["organisation", "symbol", "exchange"]}}, "uniqueConstraints": {}}, "public.MailchimpSubscriberSnapshot": {"name": "MailchimpSubscriberSnapshot", "schema": "", "columns": {"at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "audience": {"name": "audience", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "subscribers": {"name": "subscribers", "type": "integer", "primaryKey": false, "notNull": true}, "organisation": {"name": "organisation", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"MailchimpSubscriberSnapshot_organisation_Organisation_id_fk": {"name": "MailchimpSubscriberSnapshot_organisation_Organisation_id_fk", "tableFrom": "MailchimpSubscriberSnapshot", "tableTo": "Organisation", "columnsFrom": ["organisation"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "MailchimpSubscriberSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "MailchimpSubscriberSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "MailchimpSubscriberSnapshot", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"MailchimpSubscriberSnapshot_organisation_symbol_exchange_audience_at_pk": {"name": "MailchimpSubscriberSnapshot_organisation_symbol_exchange_audience_at_pk", "columns": ["organisation", "symbol", "exchange", "audience", "at"]}}, "uniqueConstraints": {}}, "public.TwitterProfileSnapshot": {"name": "TwitterProfileSnapshot", "schema": "", "columns": {"at": {"name": "at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true}, "followers": {"name": "followers", "type": "integer", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "exchange": {"name": "exchange", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"TwitterProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk": {"name": "TwitterProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk", "tableFrom": "TwitterProfileSnapshot", "tableTo": "ListedEntity", "columnsFrom": ["symbol", "exchange"], "columnsTo": ["symbol", "exchange"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"TwitterProfileSnapshot_at_username_pk": {"name": "TwitterProfileSnapshot_at_username_pk", "columns": ["at", "username"]}}, "uniqueConstraints": {}}}, "enums": {"public.comparator": {"name": "comparator", "schema": "public", "values": ["GTE", "LTE"]}, "public.field": {"name": "field", "schema": "public", "values": ["SENTIMENT", "SHARE_PERCENT", "ACTIVITY", "SEARCH"]}, "public.intervalEnum": {"name": "intervalEnum", "schema": "public", "values": ["DAY", "WEEK", "MONTH"]}, "public.OAuthProvider": {"name": "OAuth<PERSON><PERSON><PERSON>", "schema": "public", "values": ["linkedin"]}, "public.OAuthResourceType": {"name": "OAuthResourceType", "schema": "public", "values": ["urn:li:organization"]}}, "schemas": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}