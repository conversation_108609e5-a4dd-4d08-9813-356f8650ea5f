CREATE TABLE IF NOT EXISTS "Organisation" (
	"id" varchar(32) PRIMARY KEY NOT NULL,
	"name" varchar(256) NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "OrganisationEntity" (
	"organisation" varchar(32) NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "OrganisationEntity_organisation_symbol_exchange_pk" PRIMARY KEY("organisation","symbol","exchange")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "OrganisationUser" (
	"id" varchar(32) NOT NULL,
	"organisation" varchar(32) NOT NULL,
	CONSTRAINT "OrganisationUser_id_organisation_pk" PRIMARY KEY("id","organisation")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "OrganisationUserEntity" (
	"id" varchar(32) NOT NULL,
	"organisation" varchar(32) NOT NULL,
	"entity" varchar(32) NOT NULL,
	CONSTRAINT "OrganisationUserEntity_id_organisation_entity_pk" PRIMARY KEY("id","organisation","entity")
);
--> statement-breakpoint
ALTER TABLE "TwitterPost" ADD COLUMN "body" text NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OrganisationEntity" ADD CONSTRAINT "OrganisationEntity_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OrganisationEntity" ADD CONSTRAINT "OrganisationEntity_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OrganisationUser" ADD CONSTRAINT "OrganisationUser_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "OrganisationUserEntity" ADD CONSTRAINT "OrganisationUserEntity_organisation_Organisation_id_fk" FOREIGN KEY ("organisation") REFERENCES "public"."Organisation"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "TwitterPost" ADD CONSTRAINT "TwitterPost_url_unique" UNIQUE("url");