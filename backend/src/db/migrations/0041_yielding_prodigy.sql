CREATE TABLE IF NOT EXISTS "LinkedInPost" (
	"activity" varchar(32) PRIMARY KEY NOT NULL,
	"user" varchar(128) NOT NULL,
	"body" text NOT NULL,
	"image" varchar(1024),
	"comments" integer DEFAULT 0 NOT NULL,
	"likes" integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "LinkedInPost" ADD CONSTRAINT "LinkedInPost_activity_Activity_id_fk" FOREIGN KEY ("activity") REFERENCES "public"."Activity"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
