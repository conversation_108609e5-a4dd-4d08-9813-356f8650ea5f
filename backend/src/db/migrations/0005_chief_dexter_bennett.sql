CREATE TABLE IF NOT EXISTS "LinkedInProfileSnapshot" (
	"at" timestamp with time zone NOT NULL,
	"username" varchar(128) NOT NULL,
	"followers" integer NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	CONSTRAINT "LinkedInProfileSnapshot_at_username_pk" PRIMARY KEY("at","username")
);
--> statement-breakpoint
ALTER TABLE "TwitterPost" ADD COLUMN "views" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
ALTER TABLE "TwitterPost" ADD COLUMN "likes" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "LinkedInProfileSnapshot" ADD CONSTRAINT "LinkedInProfileSnapshot_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
