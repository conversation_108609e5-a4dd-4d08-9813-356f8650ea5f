CREATE TABLE IF NOT EXISTS "ASXAnnouncement" (
	"id" varchar(128) PRIMARY KEY NOT NULL,
	"posted" timestamp with time zone,
	"title" varchar(256),
	"pdf" varchar(256),
	"priceSensitive" boolean,
	"symbol" varchar(32),
	"exchange" varchar(32),
	CONSTRAINT "ASXAnnouncement_pdf_unique" UNIQUE("pdf")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ListedEntity" (
	"symbol" varchar(32) NOT NULL,
	"exchange" varchar(32) NOT NULL,
	"name" varchar(128) NOT NULL,
	"twitterUsername" varchar(128),
	"twitterQuery" text,
	"newsQuery" text,
	CONSTRAINT "ListedEntity_symbol_exchange_pk" PRIMARY KEY("symbol","exchange")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ASXAnnouncement" ADD CONSTRAINT "ASXAnnouncement_symbol_exchange_ListedEntity_symbol_exchange_fk" FOREIGN KEY ("symbol","exchange") REFERENCES "public"."ListedEntity"("symbol","exchange") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
