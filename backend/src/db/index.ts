import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import pgConnectionString from 'pg-connection-string';

const connectionString = pgConnectionString.parse(process.env['DATABASE_URL']!);

export const queryClient = postgres({
    host: connectionString.host ?? undefined,
    port: connectionString.port ? parseInt(connectionString.port) : undefined,
    user: connectionString.user,
    password: connectionString.password,
    database: connectionString.database ?? undefined
});

await queryClient`SET TIME ZONE 'Australia/Sydney'`;

export default drizzle(queryClient, { logger: false });
