import * as crypto from 'crypto';
import { base64 } from '@quarterback/util';
import Actor from '../schedulers/Actor.js';
import { log } from '@quarterback/util/gcp';

/*
AAAAAAAAHHHH for some reason some apify actors won't fucking accept
JSON that's completely valid if it contains a ~ character in a string.
So we have to ensure we encode all non-alphanumeric characters.
*/
function retard(str: string) {
    const extraCharacters = /[!'()*~]/g;

    return encodeURIComponent(str).replace(extraCharacters, (char) => {
        return '%' + char.charCodeAt(0).toString(16).toUpperCase();
    });
}

export default async function runActor(
    actor: Actor,
    symbol: string,
    exchange: string,
    twitterUsername: string | null | undefined,
    linkedInUsername: string | null | undefined,
    webhookBase: string,
    params: { [key: string]: any }
) {
    const hmac = crypto
        // TODO: make this a random secret passed in from pulumi
        .createHmac('sha1', 'topsecretquarterbackkey')
        .update(JSON.stringify([symbol, exchange, actor]))
        .digest('base64');

    const webhookParams = new URLSearchParams({
        symbol,
        exchange,
        actor: retard(actor),
        twitterUsername: twitterUsername ?? '',
        linkedInUsername: linkedInUsername ?? '',
        key: hmac
    });

    const webhooks = [
        {
            eventTypes: ['ACTOR.RUN.SUCCEEDED'],
            requestUrl: `${webhookBase}/webhooks/apify?${webhookParams.toString()}`
        }
    ];

    const response = await fetch(
        `https://api.apify.com/v2/acts/${actor}/runs?token=${process.env['APIFY_TOKEN']}&timeout=900&memory=1024&webhooks=${base64.encode(JSON.stringify(webhooks))}`,
        {
            method: 'POST',
            body: JSON.stringify({ ...params }),
            headers: {
                'Content-Type': 'application/json'
            }
        }
    );

    if (!response.ok) {
        throw Error(`Error starting Apify actor: ${await response.text()}`);
    }
}
