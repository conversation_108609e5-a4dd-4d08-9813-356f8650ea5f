import * as Sentry from '@sentry/node';

if (
    (['https://app.qback.au', 'https://app.qback.dev'] as Array<string | undefined>).includes(
        process.env['FRONTEND_URL']
    )
) {
    Sentry.init({
        dsn: 'https://<EMAIL>/4507520616497232',
        tracesSampleRate: 0.5,
        profilesSampleRate: 0.5,
        environment:
            process.env.FRONTEND_URL === 'https://app.qback.au' ? 'production' : 'development'
    });
}
