import { NextFunction, Request, Response } from 'express';
import metadata, { Metadata } from './metadata.js';

const meta = await metadata();

export interface GoogleCloudPlatformRequest extends Request {
    metadata?: Metadata;
}

export default async function middleware(
    request: GoogleCloudPlatformRequest,
    _: Response,
    next: NextFunction
) {
    request.metadata = meta;
    return next();
}
