import { log } from '@quarterback/util/gcp';

async function fetchServiceMeta(region: string, project: string, service: string, token: string) {
    const response = await fetch(
        `https://${region}-run.googleapis.com/apis/serving.knative.dev/v1/namespaces/${project}/services/${service}`,
        { headers: { Authorization: `Bearer ${token}` } }
    );

    if (!response.ok) throw Error();

    return response;
}

async function fetchMeta(path: string): Promise<Response> {
    const response = await fetch(`http://metadata.google.internal${path}`, {
        headers: { 'Metadata-Flavor': 'Google' }
    });

    if (!response.ok) throw Error();

    return response;
}

interface Token {
    access_token: string;
    expires_in: number;
    token_type: string;
}

interface ServiceMeta {
    status: {
        url: string;
    };
}

export interface Metadata {
    url: string;
    project: string;
    projectNumber: string;
    region: string;
}

export default async function metadata(): Promise<Metadata | undefined> {
    try {
        const project = await fetchMeta('/computeMetadata/v1/project/project-id').then((it) =>
            it.text()
        );

        const [_, projectNumber, region] =
            /projects\/(\d+)\/regions\/(.+)$/.exec(
                await fetchMeta('/computeMetadata/v1/instance/region').then((it) => it.text())
            ) || [];

        const { access_token: token } = await fetchMeta(
            '/computeMetadata/v1/instance/service-accounts/default/token'
        ).then((it) => it.json() as Promise<Token>);

        const service = await fetchServiceMeta(
            region,
            project,
            process.env['K_SERVICE']!,
            token
        ).then((it) => it.json() as Promise<ServiceMeta>);

        return {
            project,
            projectNumber,
            region,
            url: service.status.url
        };
    } catch (error) {
        log('ERROR', `Error fetching project metadata: ${error}`);
    }

    return undefined;
}
