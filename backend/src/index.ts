import './instrumentation.js';

import express from 'express';
import cors from 'cors';
import * as Sen<PERSON> from '@sentry/node';

import webhooks from './webhooks/index.js';
import pubsub from './pubsub/index.js';
import api from './api/index.js';
import admin from './admin/index.js';
import schedulers from './schedulers/index.js';

import gcloudAuth from './auth/gcloud.js';
import jwtAuth from './auth/jwt.js';

import gcloudMeta from './gcloud/middleware.js';

const app = express();

app.use(
    cors({
        origin: process.env['FRONTEND_URL'] ?? 'http://localhost:3000',
        allowedHeaders: ['content-type', 'authorization'],
        credentials: true
    })
);

app.use(express.json({ limit: '40mb' }));

app.use(
    '/pubsub',
    [gcloudMeta, gcloudAuth(process.env['INTERNAL_IAM_EMAIL'] ?? '')],
    pubsub
);

app.use(
    '/schedulers',
    [gcloudMeta, gcloudAuth(process.env['INTERNAL_IAM_EMAIL'] ?? '')],
    schedulers
);

app.use('/webhooks', webhooks);

app.use('/admin', jwtAuth(['admin']), admin);
app.use('/v1', jwtAuth(), api);

Sentry.setupExpressErrorHandler(app);

app.listen(8080);
