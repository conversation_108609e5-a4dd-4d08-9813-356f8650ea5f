import { log } from '@quarterback/util/gcp';
import { Request, Response } from 'express';
import z, { ZodError } from 'zod';

import * as market from '../db/repositories/market/index.js';

const QuerySchema = z.object({
    symbol: z.string(),
    exchange: z.string()
});
type QuerySchema = z.infer<typeof QuerySchema>;

export default async function quote(request: Request, response: Response) {
    try {
        const { symbol, exchange } = QuerySchema.parse(request.query);

        return response.status(200).json(await market.quote.fetch(symbol, exchange));
    } catch (error) {
        if (error instanceof ZodError) {
            log('INFO', `Bad request ${error}`, { body: request.body });
            return response.status(400).json('Bad request');
        } else {
            return response.status(500).json('Internal server error');
        }
    }
}
