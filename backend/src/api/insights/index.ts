import { Response, Router } from 'express';
import OpenAI from 'openai';
import * as z from 'zod';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import db from '../../db/index.js';
import * as activitiesRepository from '../../db/repositories/activities/index.js';
import * as insightsRepository from '../../db/repositories/insights/index.js';

const openAi = new OpenAI();
const router: Router = Router({ mergeParams: true });

const QueryParams = z.object({
    from: z.coerce.date().optional(),
    to: z.coerce.date().optional(),
    organisation: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    ids: z.string().array().optional(),
    limit: z.coerce.number().optional(),
    type: z.string().optional().default('mixed')
});

router.post('/', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, symbol, exchange, from, to, ids, limit, type } =
        QueryParams.parse(request.body);

    const activities = await activitiesRepository.fetch(db, {
        symbol,
        exchange,
        from,
        to,
        ids
    });

    return response
        .status(200)
        .json(
            await insightsRepository.chatter(
                db,
                openAi,
                organisation,
                `${symbol}:${exchange}`,
                activities,
                limit,
                type
            )
        );
});

export default router;
