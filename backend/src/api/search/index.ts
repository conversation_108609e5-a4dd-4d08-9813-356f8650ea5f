import { Router, Response } from 'express';
import z from 'zod';

import db from '../../db/index.js';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';

import * as activitiesRepository from '../../db/repositories/activities/index.js';
import OpenAI from 'openai';
import { log } from '@quarterback/util/gcp';
import { pinecone } from '../../db/pinecone.js';

const openAi = new OpenAI();
const index = pinecone.index('activities');

const router: Router = Router();

const QueryParams = z.object({
    organisation: z.string(),
    symbol: z.string(),
    exchange: z.string(),
    query: z.string().min(3),
    from: z.coerce.date().optional(),
    to: z.coerce.date().optional()
});

router.get('/', async (request: AuthenticatedRequest, response: Response) => {
    try {
        const query = QueryParams.parse(request.query);
        const embedding = await openAi.embeddings
            .create({
                input: query.query,
                model: 'text-embedding-3-small'
            })
            .then((it) => it.data[0].embedding);

        const matches = await index
            .query({
                vector: embedding,
                topK: 50,
                includeMetadata: true,
                filter: {
                    symbol: { $eq: query.symbol },
                    exchange: { $eq: query.exchange },
                    ...(query.from || query.to
                        ? {
                              posted: {
                                  ...(query.from
                                      ? { $gt: new Date(query.from).valueOf() }
                                      : {}),
                                  ...(query.to
                                      ? { $lte: new Date(query.to).valueOf() }
                                      : {})
                              }
                          }
                        : {})
                }
            })
            .then((it) => it.matches.filter((it) => (it.score ?? 0) > 0.25));

        if (!matches.length) {
            return response.status(200).json([]);
        }

        const activities = new Map(
            await activitiesRepository
                .fetch(db, {
                    symbol: query.symbol,
                    exchange: query.exchange,
                    expand: ['flags', 'archives', 'reads'],
                    flags: { organisation: query.organisation },
                    ids: matches.map((it) => it.id)
                })
                .then((it) => it.map((activity) => [activity.id, activity]))
        );

        return response.status(200).json(
            matches
                .map((match) => ({
                    activity: activities.get(match.id),
                    similarity: match.score
                }))
                .filter((it) => !!it.activity)
        );
    } catch (error) {
        log('ERROR', `Error ${error}`);
        return response.status(500).json('Something went wrong');
    }
});

export default router;
