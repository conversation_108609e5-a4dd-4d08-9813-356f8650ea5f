import { log } from '@quarterback/util/gcp';
import { Request, Response } from 'express';
import z, { ZodError } from 'zod';

const Interval = z.enum([
    '1min',
    '5min',
    '15min',
    '30min',
    '45min',
    '1h',
    '2h',
    '4h',
    '1day',
    '1week',
    '1month'
]);
type Interval = z.infer<typeof Interval>;

const QuerySchema = z.object({
    symbol: z.string(),
    exchange: z.string(),
    start: z.string().date(),
    end: z.string().date(),
    interval: Interval
});
type QuerySchema = z.infer<typeof QuerySchema>;

const TwelveDataQuery = QuerySchema.transform(
    ({ start, end, symbol, exchange, ...query }) => ({
        ...query,
        symbol: `${symbol}:${exchange}`,
        start_date: start,
        end_date: end
    })
);

export default async function timeSeries(request: Request, response: Response) {
    try {
        const params = new URLSearchParams({
            ...TwelveDataQuery.parse(request.query),
            apikey: '0175eda078884359b03ba1c47d5da744',
            order: 'ASC'
        });

        const quote = await fetch(
            `https://api.twelvedata.com/time_series?${params.toString()}`
        );

        if (quote.ok) {
            return response.status(200).json(await quote.json());
        } else {
            throw new Error();
        }
    } catch (error) {
        if (error instanceof ZodError) {
            log('INFO', `Bad request ${error}`, { body: request.body });
            return response.status(400).json('Bad request');
        } else {
            log('INFO', `Error: ${error}`);
            return response.status(400).json('Bad request');
        }
    }
}
