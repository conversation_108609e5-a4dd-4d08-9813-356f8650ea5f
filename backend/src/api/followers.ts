import { Router } from 'express';
import z from 'zod';

import { and, desc, eq, gt, lt } from 'drizzle-orm';

import db from '../db/index.js';
import { linkedInProfileSnapshot, twitterProfileSnapshot } from '../db/schema/socials.js';

const router: Router = Router();

const QuerySchema = z.object({
    symbol: z.string(),
    exchange: z.string(),
    to: z.coerce.date().optional(),
    from: z.coerce.date().optional()
});
type QuerySchema = z.infer<typeof QuerySchema>;

router.get('/', async (request, response) => {
    try {
        const { symbol, exchange, from, to } = QuerySchema.parse(request.query);

        const twitter = await db
            .select()
            .from(twitterProfileSnapshot)
            .orderBy(desc(twitterProfileSnapshot.at))
            .where(
                and(
                    eq(twitterProfileSnapshot.symbol, symbol),
                    eq(twitterProfileSnapshot.exchange, exchange),
                    from && gt(twitterProfileSnapshot.at, from),
                    to && lt(twitterProfileSnapshot.at, to)
                )
            );

        const linkedIn = await db
            .select()
            .from(linkedInProfileSnapshot)
            .orderBy(desc(linkedInProfileSnapshot.at))
            .where(
                and(
                    eq(linkedInProfileSnapshot.symbol, symbol),
                    eq(linkedInProfileSnapshot.exchange, exchange),
                    from && gt(linkedInProfileSnapshot.at, from),
                    to && lt(linkedInProfileSnapshot.at, to)
                )
            );

        return response.status(200).json({ twitter, linkedIn });
    } catch (error) {
        return response.status(500);
    }
});

export default router;
