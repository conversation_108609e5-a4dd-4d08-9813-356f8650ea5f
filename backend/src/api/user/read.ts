import { Router, Request, Response } from 'express';
import z from 'zod';
import { ActivityRead } from '@quarterback/types';
import * as user from '../../db/repositories/user.js';
import db from '../../db/index.js';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';

const router: Router = Router();

const BodySchema = z.array(ActivityRead);
router.post('/', async (request: AuthenticatedRequest, response: Response) => {
    const body = BodySchema.parse(request.body);

    return response.json(
        await user.activityRead(
            db,
            request.user!.sub!,
            body.map((it) => it.activity!)
        )
    );
});

const QuerySchema = z.object({
    id: z.string()
});

router.delete('/', async (request: AuthenticatedRequest, response: Response) => {
    const query = QuerySchema.parse(request.query);
    return response.json(await user.activityUnread(db, request.user!.sub!, [query.id]));
});

export default router;
