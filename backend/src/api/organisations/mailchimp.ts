import { Router, Request, Response } from 'express';
import z from 'zod';
import db from '../../db/index.js';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import { mailchimpConfiguration } from '../../db/schema/socials.js';
import { and, eq, sql } from 'drizzle-orm';
import verifyUserOrganisation from '../../auth/verifyUserOrganisation.js';
import { organisationUserEntity } from '../../db/schema/organisation.js';

const router: Router = Router({ mergeParams: true });

router.get('/', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, entity } = request.params;
    const [symbol, exchange] = entity.split(':');

    const results = await db
        .select()
        .from(mailchimpConfiguration)
        .innerJoin(
            organisationUserEntity,
            and(
                eq(mailchimpConfiguration.organisation, organisationUserEntity.organisation),
                eq(mailchimpConfiguration.symbol, organisationUserEntity.symbol),
                eq(mailchimpConfiguration.exchange, organisationUserEntity.exchange)
            )
        )
        .where(
            and(
                eq(mailchimpConfiguration.organisation, organisation),
                eq(mailchimpConfiguration.symbol, symbol),
                eq(mailchimpConfiguration.exchange, exchange),
                eq(organisationUserEntity.user, request.user!.sub!)
            )
        );

    return response.status(200).json(results.map((it) => it.MailchimpConfiguration));
});

const MailChimpConfiguration = z.object({
    token: z.string(),
    audience: z.string()
});

// token.split('-')[1]
// <>.api.mailchimp.com/3.0/
const BodySchema = z.array(MailChimpConfiguration);
router.post('/', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, entity } = request.params;
    const [symbol, exchange] = entity.split(':');

    const body = BodySchema.parse(request.body);

    if (await verifyUserOrganisation(request.user!.sub!, organisation, symbol, exchange)) {
        await db
            .insert(mailchimpConfiguration)
            .values(
                body.map((config) => ({
                    token: config.token,
                    audience: config.audience,

                    organisation,

                    symbol,
                    exchange
                }))
            )
            .onConflictDoUpdate({
                target: [
                    mailchimpConfiguration.organisation,
                    mailchimpConfiguration.symbol,
                    mailchimpConfiguration.exchange
                ],
                set: { token: sql`excluded.token`, audience: sql`excluded.audience` }
            });

        return response.json(true);
    } else {
        return response.status(404).json('Not found');
    }
});

export default router;
