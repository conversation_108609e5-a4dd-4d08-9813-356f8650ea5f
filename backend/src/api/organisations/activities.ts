import { Router, Response } from 'express';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import z from 'zod';
import * as activities from '../../db/repositories/activities.js';
import db from '../../db/index.js';

const router: Router = Router({ mergeParams: true });

router.get('/', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, entity } = request.params;
    const [symbol, exchange] = entity.split(':');

    const QuerySchema = z.object({
        to: z.coerce.date().optional(),
        from: z.coerce.date().optional(),
        read: z
            .enum(['true', 'false'])
            .transform((value) => value === 'true')
            .optional(),
        archived: z
            .enum(['true', 'false'])
            .transform((value) => value === 'true')
            .optional(),
        flagged: z
            .enum(['true', 'false'])
            .transform((value) => value === 'true')
            .optional(),
        thread: z.coerce.string().optional(),
        limit: z.coerce.number().optional(),
        source: z.enum(['asx']).optional()
    });

    try {
        return response
            .status(200)
            .json(
                await activities.read(
                    db,
                    request.user!.sub!,
                    organisation,
                    symbol,
                    exchange,
                    QuerySchema.parse(request.query)
                )
            );
    } catch (error) {
        return response.status(500).json('No success');
    }
});

router.get('/count', async (request: AuthenticatedRequest, response) => {
    const QuerySchema = z.object({
        read: z
            .enum(['true', 'false'])
            .transform((value) => value === 'true')
            .optional(),
        provenance: z.enum(['broadcast', 'chatter']).optional()
    });

    const { read, provenance } = QuerySchema.parse(request.query);

    const { organisation, entity } = request.params;
    const [symbol, exchange] = entity.split(':');

    return response.json(
        await activities.count(db, request.user!.sub!, symbol, exchange, {
            read,
            provenance
        })
    );
});

router.get('/:activity', async (request: AuthenticatedRequest, response) => {
    const { organisation, entity } = request.params;
    const [symbol, exchange] = entity.split(':');

    try {
        const result = await activities.read(
            db,
            request.user!.sub!,
            organisation,
            symbol,
            exchange,
            {
                id: request.params.activity
            }
        );

        if (result.length) {
            return response.status(200).json(result[0]);
        } else {
            return response.status(404).json('Not Found');
        }
    } catch (error) {
        return response.status(500);
    }
});

export default router;
