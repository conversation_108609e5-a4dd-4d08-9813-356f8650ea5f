import { Router, Request, Response } from 'express';
import z from 'zod';
import { ActivityArchived, ActivityFlagged } from '@quarterback/types';
import * as organisations from '../../db/repositories/organisations.js';
import db from '../../db/index.js';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import verifyUserActivities from '../../auth/verifyUserActivities.js';

const router: Router = Router({ mergeParams: true });

const BodySchema = z.array(ActivityArchived);
router.post('/', async (request: AuthenticatedRequest, response: Response) => {
    const body = BodySchema.parse(request.body);

    const user = request.user!.sub!;
    const { organisation } = request.params;

    if (
        await verifyUserActivities(
            user,
            organisation,
            body.map((it) => it.activity!)
        )
    ) {
        return response.json(
            await organisations.archiveActivity(
                db,
                request.params.organisation,
                body.map((it) => it.activity!)
            )
        );
    } else {
        return response.status(404).json('Not found');
    }
});

const QuerySchema = z.object({
    id: z.string()
});
router.delete('/', async (request: AuthenticatedRequest, response: Response) => {
    const query = QuerySchema.parse(request.query);

    return response.json(
        await organisations.unarchiveActivity(db, request.params.organisation, [query.id])
    );
});

export default router;
