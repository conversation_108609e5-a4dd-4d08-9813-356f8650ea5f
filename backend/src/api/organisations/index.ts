import { Router } from 'express';
import z from 'zod';

import db from '../../db/index.js';
import * as organisations from '../../db/repositories/organisations.js';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import flagged from './flagged.js';
import archived from './archived.js';
import activities from './activities.js';
import mailchimp from './mailchimp.js';
import alerts from '../alerts/index.js';

const router: Router = Router();

router.get('/', async (request: AuthenticatedRequest, response) => {
    try {
        return response
            .status(200)
            .json(await organisations.read(db, { users: [request.user!.sub] }));
    } catch (error) {
        return response.status(500);
    }
});

router.use('/:organisation/:entity/alerts', alerts);
router.use('/:organisation/:entity/activities', activities);
router.use('/:organisation/:entity/mailchimp', mailchimp);
router.use('/:organisation/flagged', flagged);
router.use('/:organisation/archived', archived);

export default router;
