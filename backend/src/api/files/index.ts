import { Router, Response } from 'express';
import { getSignedDownloadUrl, getSignedUploadUrl } from '../../services/storage.js';
import { ActivityFile, ActivityFileUploadRequest } from '@quarterback/types';
import { z } from 'zod';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import * as activityFilesRepository from '../../db/repositories/files.js';
import db from '../../db/index.js';
import { log } from 'console';

const router: Router = Router();

const DownloadRequestSchema = z.object({
    fileId: z.string().min(1)
});

router.post('/upload', async (request, response) => {
    try {
        const { fileName, fileType, fileSize } = ActivityFileUploadRequest.parse(
            request.body
        );

        log('INFO', 'Processing file upload request', {
            fileName,
            fileType,
            fileSize,
            bucketName: process.env.GCP_ACTIVITY_FILES_BUCKET || 'Not set'
        });

        // Generate a signed URL for uploading the file
        const signedUrl = await getSignedUploadUrl(fileName, fileType);

        return response.status(200).json({
            signedUrl,
            fileName,
            fileType,
            fileSize
        });
    } catch (error: any) {
        if (error instanceof z.ZodError) {
            return response.status(400).json({ error: 'Invalid request data' });
        }

        // Log detailed error information
        log('ERROR', 'Failed to generate signed upload URL in API endpoint', {
            name: error.name,
            message: error.message,
            stack: error.stack,
            details: error.details || 'No details available'
        });

        return response.status(500).json({
            error: 'Failed to generate signed upload URL',
            details: error.name === 'SigningError' ? error.details : undefined
        });
    }
});

router.get('/download', async (request: AuthenticatedRequest, response: Response) => {
    try {
        const { fileId } = DownloadRequestSchema.parse({
            fileId: request.query.fileId
        });

        // Get the file details from the database
        const file = await activityFilesRepository.getByStoragePath(db, fileId);

        if (!file) {
            return response.status(404).json({ error: 'File not found' });
        }

        // Generate a signed URL for downloading the file
        const signedUrl = await getSignedDownloadUrl(file.storagePath!);

        return response.status(200).json({
            ...file,
            signedUrl
        });
    } catch (error) {
        console.log(error);
        log('ERROR', 'Error generating signed download URL', { error });
        if (error instanceof z.ZodError) {
            return response.status(400).json({ error: 'Invalid request data' });
        }
        return response
            .status(500)
            .json({ error: 'Failed to generate signed download URL' });
    }
});

router.get('/', async (request: AuthenticatedRequest, response: Response) => {
    try {
        const { activity } = request.query;

        if (!activity || typeof activity !== 'string') {
            return response.status(400).json({ error: 'Activity ID is required' });
        }

        // Get all files for the activity
        const files = await activityFilesRepository.getByActivity(db, activity);

        return response.status(200).json(files);
    } catch (error) {
        log('ERROR', 'Error getting activity files', { error });
        return response.status(500).json({ error: 'Failed to get activity files' });
    }
});

router.delete('/:fileId', async (request: AuthenticatedRequest, response: Response) => {
    try {
        const { fileId } = DownloadRequestSchema.parse({
            fileId: request.params.fileId
        });

        // Delete the file from the database
        await activityFilesRepository.deleteByPath(db, fileId);

        return response.status(200).json({ success: true });
    } catch (error) {
        log('ERROR', 'Error deleting file', { error });
        if (error instanceof z.ZodError) {
            return response.status(400).json({ error: 'Invalid request data' });
        }
        return response.status(500).json({ error: 'Failed to delete file' });
    }
});

export default router;
