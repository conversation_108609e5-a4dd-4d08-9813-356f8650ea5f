import { Router, Response } from 'express';
import AuthenticatedRequest from '../../auth/AuthenticatedRequest.js';
import verifyUserOrganisation from '../../auth/verifyUserOrganisation.js';
import * as alerts from '../../db/repositories/alerts/alerts.js';
import db from '../../db/index.js';
import { Alert } from '@quarterback/types';
import z from 'zod';

const router: Router = Router({ mergeParams: true });

router.get('/', async (request: AuthenticatedRequest, response: Response) => {
    const { organisation, entity } = request.params;
    const [symbol, exchange] = entity.split(':');

    if (
        await verifyUserOrganisation(request.user!.sub!, organisation, symbol, exchange)
    ) {
        try {
            const alertJson = await alerts.getAlertConfigs(db, organisation, symbol);

            return response.status(200).json(alertJson);
        } catch (error) {
            console.error(error);
            return response.status(500).send('Internal server error');
        }
    } else {
        return response.status(404).send('Not Found');
    }
});

const AlertConfigBody = Alert.omit({
    organisation: true,
    symbol: true,
    exchange: true
}).array();

router.post('/', async (request: AuthenticatedRequest, response: Response) => {
    try {
        const { organisation, entity } = request.params;
        const [symbol, exchange] = entity.split(':');

        const body = AlertConfigBody.parse(request.body);

        if (
            await verifyUserOrganisation(
                request.user!.sub!,
                organisation,
                symbol,
                exchange
            )
        ) {
            try {
                await alerts.updateAlertConfigs(
                    db,
                    body.map((it) => ({
                        ...it,
                        organisation,
                        symbol,
                        exchange
                    }))
                );

                return response.status(200).json('Success');
            } catch (error) {
                console.error(error);
                return response.status(500).json('Internal server error');
            }
        } else {
            return response.status(404).json('Not Found');
        }
    } catch (error) {
        console.error(error);
        return response.status(500).json('Internal server error');
    }
});

const deleteQuerySchema = z.object({
    id: z.string()
});

router.delete('/', async (request: AuthenticatedRequest, response: Response) => {
    const { id } = deleteQuerySchema.parse(request.query);

    const { organisation, entity } = request.params;
    const [symbol, exchange] = entity.split(':');

    if (
        await verifyUserOrganisation(request.user!.sub!, organisation, symbol, exchange)
    ) {
        await alerts.deleteAlertConfigs(db, [{ id }]);
        return response.status(200).json('Success');
    }

    return response.status(404).json('Not Found');
});

export default router;
