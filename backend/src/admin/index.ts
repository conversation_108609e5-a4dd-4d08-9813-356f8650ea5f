import { Router } from 'express';

import entities from './entities.js';
import organisations from './organisations.js';
import newscatcher from './newscatcher.js';
import activities from './activities.js';

const router: Router = Router();

router.use('/entities', entities);
router.use('/organisations', organisations);
router.use('/newscatcher', newscatcher);
router.use('/activities', activities);

export default router;
