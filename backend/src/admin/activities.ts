import { Activity2 } from '@quarterback/types';
import { Request, Response, Router } from 'express';
import OpenAI from 'openai';
import db from '../db/index.js';
import { pinecone } from '../db/pinecone.js';
import * as activitiesRepository from '../db/repositories/activities/index.js';
import * as embeddingsRepository from '../db/repositories/embeddings/index.js';
import { sentimentAnalysis } from '../db/repositories/insights/prompts.js';
import * as sentimentRepository from '../db/repositories/sentiment/index.js';
import * as summariesRepository from '../db/repositories/summaries/index.js';
import { sendBroadSearchAlerts, sendSentimentAlerts } from '../schedulers/alerts.js';

const openAi = new OpenAI();

const router: Router = Router();

router.post('/', async (req: Request, res: Response) => {
    try {
        const activities = Activity2.array().parse(req.body);

        const { created } = await db.transaction(async (tx) => {
            return await activitiesRepository.upsert(tx, activities);
        });

        await embeddingsRepository.create(pinecone, openAi, created);

        const createdSentiments = await sentimentRepository.createWithOpenAI(
            db,
            created,
            sentimentAnalysis
        );

        await summariesRepository.create(openAi, db, created);

        await Promise.all([
            sendSentimentAlerts({ activities: created, sentiments: createdSentiments }),
            sendBroadSearchAlerts(activities)
        ]);

        return res.status(200).send(created);
    } catch (error) {
        console.error('Error creating activities:', error);
        return res.status(400).json('Something went wrong');
    }
});

export default router;
