import { Router, Request, Response } from 'express';
import { TZDate } from '@date-fns/tz/date';
import { format } from 'date-fns';
import { z } from 'zod';

const router: Router = Router();

const bodySchema = z.object({});

router.post('/', async (request: Request, response: Response) => {
    // const params = new URLSearchParams(request.body as {});
    // const apiResponse = await fetch(
    //     `https://v3-api.newscatcherapi.com/api/search?${params.toString()}`,
    //     {
    //         headers: { 'x-api-token': 'ZoPSvY3GDGdtkswYkDxYScEaJ2EPOeoS' }
    //     }
    // );
    //
    // return response.status(apiResponse.status).json(await apiResponse.json());
});

export default router;
