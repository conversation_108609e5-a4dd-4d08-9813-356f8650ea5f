import { Router } from 'express';
import z from 'zod';

import { Organisation } from '@quarterback/types';
import db from '../db/index.js';
import * as organisations from '../db/repositories/organisations.js';

const router: Router = Router();

router.get('/', async (request, response) => {
    try {
        return response.status(200).json(await organisations.read(db));
    } catch (error) {
        console.error(error);
        return response.status(500);
    }
});

router.post('/', async (request, response) => {
    try {
        await organisations.write(db, z.array(Organisation).min(1).parse(request.body));
        return response.status(200).json(await organisations.read(db));
    } catch (error) {
        console.error(error);
        return response.status(500);
    }
});

export default router;
