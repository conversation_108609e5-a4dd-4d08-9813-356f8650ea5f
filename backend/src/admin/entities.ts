import { Router } from 'express';
import z, { ZodError, symbol } from 'zod';

import { log } from '@quarterback/util/gcp';
import { ListedEntity } from '@quarterback/types';
import { sql } from 'drizzle-orm';
import db from '../db/index.js';
import { listedEntity } from '../db/schema/market.js';

const router: Router = Router();

router.get('/', async (request, response) => {
    return response.status(200).json(await db.select().from(listedEntity));
});

const BodySchema = z.array(ListedEntity);
type BodySchema = z.infer<typeof BodySchema>;

router.post('/', async (request, response) => {
    try {
        const entities = BodySchema.parse(request.body);

        const inserted = await db
            .insert(listedEntity)
            .values(
                entities.map((entity) => ({
                    name: entity.name,
                    twitterUsername: entity.twitterUsername,
                    twitterQuery: entity.twitterQuery,
                    newsQuery: entity.newsQuery,
                    redditQuery: entity.redditQuery,
                    linkedInQuery: entity.linkedInQuery,
                    linkedInUsername: entity.linkedInUsername,
                    symbol: entity.symbol,
                    exchange: entity.exchange,
                    about: entity.about
                }))
            )
            .onConflictDoUpdate({
                target: [listedEntity.symbol, listedEntity.exchange],
                set: {
                    name: sql`excluded."name"`,
                    twitterUsername: sql`excluded."twitterUsername"`,
                    twitterQuery: sql`excluded."twitterQuery"`,
                    newsQuery: sql`excluded."newsQuery"`,
                    redditQuery: sql`excluded."redditQuery"`,
                    linkedInQuery: sql`excluded."linkedInQuery"`,
                    linkedInUsername: sql`excluded."linkedInUsername"`,
                    about: sql`excluded."about"`
                }
            })
            .returning();

        return response.status(200).json(inserted);
    } catch (error) {
        if (error instanceof ZodError) {
            log('INFO', `Bad request ${error}`, { body: request.body });
            return response.status(400).json('Bad request');
        } else {
            log('INFO', `Error: ${error}`);
            return response.status(400).json('Bad request');
        }
    }
});

export default router;
