import run from '@google-cloud/run';
import { and, eq, isNotNull, max, gte } from 'drizzle-orm';
import { Router } from 'express';
import z, { ZodError } from 'zod';

import { log } from '@quarterback/util/gcp';

import { NewsCatcherSearchParams } from '@quarterback/types';
import { chunked } from '@quarterback/util';
import { format, sub } from 'date-fns';
import db from '../db/index.js';
import {
    activity,
    hotCopperPost,
    hotCopperThread,
    linkedInPost
} from '../db/schema/activities.js';
import { listedEntity } from '../db/schema/market.js';
import {
    mailchimpConfiguration,
    mailchimpSubscriberSnapshot
} from '../db/schema/socials.js';
import { GoogleCloudPlatformRequest } from '../gcloud/middleware.js';
import runActor from '../jobs/runActor.js';
import runJob from '../jobs/runJob.js';
import Actor from './Actor.js';
import Scraper from './Scraper.js';

export const MailchimpListSchema = z.object({
    stats: z.object({
        member_count: z.number()
    })
});
export type MailchimpListSchema = z.infer<typeof MailchimpListSchema>;

const ScrapersSchema = z.array(z.nativeEnum(Scraper));
type ScrapersSchema = z.infer<typeof ScrapersSchema>;

const client = new run.v2.JobsClient();

async function sleep(milliseconds: number): Promise<void> {
    return new Promise((resolve, reject) => {
        setTimeout(() => resolve(), milliseconds);
    });
}

const router: Router = Router();

router.post('/', async (request: GoogleCloudPlatformRequest, response) => {
    const webhookBase = request.metadata!.url;

    try {
        const scrapers = ScrapersSchema.parse(request.body);

        if (scrapers.includes(Scraper.asxAnnouncements)) {
            const entities = await db.select().from(listedEntity);

            for (const entity of entities) {
                log('INFO', `Scrapper : ${Scraper.asxAnnouncements} :: ${entity.symbol}`);
                await runJob(client, process.env['ASX_ANNOUNCEMENTS_SCRAPER']!, [
                    '--symbol',
                    entity.symbol,
                    '--exchange',
                    entity.exchange
                ]);

                await sleep(2000);
            }
        }

        if (scrapers.includes(Scraper.reddit)) {
            // Reddit's free API limits requests to 100 per minute. We want to minimise the number
            // of scrapers, making it easier to coordinate rate limits.
            // FIXME: this may not be necessary when using Reddit's enterprise API

            // With symbol and query args we can specify about 20 companies at once

            const exchangeEntities = Object.groupBy(
                await db.select().from(listedEntity),
                (it) => it.exchange
            );

            for (const [exchange, entities] of Object.entries(exchangeEntities)) {
                for (const chunk of chunked(entities ?? [], 20)) {
                    const withReddit = chunk.filter((it) => it.redditQuery);

                    if (withReddit.length) {
                        log('INFO', `Scrapper : ${Scraper.reddit}`);
                        await runJob(client, process.env['REDDIT_SCRAPER']!, [
                            ...withReddit.flatMap((entity) => [
                                '--symbol',
                                entity.symbol,
                                '--query',
                                entity.redditQuery!
                            ]),
                            '--exchange',
                            exchange
                        ]);

                        await sleep(2000);
                    }
                }
            }
        }

        if (scrapers.includes(Scraper.hotcopper)) {
            const entities = await db.select().from(listedEntity);

            const from = (
                await db
                    .select({
                        exchange: activity.exchange,
                        symbol: activity.symbol,
                        from: max(hotCopperPost.post)
                    })
                    .from(activity)
                    .innerJoin(hotCopperPost, eq(hotCopperPost.activity, activity.id))
                    .innerJoin(
                        hotCopperThread,
                        eq(hotCopperPost.thread, hotCopperThread.thread)
                    )
                    .groupBy(activity.symbol, activity.exchange)
            ).reduce(
                (map, entity): Record<string, Record<string, number>> => {
                    return {
                        ...map,
                        [entity.exchange]: {
                            ...(map[entity.exchange] ?? {}),
                            [entity.symbol]: entity.from ?? 0
                        }
                    };
                },
                {} as Record<string, Record<string, number>>
            );

            for (const entity of entities) {
                log('INFO', `Scrapper : ${Scraper.hotcopper} :: ${entity.symbol}`);
                await runJob(client, process.env['HOTCOPPER_SCRAPER']!, [
                    '--symbol',
                    entity.symbol,
                    '--exchange',
                    entity.exchange,
                    '--from',
                    (from[entity.exchange]?.[entity.symbol] ?? 0).toString()
                ]);

                await sleep(2000);
            }
        }

        if (scrapers.includes(Scraper.googleNews)) {
            const entities = await db.select().from(listedEntity);

            for (const entity of entities) {
                if (entity.newsQuery) {
                    log('INFO', `Scrapper : ${Scraper.googleNews} :: ${entity.symbol}`);
                    await runJob(client, process.env['GOOGLE_NEWS_SCRAPER']!, [
                        '--symbol',
                        entity.symbol,
                        '--exchange',
                        entity.exchange,
                        '--query',
                        entity.newsQuery
                    ]);

                    await sleep(2000);
                }
            }
        }

        if (scrapers.includes(Scraper.news)) {
            const entities = await db.select().from(listedEntity);

            for (const entity of entities) {
                if (entity.newsQuery) {
                    log('INFO', `Scrapper : ${Scraper.news} :: ${entity.symbol}`);

                    const params: NewsCatcherSearchParams = {
                        q: entity.newsQuery,
                        lang: 'en',
                        exclude_duplicates: false,
                        ranked_only: false,
                        sort_by: 'date',
                        page_size: 1000
                    };

                    await runJob(client, process.env['NEWS_SCRAPER']!, [
                        '--symbol',
                        entity.symbol,
                        '--exchange',
                        entity.exchange,
                        '--params',
                        Buffer.from(JSON.stringify(params)).toString('base64')
                    ]);

                    await sleep(2000);
                }
            }
        }

        if (scrapers.includes(Scraper.twitter)) {
            const entities = await db.select().from(listedEntity);

            const [until, since] = [
                format(new Date(), 'yyyy-MM-dd'),
                format(sub(new Date(), { days: 1 }), 'yyyy-MM-dd')
            ];

            for (const entity of entities) {
                if (entity.twitterQuery && webhookBase) {
                    log('INFO', `Scrapper : ${Scraper.twitter} :: ${entity.symbol}`);

                    await runActor(
                        Actor.twitter,
                        entity.symbol,
                        entity.exchange,
                        entity.twitterUsername,
                        entity.linkedInUsername,
                        webhookBase,
                        {
                            maxItems: 1000,
                            searchTerms: [
                                entity.twitterUsername
                                    ? `(from:${entity.twitterUsername}) until:${until} since:${since}`
                                    : undefined,
                                entity.twitterUsername
                                    ? `(@${entity.twitterUsername}) until:${until} since:${since}`
                                    : undefined,
                                entity.twitterQuery
                                    ? `${entity.twitterQuery} until:${until} since:${since}`
                                    : undefined
                            ].filter(Boolean)
                        }
                    );

                    await sleep(2000);
                }
            }
        }

        if (scrapers.includes(Scraper.twitterFollowers)) {
            const entities = await db.select().from(listedEntity);

            for (const entity of entities) {
                if (entity.twitterUsername && webhookBase) {
                    log(
                        'INFO',
                        `Scrapper : ${Scraper.twitterFollowers} :: ${entity.symbol}`
                    );
                    await runActor(
                        Actor.twitterFollowers,
                        entity.symbol,
                        entity.exchange,
                        entity.twitterUsername,
                        entity.linkedInUsername,
                        webhookBase,
                        {
                            addNotFoundUsersToOutput: false,
                            addUserInfo: false,
                            customMapFunction: '(object) => { return {...object} }',
                            maxTweetsPerUser: 100,
                            onlyUserInfo: true,
                            proxy: {
                                useApifyProxy: true
                            },
                            startUrls: [`https://twitter.com/${entity.twitterUsername}`]
                        }
                    );

                    await sleep(2000);
                }
            }
        }

        if (scrapers.includes(Scraper.linkedinFollowers)) {
            const entities = await db.select().from(listedEntity);

            for (const entity of entities) {
                log(
                    'INFO',
                    `Scrapper : ${Scraper.linkedinFollowers} :: ${entity.symbol}`
                );
                if (entity.linkedInUsername && webhookBase) {
                    await runActor(
                        Actor.linkedinFollowers,
                        entity.symbol,
                        entity.exchange,
                        entity.twitterUsername,
                        entity.linkedInUsername,
                        webhookBase,
                        {
                            action: 'get-companies',
                            isName: false,
                            isUrl: true,
                            proxy: {
                                useApifyProxy: true
                            },
                            limit: 1,
                            keywords: [
                                `https://www.linkedin.com/company/${entity.linkedInUsername}/`
                            ]
                        }
                    );

                    await sleep(2000);
                }
            }
        }

        if (scrapers.includes(Scraper.linkedin)) {
            const entities = await db.select().from(listedEntity);
            for (const entity of entities) {
                try {
                    if (
                        entity.linkedInQuery?.length &&
                        entity.linkedInUsername?.length &&
                        webhookBase
                    ) {
                        log('INFO', `Scrapper : ${Scraper.linkedin} :: ${entity.symbol}`);
                        await Promise.all([
                            runActor(
                                Actor.linkedin,
                                entity.symbol,
                                entity.exchange,
                                entity.twitterUsername,
                                entity.linkedInUsername,
                                webhookBase,
                                {
                                    body: {
                                        query: entity.linkedInQuery,
                                        page: 1,
                                        sort_by: 'date_posted'
                                    },
                                    endpoint: 'search-posts'
                                }
                            ),
                            runActor(
                                Actor.linkedin,
                                entity.symbol,
                                entity.exchange,
                                entity.twitterUsername,
                                entity.linkedInUsername,
                                webhookBase,
                                {
                                    body: {
                                        query: ' ',
                                        from_organization: entity.linkedInCompanyId,
                                        page: 1,
                                        sort_by: 'date_posted'
                                    },
                                    endpoint: 'search-posts'
                                }
                            ),
                            runActor(
                                Actor.linkedin,
                                entity.symbol,
                                entity.exchange,
                                entity.twitterUsername,
                                entity.linkedInUsername,
                                webhookBase,
                                {
                                    body: {
                                        query: ' ',
                                        mentions_organization: entity.linkedInCompanyId,
                                        page: 1,
                                        sort_by: 'date_posted'
                                    },
                                    endpoint: 'search-posts'
                                }
                            )
                        ]);

                        await sleep(2000);
                    }
                } catch (error) {
                    log(
                        'ERROR',
                        `Error fetching linkedin data: ${error} \n ${entity.symbol}:${entity.exchange}`
                    );
                }
            }
        }

        if (scrapers.includes(Scraper.linkedinComments)) {
            const entities = await db.select().from(listedEntity);

            for (const entity of entities) {
                const chunkedCommentUrns: string[][] = chunked(
                    await db
                        .select()
                        .from(linkedInPost)
                        .innerJoin(activity, eq(activity.id, linkedInPost.activity))
                        .where(
                            and(
                                isNotNull(linkedInPost.commentUrn),
                                eq(activity.symbol, entity.symbol),
                                gte(activity.posted, sub(new Date(), { days: 2 }))
                            )
                        )
                        .then((it) => it.map((it) => it.LinkedInPost.commentUrn!)),
                    25
                );

                try {
                    if (webhookBase && chunkedCommentUrns.length) {
                        log(
                            'INFO',
                            `Scrapper : ${Scraper.linkedinComments} :: ${entity.symbol}`
                        );
                        for (const chunk of chunkedCommentUrns) {
                            await Promise.all(
                                chunk.map((commentsUrn) => {
                                    return runActor(
                                        Actor.linkedinComments,
                                        entity.symbol,
                                        entity.exchange,
                                        entity.twitterUsername,
                                        entity.linkedInUsername,
                                        webhookBase,
                                        {
                                            body: {
                                                commentsUrn,
                                                page: 1
                                            },
                                            endpoint: 'post-comments'
                                        }
                                    );
                                })
                            );
                            await sleep(180000);
                        }
                    }
                } catch (error) {
                    log(
                        'ERROR',
                        `Error fetching linkedin data: ${error} \n ${entity.symbol}:${entity.exchange}`
                    );
                }
                await sleep(180000);
            }
        }

        if (scrapers.includes(Scraper.mailchimp)) {
            const lists = await db.select().from(mailchimpConfiguration);

            for (const list of lists) {
                log(
                    'INFO',
                    `Scrapper : ${Scraper.mailchimp} :: ${list.symbol} ${list.token}`
                );
                try {
                    const token = list.token;
                    const region = token.split('-')[1];
                    const audience = list.audience;

                    const response = await fetch(
                        `https://${region}.api.mailchimp.com/3.0/lists/${audience}`,
                        {
                            headers: {
                                Authorization: `apikey ${token}`
                            }
                        }
                    )
                        .then((it) => {
                            if (!it.ok)
                                throw new Error(
                                    `Mailchimp request failed with response code ${it.status}`
                                );
                            return it;
                        })
                        .then((it) => it.json());

                    await db.insert(mailchimpSubscriberSnapshot).values([
                        {
                            audience: list.audience,
                            at: new Date(),
                            subscribers:
                                MailchimpListSchema.parse(response).stats.member_count,
                            organisation: list.organisation,
                            symbol: list.symbol,
                            exchange: list.exchange
                        }
                    ]);
                } catch (error) {
                    log('ERROR', `Error fetching mailchimp list data: ${error}`);
                }
            }
        }

        if (scrapers.includes(Scraper.redditApify)) {
            const entities = await db.select().from(listedEntity);
            for (const entity of entities) {
                try {
                    if (entity.redditQuery?.length && webhookBase) {
                        log(
                            'INFO',
                            `Scrapper : ${Scraper.redditApify} :: ${entity.symbol}`
                        );
                        await runActor(
                            Actor.reddit,
                            entity.symbol,
                            entity.exchange,
                            entity.twitterUsername,
                            entity.linkedInUsername,
                            webhookBase,
                            {
                                debugMode: false,
                                includeNSFW: false,
                                maxComments: 10,
                                maxCommunitiesCount: 0,
                                maxItems: 100,
                                maxPostCount: 50,
                                maxUserCount: 0,
                                proxy: {
                                    useApifyProxy: true,
                                    apifyProxyGroups: []
                                },
                                scrollTimeout: 40,
                                searchComments: true,
                                searchCommunities: false,
                                searchPosts: true,
                                searchUsers: false,
                                searches: [entity.redditQuery],
                                skipComments: false,
                                skipCommunity: false,
                                skipUserPosts: false,
                                sort: 'new',
                                time: 'day'
                            }
                        );

                        await sleep(2000);
                    }
                } catch (error) {
                    log(
                        'ERROR',
                        `Error fetching reddit data: ${error} \n ${entity.symbol}:${entity.exchange}`
                    );
                }
            }
        }

        return response.status(200).json('Success');
    } catch (error) {
        if (error instanceof ZodError) {
            log('INFO', `Bad request: ${error}`, { body: request.body });
            return response.status(400).json('Bad request');
        } else {
            log('INFO', `Error: ${error}`);
        }
    }
});

export default router;
