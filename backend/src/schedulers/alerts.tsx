import { Router } from 'express';
import { log } from '@quarterback/util/gcp';

import db from '../db/index.js';

import { GoogleCloudPlatformRequest } from '../gcloud/middleware.js';

import * as alerts from '../db/repositories/alerts/alerts.js';
import * as market from '../db/repositories/market/index.js';

import { Resend } from 'resend';
import { ActivityAlertInterval, AlertConfig } from '@quarterback/types';
import { format, startOfDay, sub } from 'date-fns';
import { alertNotification } from '../db/schema/alerts.js';
import PriceAlert from '../emails/alerts/PriceAlert.js';
import { TZDate } from '@date-fns/tz/date';
import { Activity2 } from '@quarterback/types';
import SentimentAlert from '../emails/alerts/SentimentAlert.js';
import { sentimentScore } from '../db/export.js';
import { activity } from '../db/schema/activities.js';
import { sql } from 'drizzle-orm';
import ActivityAlert from '../emails/alerts/ActivityAlert.js';
import OpenAI from 'openai';
import { pinecone } from '../db/pinecone.js';
import BroadSearchAlert from '../emails/alerts/BroadSearchAlert.js';
const router: Router = Router();

const resend = new Resend(process.env['RESEND_KEY'] ?? '');

const sendPriceAlertEmail = async (alert: AlertConfig, change: number | undefined) => {
    const { emails } = alert;

    try {
        const { data, error } = await resend.emails.send({
            from: 'Quarterback Alerts <<EMAIL>>',
            to: emails,
            subject: 'Price Alert',
            react: (
                <PriceAlert
                    change={change ?? 0}
                    symbol={alert.symbol}
                    exchange={alert.exchange}
                />
            )
        });

        if (error) throw error;

        return data;
    } catch (error) {
        log('ERROR', `Error sending email via resend ${error}`, { alert });
        return null;
    }
};

const sendSentimentAlertEmail = async (alert: AlertConfig, activities: Activity2[]) => {
    const { emails } = alert;
    const rule = `${alert.comparator === 'LTE' ? 'less than' : 'more than'} ${alert.threshold}`;

    try {
        const { data, error } = await resend.emails.send({
            from: 'Quarterback Alerts <<EMAIL>>',
            to: emails,
            subject: 'Sentiment Alert',
            react: <SentimentAlert rule={rule} activities={activities} />
        });

        if (error) throw error;

        return data;
    } catch (error) {
        log('ERROR', `Error sending email via resend ${error}`, { alert });
        return null;
    }
};

const sendActivityAlertEmail = async (alert: AlertConfig) => {
    const { emails } = alert;

    try {
        const { data, error } = await resend.emails.send({
            from: 'Quarterback Alerts <<EMAIL>>',
            to: emails,
            subject: 'Activity Alert',
            react: (
                <ActivityAlert
                    threshold={alert.threshold}
                    interval={alert.interval!}
                    symbool={alert.symbol}
                />
            )
        });

        if (error) throw error;

        log(
            'INFO',
            `Activity alert sent to ${JSON.stringify(emails)} for rule ${alert.id}`
        );

        return data;
    } catch (error) {
        log('ERROR', `Error sending email via resend ${error}`, { alert });
        return null;
    }
};

const sendBroadSearchEmail = async (alert: AlertConfig, activities: Activity2[]) => {
    const { emails } = alert;

    try {
        const { data, error } = await resend.emails.send({
            from: 'Quarterback Alerts <<EMAIL>>',
            to: emails,
            subject: 'Broad Search Alert',
            react: <BroadSearchAlert rule={alert} activities={activities} />
        });

        if (error) throw error;

        return data;
    } catch (error) {
        log('ERROR', `Error sending email via resend ${error}`, { alert });
        return null;
    }
};

async function sendPriceAlerts() {
    // A 12 hour debounce avoids duplicate notifications
    const cutoffDate = sub(new Date(), { hours: 12 });

    const rules = await alerts.rules.fetch(db, {
        type: 'SHARE_PERCENT',
        history: { notExists: { from: cutoffDate } }
    });
    const quotes = await market.quotes.fetch(rules);

    for (const rule of rules) {
        const quote = quotes[`${rule.symbol}:${rule.exchange}`];

        if (quote) {
            const change = quote.percent_change;
            const open = quote.is_market_open;
            const today =
                quote.datetime === format(TZDate.tz('Australia/Sydney'), 'yyyy-MM-dd');

            if (today && open && Math.abs(change) >= rule.threshold) {
                log('INFO', 'Sending price alert email', {
                    quote,
                    change,
                    rule,
                    original: quotes[`${rule.symbol}:${rule.exchange}`]
                });

                await sendPriceAlertEmail(rule, change);
                await db
                    .insert(alertNotification)
                    .values([{ alert: rule.id!, at: new Date() }]);
            }
        } else {
            throw new Error(`Missing quote for entity ${rule.symbol}`);
        }
    }
}

export async function sendSentimentAlerts({
    activities,
    sentiments
}: {
    activities: Activity2[];
    sentiments: { activity: string; magnitude: number; score: number }[];
}) {
    if (sentiments.length === 0) {
        return true;
    }

    const rules = await alerts.rules.fetch(db, {
        type: 'SENTIMENT',
        history: { notExists: {} }
    });

    const filter = (
        sentiment: { activity: string; magnitude: number; score: number },
        comparator: 'GTE' | 'LTE',
        threshold: number
    ) => {
        if (comparator === 'GTE') {
            return sentiment.score > threshold;
        } else {
            return sentiment.score < threshold;
        }
    };

    const ruleWithActivities = rules.reduce(
        (acc, rule) => {
            const filteredSentiments = sentiments
                .map((sentiment) => {
                    return {
                        ...sentiment,
                        score: sentimentScore(sentiment)
                    };
                })
                .filter((sentiment) =>
                    filter(sentiment, rule.comparator, rule.threshold)
                );

            const filteredActivities = activities
                .filter((activity) =>
                    filteredSentiments.some(
                        (sentiment) =>
                            activity.symbol === rule.symbol &&
                            sentiment.activity === activity.id
                    )
                )
                .map((activity) => {
                    return {
                        ...activity,
                        sentiment: filteredSentiments.find(
                            (sentiment) => sentiment.activity === activity.id
                        )
                    };
                });

            acc.push({
                rule,
                activities: filteredActivities
            });

            return acc;
        },
        [] as { rule: AlertConfig; activities: Activity2[] }[]
    );

    for (const rule of ruleWithActivities) {
        if (rule.activities.length > 0) {
            log('INFO', 'Sending sentiment alert email');

            await sendSentimentAlertEmail(rule.rule, rule.activities);

            await db
                .insert(alertNotification)
                .values([{ alert: rule.rule.id!, at: new Date() }]);
        }
    }

    return true;
}

export async function sendActivityAlerts() {
    async function isActivityIncreasedByPercentage(
        interval: ActivityAlertInterval,
        threshold: number,
        symbol: string
    ): Promise<boolean> {
        await db.execute(sql`SET TIME ZONE 'Australia/Sydney'`);

        const result: {
            percentageincrease: number;
            countthisperiod: number;
            countlastperiod: number;
        }[] = await db.execute(sql`
        WITH currentPeriod AS (
        SELECT COUNT(*) AS countThisPeriod
        FROM ${activity}
    WHERE posted >= DATE_TRUNC('${sql.raw(interval!)}', NOW())
      AND posted <= NOW()
        AND symbol = ${symbol}
    ),
    previousPeriod AS (
        SELECT COUNT(*) AS countLastPeriod
        FROM ${activity}
    WHERE posted >= DATE_TRUNC('${sql.raw(interval!)}', NOW() - INTERVAL '1 ${sql.raw(interval!)}')
      AND posted < DATE_TRUNC('${sql.raw(interval!)}', NOW())
          AND symbol = ${symbol}
    )
    SELECT
        countThisPeriod,
        countLastPeriod,
    CASE 
        WHEN countLastPeriod = 0 THEN 
            CASE 
                WHEN countThisPeriod > 0 THEN 100.0  
                ELSE 0.0
            END
        ELSE 
            ((countThisPeriod - countLastPeriod) * 100.0 / countLastPeriod)
    END AS percentageIncrease
FROM currentPeriod, previousPeriod; 
`);
        log('INFO', `Activity alert result`, { ...result[0], symbol });
        return (result[0]?.percentageincrease >= threshold) as boolean;
    }

    const rules = await alerts.rules.fetch(db, {
        type: 'ACTIVITY',
        history: {
            notExists: {
                from: startOfDay(new Date(TZDate.tz('Australia/Sydney')))
            }
        }
    });

    const results = (
        await Promise.all(
            rules.map(async (rule) => {
                return {
                    rule,
                    increasedByPercentage: await isActivityIncreasedByPercentage(
                        rule.interval,
                        rule.threshold,
                        rule.symbol
                    )
                };
            })
        )
    ).filter((it) => it.increasedByPercentage === true);

    for (const rule of results) {
        log(
            'INFO',
            `Sending activity alert email for rule ${rule.rule.id} ${JSON.stringify(rule?.rule?.emails)}`
        );

        await sendActivityAlertEmail(rule.rule);

        await db
            .insert(alertNotification)
            .values([{ alert: rule.rule.id!, at: new Date() }]);
    }

    return true;
}

export async function sendBroadSearchAlerts(activities: Activity2[]) {
    const openAi = new OpenAI();
    const index = pinecone.index('activities');
    const upperBoundScore: Map<number, number> = new Map([
        [0.48, 1],
        [0.41, 0.48],
        [0.3, 0.41]
    ]);

    const rules = await alerts.rules.fetch(db, {
        type: 'SEARCH',
        history: { notExists: {} }
    });

    await Promise.all(
        rules.map(async (rule) => {
            const embedding = await openAi.embeddings
                .create({
                    input: rule.searchTerm!,
                    model: 'text-embedding-3-small'
                })
                .then((it) => it.data[0].embedding);

            const matches = await index
                .query({
                    vector: embedding,
                    topK: 50,
                    includeMetadata: true,
                    filter: {
                        symbol: { $eq: rule.symbol },
                        exchange: { $eq: rule.exchange }
                    }
                })
                .then((it) =>
                    it.matches.filter(
                        (it) =>
                            it.score! >= rule.threshold &&
                            it.score! < upperBoundScore.get(rule.threshold)!
                    )
                );

            if (matches.length) {
                const identifiedActivities = activities.filter((it) =>
                    matches.some((match) => match.id === it.id)
                );

                if (identifiedActivities.length) {
                    log('INFO', 'Sending broad search alert email');
                    await sendBroadSearchEmail(rule, identifiedActivities);
                    await db
                        .insert(alertNotification)
                        .values([{ alert: rule.id!, at: new Date() }]);
                }
            }
            return true;
        })
    );

    return true;
}

router.post('/', async (request: GoogleCloudPlatformRequest, response) => {
    try {
        await sendPriceAlerts();

        return response.status(200).json('Success');
    } catch (error) {
        log('ERROR', `Error: ${error}`);
        return response.status(500).json('Internal server error');
    }
});

router.post('/activity', async (request: GoogleCloudPlatformRequest, response) => {
    try {
        await sendActivityAlerts();

        return response.status(200).json('Success');
    } catch (error) {
        log('ERROR', `Error: ${error}`);
        return response.status(500).json('Internal server error');
    }
});

export default router;
