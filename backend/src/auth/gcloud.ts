import { Request, Response, NextFunction, RequestHandler } from 'express';
import { OAuth2Client } from 'google-auth-library';
import { GoogleCloudPlatformRequest } from '../gcloud/middleware.js';

const client = new OAuth2Client();

export default function authenticate(serviceAccountEmail: string) {
    return async (
        request: GoogleCloudPlatformRequest,
        response: Response,
        next: NextFunction
    ) => {
        try {
            const header = request.headers['authorization'];

            if (!header || !header.startsWith('Bearer ')) {
                throw new Error();
            }

            const ticket = await client.verifyIdToken({
                idToken: header.split(' ')[1],
                audience: `${request.metadata!.url}${request.baseUrl + request.path}`
            });

            const payload = ticket.getPayload();

            if (
                !payload ||
                payload.iss !== 'https://accounts.google.com' ||
                payload.email !== serviceAccountEmail
            ) {
                throw new Error();
            }

            return next();
        } catch (error) {
            return response.status(401).send('Unauthorized');
        }
    };
}
