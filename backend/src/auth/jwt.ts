import { NextFunction, Request, Response } from 'express';
import JsonWebToken, { Jwt<PERSON><PERSON><PERSON>, SigningKeyCallback } from 'jsonwebtoken';
import JwksClient from 'jwks-rsa';
import AuthenticatedRequest from './AuthenticatedRequest.js';

const client = JwksClient({
    jwksUri: process.env['JWKS_URI']!
});

function getKey(header: JwtHeader, callback: SigningKeyCallback) {
    client.getSigningKey(header.kid, (err, key) => {
        callback(err, key!.getPublicKey());
    });
}

async function verify(jwt: string): Promise<JsonWebToken.JwtPayload> {
    return new Promise((resolve, reject) => {
        JsonWebToken.verify(jwt, getKey, {}, (err, decoded) => {
            if (err || !decoded) {
                reject(err);
            } else {
                resolve(decoded as JsonWebToken.JwtPayload);
            }
        });
    });
}

export default function authenticate(permissions: Array<string> = []) {
    return async (request: AuthenticatedRequest, response: Response, next: NextFunction) => {
        try {
            const header = request.headers['authorization'];

            if (!header || !header.startsWith('Bearer ')) {
                throw new Error();
            }

            const verification = await verify(header.split(' ')[1]);

            for (const permission of permissions) {
                if (!((verification['st-perm']?.v as string[]) ?? []).includes(permission)) {
                    throw new Error();
                }
            }

            request.user = {
                sub: verification.sub!
            };

            return next();
        } catch {
            return response.status(401).send('Unauthorized');
        }
    };
}
