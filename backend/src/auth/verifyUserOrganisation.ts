import db from '../db/index.js';
import { organisationUserEntity } from '../db/schema/organisation.js';
import { and, eq, inArray } from 'drizzle-orm';
import { activity } from '../db/schema/activities.js';

export default async function verifyUserOrganisation(
    user: string,
    organisation: string,
    symbol: string,
    exchange: string
) {
    const result = await db
        .select()
        .from(organisationUserEntity)
        .where(
            and(
                eq(organisationUserEntity.organisation, organisation),
                eq(organisationUserEntity.symbol, symbol),
                eq(organisationUserEntity.exchange, exchange),
                eq(organisationUserEntity.user, user)
            )
        );

    return result.length > 0;
}
