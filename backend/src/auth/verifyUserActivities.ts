// Verify a user has access to modify or post data about an activity
import db from '../db/index.js';
import { activity } from '../db/schema/activities.js';
import { organisationUserEntity } from '../db/schema/organisation.js';
import { and, eq, inArray } from 'drizzle-orm';

export default async function verifyUserActivities(
    user: string,
    organisation: string,
    activities: string[]
) {
    const result = (
        await db
            .select()
            .from(activity)
            .innerJoin(
                organisationUserEntity,
                and(
                    eq(activity.symbol, organisationUserEntity.symbol),
                    eq(activity.exchange, organisationUserEntity.exchange)
                )
            )
            .where(
                and(
                    inArray(activity.id, activities),
                    eq(organisationUserEntity.user, user),
                    eq(organisationUserEntity.organisation, organisation)
                )
            )
    ).map((it) => it.Activity.id);

    return activities.every((activity) => result.includes(activity));
}
