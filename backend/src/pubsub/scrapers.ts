import { Router } from 'express';

import { toMessagePublishedData } from '@google/events/cloud/pubsub/v1/MessagePublishedData.js';

import { log } from '@quarterback/util/gcp';
import { base64 } from '@quarterback/util';
import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';

import db from '../db/index.js';

import * as summariesRepository from '../db/repositories/summaries/index.js';
import * as embeddingsRepository from '../db/repositories/embeddings/index.js';
import * as sentimentRepository from '../db/repositories/sentiment/index.js';
import * as activitiesRepository from '../db/repositories/activities/index.js';
import * as twitterProfileRepository from '../db/repositories/twitter-profile/index.js';
import * as linkedInProfileRepository from '../db/repositories/linkedIn-profile/index.js';

import { pinecone } from '../db/pinecone.js';
import * as language from '@google-cloud/language';
import OpenAI from 'openai';
import { sendBroadSearchAlerts, sendSentimentAlerts } from '../schedulers/alerts.js';
import { sentimentAnalysis } from '../db/repositories/insights/prompts.js';

const languageService = new language.LanguageServiceClient();
const openAi = new OpenAI();

const router: Router = Router();

router.post('/', async (request, result) => {
    if (!request.body && !request.body.message) {
        return result.status(200).json('Success');
    }

    try {
        log('INFO', 'Processing request from pubsub.');

        const body: PubSubMessage2 = PubSubMessage2.parse(
            JSON.parse(base64.decode(toMessagePublishedData(request.body).message!.data!))
        );

        switch (body.type) {
            case 'activities':
                const { created } = await db.transaction(async (tx) => {
                    return await activitiesRepository.upsert(tx, body.activities);
                });

                await embeddingsRepository.create(pinecone, openAi, created);
                const createdSentiments = await sentimentRepository.createWithOpenAI(
                    db,
                    created,
                    sentimentAnalysis
                );

                await summariesRepository.create(openAi, db, created);

                await Promise.all([
                    sendSentimentAlerts({
                        activities: created,
                        sentiments: createdSentiments
                    }),
                    sendBroadSearchAlerts(created)
                ]);

                break;

            case 'twitter-profiles':
                await db.transaction(async (tx) => {
                    await twitterProfileRepository.upsert(tx, body.profiles);
                });

                break;

            case 'linkedIn-profiles':
                await db.transaction(async (tx) => {
                    await linkedInProfileRepository.upsert(tx, body.profiles);
                });

                break;
        }
    } catch (error) {
        log('ERROR', `Error inserting scraper results: ${error}`, {
            pubsubPayload: JSON.parse(
                base64.decode(toMessagePublishedData(request.body).message!.data!)
            )
        });
    }

    // Return success in all cases for now.
    // The scrapers are still error-prone and
    // haven't had all of their issues ironed out.
    return result.status(200).json('Success');
});

export default router;
