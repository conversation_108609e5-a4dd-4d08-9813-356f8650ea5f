import { Activity2, AlertConfig, searchTermRelevanceText } from '@quarterback/types';
import {
    Body,
    Button,
    Container,
    Head,
    Hr,
    Html,
    Img,
    Preview,
    Section,
    Text
} from '@react-email/components';
import ActivityItem from './ActivityItem.js';

interface Props {
    rule: AlertConfig;
    activities: Activity2[];
}

export default function BroadSearchAlert({ rule, activities }: Props) {
    return (
        <Html>
            <Head />
            <Preview>New activity detected within your sentiment alert range</Preview>
            <Body style={main}>
                <Container style={container}>
                    <Img
                        src="https://storage.googleapis.com/quarterback-alerts/logo.png"
                        height="50"
                        alt="Quarterback"
                        style={logo}
                    />

                    <Text style={subheading}>
                        We've detected new activities relevant to {rule?.searchTerm}
                    </Text>
                    <Text style={paragraph}>
                        Following activities are{' '}
                        {searchTermRelevanceText.get(rule.threshold)} relevant to{' '}
                        {rule.searchTerm}{' '}
                    </Text>
                    {activities?.length > 0
                        ? activities?.map((activity) => (
                              <ActivityItem activity={activity} key={activity.id} />
                          ))
                        : null}
                    <Text style={paragraph}>
                        Sign in to Quarterback for more information.
                    </Text>
                    <Section style={btnContainer}>
                        <Button style={button} href="https://app.qback.au">
                            Open Quarterback
                        </Button>
                    </Section>
                    <Text style={paragraph}>
                        Best,
                        <br />
                        The Quarterback team
                    </Text>
                    <Hr style={hr} />
                    <Text style={footer}>
                        Level 1, 191 St Georges Terrace, Perth, Western Australia 6000
                    </Text>
                </Container>
            </Body>
        </Html>
    );
}

const main = {
    backgroundColor: '#ffffff',
    fontFamily:
        '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif'
};

const container = {
    margin: '0 auto',
    padding: '20px 0 48px'
};

const logo = {
    margin: '0 auto'
};

const subheading = {
    color: '#111827',
    fontSize: '18px',
    lineHeight: '26px'
};

const paragraph = {
    color: '#4b5563',
    fontSize: '16px',
    lineHeight: '24px'
};

const btnContainer = {
    textAlign: 'center' as const
};

const button = {
    backgroundColor: '#4338ca',
    borderRadius: '3px',
    color: '#fff',
    fontSize: '16px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'block',
    padding: '12px'
};

const hr = {
    borderColor: '#cccccc',
    margin: '20px 0'
};

const footer = {
    color: '#8898aa',
    fontSize: '12px'
};
