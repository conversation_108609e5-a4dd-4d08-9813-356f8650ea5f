import {
    Body,
    <PERSON><PERSON>,
    Container,
    Head,
    Hr,
    Html,
    Img,
    Preview,
    Section,
    Text
} from '@react-email/components';

interface Props {
    threshold: number;
    symbool: string;
    interval: string;
}

export default function ActivityAlert({ threshold, symbool, interval }: Props) {
    return (
        <Html>
            <Head />
            <Preview>New activity detected</Preview>
            <Body style={main}>
                <Container style={container}>
                    <Img
                        src="https://storage.googleapis.com/quarterback-alerts/logo.png"
                        height="50"
                        alt="Quarterback"
                        style={logo}
                    />

                    <Text style={subheading}>
                        We've detected an increase in your activity volumes for {symbool}
                    </Text>
                    <Text style={paragraph}>
                        We've detected an increase in your activity volumes by {threshold}
                        % compared with the previous {interval.toLocaleLowerCase()}
                    </Text>

                    <Text style={paragraph}>
                        Sign in to Quarterback for more information.
                    </Text>
                    <Section style={btnContainer}>
                        <Button style={button} href="https://app.qback.au">
                            View Activities
                        </Button>
                    </Section>
                    <Text style={paragraph}>
                        Best,
                        <br />
                        The Quarterback team
                    </Text>
                    <Hr style={hr} />
                    <Text style={footer}>
                        Level 1, 191 St Georges Terrace, Perth, Western Australia 6000
                    </Text>
                </Container>
            </Body>
        </Html>
    );
}

const main = {
    backgroundColor: '#ffffff',
    fontFamily:
        '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif'
};

const container = {
    margin: '0 auto',
    padding: '20px 0 48px'
};

const logo = {
    margin: '0 auto'
};

const subheading = {
    color: '#111827',
    fontSize: '18px',
    lineHeight: '26px'
};

const paragraph = {
    color: '#4b5563',
    fontSize: '16px',
    lineHeight: '24px'
};

const btnContainer = {
    textAlign: 'center' as const
};

const button = {
    backgroundColor: '#4338ca',
    borderRadius: '3px',
    color: '#fff',
    fontSize: '16px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'block',
    padding: '12px'
};

const hr = {
    borderColor: '#cccccc',
    margin: '20px 0'
};

const footer = {
    color: '#8898aa',
    fontSize: '12px'
};
