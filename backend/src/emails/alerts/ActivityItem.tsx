import { Activity2 } from '@quarterback/types';

const format = (date: Date | undefined) => {
    if (!date) return '';

    return new Intl.DateTimeFormat('en-AU', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        timeZoneName: 'short'
    })
        .format(new Date(date))
        .replace(/, /, ' ');
};

function ActivityAvatar({ activity }: { activity: Activity2 }) {
    if (activity?.hotcopper) {
        return (
            <img
                src="https://hotcopper.com.au/images/default-avatar-l.png"
                style={imageStyle}
                className="avatar-image"
            />
        );
    }

    if (activity?.asx) {
        return (
            <img
                src={'https://storage.googleapis.com/quarterback-alerts/png/asx.png'}
                style={imageStyle}
                className="avatar-image"
            />
        );
    }

    if (activity?.news) {
        return (
            <img
                src={'https://storage.googleapis.com/quarterback-alerts/png/news.png'}
                style={imageStyle}
                className="avatar-image"
            />
        );
    }

    if (activity?.tweet) {
        return (
            <img
                src={'https://storage.googleapis.com/quarterback-alerts/png/x.png'}
                style={imageStyle}
                className="avatar-image"
            />
        );
    }

    if (activity?.redditPost || activity?.redditComment) {
        return (
            <img
                src={'https://storage.googleapis.com/quarterback-alerts/png/reddit.png'}
                style={imageStyle}
                className="avatar-image"
            />
        );
    }

    return <div style={noImage} />;
}

function ActivityItem({
    activity,
    showSentiment = false
}: {
    activity: Activity2;
    showSentiment?: boolean;
}) {
    const { author, title, body, posted, sentiment } = activity;

    return (
        <div style={container} className="activityItem-container">
            <div style={avatarContainer} className="avatarContainer">
                <ActivityAvatar activity={activity} />
                <table border={0} style={{ width: '80%', marginLeft: '1rem' }}>
                    {author?.name ? (
                        <tr>
                            <td>
                                <span style={authorText} className="authorText">
                                    {author?.name}
                                </span>
                            </td>
                        </tr>
                    ) : null}
                    <tr>
                        <td>
                            <span style={postedStyle} className="postedStyle">
                                {format(posted)}
                            </span>
                        </td>
                    </tr>
                </table>
            </div>

            {title ? (
                <div style={titleContainer} className="titleContainer">
                    <span style={titleStyle} className="titleStyle">
                        {title}
                    </span>
                </div>
            ) : null}

            {body ? (
                <>
                    <p style={paragraph} className="body-paragraph">
                        {body}
                    </p>
                </>
            ) : null}

            {showSentiment && sentiment ? (
                <>
                    <p style={sentimentStyle} className="sentimentStyle">
                        Sentiment: {sentiment?.score?.toFixed(2)}
                    </p>
                </>
            ) : null}
        </div>
    );
}

const container: React.CSSProperties = {
    padding: '0',
    backgroundColor: '#fff',
    borderRadius: '0.5rem',
    border: '1px solid #cccccc',
    marginBottom: '1rem'
};

const avatarContainer: React.CSSProperties = {
    display: 'flex',
    padding: '0.5rem 1rem',
    paddingBottom: '0'
};

const authorText: React.CSSProperties = {
    fontSize: '0.875rem',
    fontWeight: '500'
};

const imageStyle: React.CSSProperties = {
    width: 'auto',
    height: '2.5rem',
    marginTop: '0.4rem',
    borderRadius: '9999px'
};

const postedStyle: React.CSSProperties = {
    fontSize: '0.75rem',
    color: '#4b5563'
};

const titleContainer: React.CSSProperties = {
    marginTop: '1rem',
    padding: '0 1rem'
};

const titleStyle: React.CSSProperties = {
    fontSize: '1rem',
    fontWeight: '500',
    lineHeight: '1.5rem',
    color: '#4b5563'
};

const paragraph: React.CSSProperties = {
    color: '#4b5563',
    borderTop: '1px solid #cccccc',
    whiteSpace: 'pre-line',
    padding: '1rem',
    fontSize: '0.9rem'
};

const noImage: React.CSSProperties = {
    height: '2.5rem',
    width: '2.5rem',
    aspectRatio: '1 / 1',
    borderRadius: '9999px',
    backgroundColor: '#e5e7eb',
    marginTop: '0.4rem'
};

const sentimentStyle = {
    fontSize: '0.875rem',
    fontWeight: '500',
    padding: '0.5rem 1rem',
    color: '#4b5563'
};

export default ActivityItem;
