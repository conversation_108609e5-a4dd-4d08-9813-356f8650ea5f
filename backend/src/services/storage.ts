import { Storage } from '@google-cloud/storage';
import { log } from '@quarterback/util/gcp';

// Initialize storage with service account credentials
let storage: Storage;

try {
    // Log environment information for debugging
    log('INFO', 'Initializing GCP Storage', {
        bucketName: process.env.GCP_ACTIVITY_FILES_BUCKET || 'Not set',
        serviceAccount: process.env.INTERNAL_IAM_EMAIL || 'Not set',
        projectId: process.env.GOOGLE_CLOUD_PROJECT || 'Not set'
    });

    // Initialize the Storage client
    storage = new Storage();
} catch (error: any) {
    log('ERROR', 'Failed to initialize GCP Storage', {
        name: error.name,
        message: error.message,
        stack: error.stack
    });
    throw new Error('Failed to initialize GCP Storage client');
}

// Get bucket name from environment variables
const bucketName = process.env.GCP_ACTIVITY_FILES_BUCKET || '';

// Validate bucket name
if (!bucketName) {
    log('ERROR', 'GCP_ACTIVITY_FILES_BUCKET environment variable is not set');
}

/**
 * Generate a signed URL for uploading a file to GCP Storage
 *
 * @param fileName The name of the file to upload
 * @param contentType The content type of the file
 * @param expiresIn The number of minutes until the URL expires (default: 15)
 * @returns A signed URL for uploading the file
 */
export async function getSignedUploadUrl(
    fileName: string,
    contentType: string,
    expiresIn: number = 15
): Promise<string> {
    try {
        // Create a unique file path to avoid collisions
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const filePath = `activity-files/${timestamp}-${randomString}-${fileName}`;

        // Log bucket information for debugging
        log('INFO', 'Generating signed URL', {
            bucketName,
            filePath,
            contentType,
            serviceAccount: process.env.INTERNAL_IAM_EMAIL || 'Not set'
        });

        const [url] = await storage
            .bucket(bucketName)
            .file(filePath)
            .getSignedUrl({
                version: 'v4',
                action: 'write',
                expires: Date.now() + expiresIn * 60 * 1000, // Convert minutes to milliseconds
                contentType
            });

        return url;
    } catch (error: any) {
        // Enhanced error logging with more details
        const errorDetails = {
            name: error.name,
            message: error.message,
            stack: error.stack,
            fileName,
            contentType,
            bucketName,
            serviceAccount: process.env.INTERNAL_IAM_EMAIL || 'Not set'
        };

        log('ERROR', 'Failed to generate signed upload URL', errorDetails);

        // Create a custom error with more information
        const signingError = new Error('Failed to generate signed upload URL');
        (signingError as any).name = 'SigningError';
        (signingError as any).details = errorDetails;
        throw signingError;
    }
}

/**
 * Generate a signed URL for downloading a file from GCP Storage
 *
 * @param filePath The path of the file in the bucket
 * @param expiresIn The number of minutes until the URL expires (default: 15)
 * @returns A signed URL for downloading the file
 */
export async function getSignedDownloadUrl(
    filePath: string,
    expiresIn: number = 15
): Promise<string> {
    try {
        const [url] = await storage
            .bucket(bucketName)
            .file(filePath)
            .getSignedUrl({
                version: 'v4',
                action: 'read',
                expires: Date.now() + expiresIn * 60 * 1000, // Convert minutes to milliseconds
                responseDisposition: 'attachment'
            });

        return url;
    } catch (error) {
        log('ERROR', 'Failed to generate signed download URL', { error, filePath });
        throw new Error('Failed to generate signed download URL');
    }
}

/**
 * Extract the file path from a signed upload URL
 *
 * @param signedUrl The signed URL
 * @returns The file path in the bucket
 */
export function extractFilePathFromUrl(signedUrl: string): string {
    try {
        const url = new URL(signedUrl);
        // The path starts with /bucket-name/
        const path = url.pathname.substring(bucketName.length + 2);
        return path;
    } catch (error) {
        log('ERROR', 'Failed to extract file path from URL', { error, signedUrl });
        throw new Error('Failed to extract file path from URL');
    }
}
