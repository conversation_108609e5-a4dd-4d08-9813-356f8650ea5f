import { it, describe, expect } from '@jest/globals';
import { MockPublisher } from '@quarterback/scraper-utils/publishers';
import { RateLimiter, RedditScraper } from '../src/scraper.js';
import {
    <PERSON>r<PERSON><PERSON><PERSON>,
    HarRecordingF<PERSON>cher,
    HttpFetcher
} from '@quarterback/scraper-utils/fetchers';

// Toggle to re-record HAR test fixtures, e.g. if the HTML or API structure changes
const UPDATE_FIXTURES = false;

class ImmediateRateLimiter implements RateLimiter {
    async available(): Promise<true> {
        return true;
    }
}

describe('Reddit scraper', () => {
    it('Parses Reddit search results, publishing activities', async () => {
        const publisher = new MockPublisher('scrapers');
        const fetcher = UPDATE_FIXTURES
            ? new HarRecordingFetcher(
                  new URL('./fixtures/brn.zip', import.meta.url).pathname
              )
            : new HarFetcher(new URL('./fixtures/brn.zip', import.meta.url).pathname);

        const rateLimiter = new ImmediateRateLimiter();

        const scraper = new RedditScraper(
            'BRN',
            'ASX',
            'Brainchip',
            publisher,
            fetcher,
            rateLimiter
        );

        await scraper.run();
        await fetcher.flush();

        expect(publisher.messages).toEqual({
            scrapers: [
                {
                    type: 'activities',
                    activities: [
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/brainchip_holdings/',
                            title: 'Brainchip holdings',
                            posted: new Date('2024-01-22T18:52:59.000Z'),
                            body: 'Does anyone have any thoughts on Brainchip holdings? Is it worth the volatility or is there any revenue insight for the company or is it all just a pipe dream?',
                            author: {
                                key: 'reddit__Nark0ne',
                                name: 'Nark0ne',
                                userId: 'Nark0ne'
                            },
                            redditPost: {
                                id: 't3_19d34qf',
                                subreddit: 'ASX',
                                score: 2,
                                ratio: 0.67
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kj5iq8y',
                            posted: new Date('2024-01-23T04:44:25.000Z'),
                            body: 'It’s a zero.',
                            author: {
                                name: 'fistingdonkeys',
                                userId: 'fistingdonkeys',
                                key: 'reddit__fistingdonkeys'
                            },
                            redditComment: {
                                id: 't1_kj5iq8y',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 4
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kj5koyj',
                            posted: new Date('2024-01-23T05:00:43.000Z'),
                            body: 'Read the short case and you will want nothing to do with it.',
                            author: {
                                name: 'onthepunt',
                                userId: 'onthepunt',
                                key: 'reddit__onthepunt'
                            },
                            redditComment: {
                                id: 't1_kj5koyj',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 3
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kj66fcl',
                            posted: new Date('2024-01-23T08:52:44.000Z'),
                            body: 'Not worth pick a better dog',
                            author: { username: 'Informal_Analysis_72' },
                            redditComment: {
                                id: 't1_kj66fcl',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kk7kgoo',
                            posted: new Date('2024-01-30T03:37:56.000Z'),
                            body: 'absolute donkey',
                            author: {
                                name: 'Sea_Camel3510',
                                userId: 'Sea_Camel3510',
                                key: 'reddit__Sea_Camel3510'
                            },
                            redditComment: {
                                id: 't1_kk7kgoo',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kjc2cm5',
                            posted: new Date('2024-01-24T11:11:39.000Z'),
                            body: 'AI ATM is a power hog as were using cpus to simulate neurons. Company’s like BC will make AI available on low powered battery devices like drones. Imagine an armed drone that didnt need a back to base comms link, its only a couple of years away. But agree BC is punt.',
                            author: {
                                name: 'woofydawg',
                                userId: 'woofydawg',
                                key: 'reddit__woofydawg'
                            },
                            redditComment: {
                                id: 't1_kjc2cm5',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kjctm4v',
                            posted: new Date('2024-01-24T14:50:51.000Z'),
                            body: 'So basically the logic is sound and it probably will be an  innovative technology of the future but brainchip is not gonna be the one to get us there.',
                            author: {
                                name: 'Nark0ne',
                                userId: 'Nark0ne',
                                key: 'reddit__Nark0ne'
                            },
                            redditComment: {
                                id: 't1_kjctm4v',
                                post: 't3_19d34qf',
                                parent: 't1_kjc2cm5',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kjbw8r1',
                            posted: new Date('2024-01-24T09:58:26.000Z'),
                            body: 'Dude, read and learn how to trade before using any real money.',
                            author: {
                                name: 'mcgaffen',
                                userId: 'mcgaffen',
                                key: 'reddit__mcgaffen'
                            },
                            redditComment: {
                                id: 't1_kjbw8r1',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kk7ki11',
                            posted: new Date('2024-01-30T03:38:11.000Z'),
                            body: 'absolute donkey',
                            author: {
                                name: 'Sea_Camel3510',
                                userId: 'Sea_Camel3510',
                                key: 'reddit__Sea_Camel3510'
                            },
                            redditComment: {
                                id: 't1_kk7ki11',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/19d34qf/comment/kqpi9l4',
                            posted: new Date('2024-02-16T16:50:32.000Z'),
                            body: "Would've been up 125% if you ignored the advice here. Hopefully you bought some?",
                            author: {
                                name: 'citizenunerased',
                                userId: 'citizenunerased',
                                key: 'reddit__citizenunerased'
                            },
                            redditComment: {
                                id: 't1_kqpi9l4',
                                post: 't3_19d34qf',
                                parent: 't3_19d34qf',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/mgcwvf/brainchip_good_investment_or_bad_investment/',
                            title: 'Brainchip: good investment or bad investment',
                            posted: new Date('2021-03-30T10:31:12.000Z'),
                            body: 'I have bought 2700 shares at 51cents in Feb 2021, keen to know what is the outlook of this company. On the face value the company looks good long term buy, but what intriguing that the company has made neglible to 0  revenue in last 5 years, yet its valued at over 1 billion. Also cannot understand the drivers behind the sharp downfall in price which dropped from the highs of 80 cents to 53 cents in last 6 months. No new material news other than departure of CEO have been announced in last month or so. Would appreciate your insights, ideas, outlook and opinions on this company.\n\n[View Poll](https://www.reddit.com/poll/mgcwvf)',
                            author: {
                                name: 'rickster5587',
                                userId: 'rickster5587',
                                key: 'reddit__rickster5587'
                            },
                            redditPost: {
                                id: 't3_mgcwvf',
                                subreddit: 'ASX',
                                score: 6,
                                ratio: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/mgcwvf/comment/gssesdy',
                            posted: new Date('2021-03-30T11:20:49.000Z'),
                            body: 'It’s been rolling the past few months. I bought some last year.. currently sitting at around 420% profit funnily enough 😅 which is down from well over 600%, it’s pulled back recently.\nDon’t have a heap of DD to throw at you but I like the stock.',
                            author: {
                                name: 'woll187',
                                userId: 'woll187',
                                key: 'reddit__woll187'
                            },
                            redditComment: {
                                id: 't1_gssesdy',
                                post: 't3_mgcwvf',
                                parent: 't3_mgcwvf',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/mgcwvf/comment/gsuqj10',
                            posted: new Date('2021-03-30T22:44:13.000Z'),
                            body: 'There was an expected announcement of awarding of new contracts by NASA on 25th March 2021 and it was highly anticipated that BRN would disclose this on ASX, but there was nothing. Agree it has a profile of Tesla stock, but does it have the capability to deliver like Tesla  ? Thanks again gor sharing your views',
                            author: {
                                name: 'rickster5587',
                                key: 'reddit__rickster5587',
                                userId: 'rickster5587'
                            },
                            redditComment: {
                                id: 't1_gsuqj10',
                                post: 't3_mgcwvf',
                                parent: 't3_mgcwvf',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/mgcwvf/comment/gt19j4l',
                            posted: new Date('2021-04-01T14:34:52.000Z'),
                            body: 'I am in on the dips. 3k shares so far. Spending time learning about their chip, which led me to TSM, which is doing well this year and will be making the Akida brainchip. Chips are hot, and this chip seems to be testing well. I am way long probably into 2022, expect 3x. before sales really take off.',
                            author: {
                                name: 'Rubblebeam',
                                key: 'reddit__Rubblebeam',
                                userId: 'Rubblebeam'
                            },
                            redditComment: {
                                id: 't1_gt19j4l',
                                post: 't3_mgcwvf',
                                parent: 't3_mgcwvf',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/mgcwvf/comment/gt4cczx',
                            posted: new Date('2021-04-02T07:28:59.000Z'),
                            body: 'I sold yesterday, made 53% on my original purchase price. I will continue to look at this and buy at dips, provided some more certainty around timelines and deliverables of ongoing projects',
                            author: {
                                name: 'rickster5587',
                                key: 'reddit__rickster5587',
                                userId: 'rickster5587'
                            },
                            redditComment: {
                                id: 't1_gt4cczx',
                                post: 't3_mgcwvf',
                                parent: 't1_gt19j4l',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/163kc2j/asx_recap_28th_august/',
                            title: 'ASX Recap 28th August',
                            posted: new Date('2023-08-28T12:07:27.000Z'),
                            body: '**Helloworld (HLO)** *shares bounced 6.0%*, with the company reporting a strong FY23 result underpinned by returning traveller demand, removal of border restrictions and increasing supply capacity. \n\n**EVT Ltd (EVT)** posted full-year earnings, which were received positively by the marker as the entertainment and leisure company reported record results for its Hotels and Thredbo business \n\n**Appen’s (APX)** share price took another big hit today, *falling by 32.1% to a 7-year low of $1.52* as the company warned it continues to face headwinds as customers evaluate their A.I. strategies. \n\n **Brainchip’s (BRN)** once-bubbly share price is losing its froth, shedding *10.7% to close at $0.29,* well down from the dizzying heights of $2.09 in January 2022. \n\nGet the Full Recap -&gt; [https://asx-news.beehiiv.com/p/fortescues-musical-chairs-continue](https://asx-news.beehiiv.com/p/fortescues-musical-chairs-continue)',
                            author: {
                                name: 'ASX_News',
                                userId: 'ASX_News',
                                key: 'reddit__ASX_News'
                            },
                            redditPost: {
                                id: 't3_163kc2j',
                                subreddit: 'ASX',
                                score: 1,
                                ratio: 0.67
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/any_arteficial_intelligence_stocks_looking/',
                            title: 'Any Arteficial Intelligence stocks looking interesting at the moment?',
                            posted: new Date('2020-09-08T06:42:12.000Z'),
                            body: 'Currently invested in Brainchip BRN which are doing me very well and would like to expand my portfolio across more AI companies. Any safe long\\\\medium term buys?\n\ncheers.',
                            author: {
                                name: 'Cambadoi',
                                userId: 'Cambadoi',
                                key: 'reddit__Cambadoi'
                            },
                            redditPost: {
                                id: 't3_ioos37',
                                subreddit: 'ASX',
                                score: 4,
                                ratio: 0.83
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g4f91n7',
                            posted: new Date('2020-09-08T08:13:01.000Z'),
                            body: "Lots but if you can't google do you want to throw your money away :) anyways today's flavour is brainchip",
                            author: {
                                name: 'Pandibabi',
                                userId: 'Pandibabi',
                                key: 'reddit__Pandibabi'
                            },
                            redditComment: {
                                id: 't1_g4f91n7',
                                post: 't3_ioos37',
                                parent: 't3_ioos37',
                                score: 5
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g4fc6yq',
                            posted: new Date('2020-09-08T09:13:25.000Z'),
                            body: "&gt;anyways today's flavour is brainchip\n\nThis BRN is the real shit, some people say brain shit is the shit.",
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_g4fc6yq',
                                post: 't3_ioos37',
                                parent: 't1_g4f91n7',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g4flrpx',
                            posted: new Date('2020-09-08T11:56:56.000Z'),
                            body: 'Its unlikely you\'re doing well because you chose to "invest in AI" and much more likely because you got lucky and road a wave of positive market sentiment. It won\'t keep happening like that so begin using available tools to conduct thorough fundamental analysis so you can generate lasting returns over multiple years.',
                            author: {
                                name: 'aintthateazy',
                                userId: 'aintthateazy',
                                key: 'reddit__aintthateazy'
                            },
                            redditComment: {
                                id: 't1_g4flrpx',
                                post: 't3_ioos37',
                                parent: 't3_ioos37',
                                score: 3
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g4f6dn4',
                            posted: new Date('2020-09-08T07:24:33.000Z'),
                            body: '&gt; **Arteficial** \n\nThat is so lazy to even use a spell checker. This is a lazy post.  Could be good, AI is the flavor of the month.',
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_g4f6dn4',
                                post: 't3_ioos37',
                                parent: 't3_ioos37',
                                score: 7
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g4fcfn8',
                            posted: new Date('2020-09-08T09:18:09.000Z'),
                            body: 'Your own post is riddled with errors.',
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_g4fcfn8',
                                post: 't3_ioos37',
                                parent: 't1_g4f6dn4',
                                score: 4
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g4fesui',
                            posted: new Date('2020-09-08T10:03:23.000Z'),
                            body: 'like me not telling you to suck a cock?',
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_g4fesui',
                                post: 't3_ioos37',
                                parent: 't1_g4fcfn8',
                                score: 5
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g4fm70r',
                            posted: new Date('2020-09-08T12:02:46.000Z'),
                            body: 'Eh?',
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_g4fm70r',
                                post: 't3_ioos37',
                                parent: 't1_g4fesui',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/ioos37/comment/g5ph5hy',
                            posted: new Date('2020-09-18T14:57:45.000Z'),
                            body: 'Therew a few presenting at the StockPal webinar I see \n\nhttps://us02web.zoom.us/webinar/register/1016002496246/WN_PXpV6ohlQPyDayItR4OICA',
                            author: {
                                name: 'firstrunescape',
                                userId: 'firstrunescape',
                                key: 'reddit__firstrunescape'
                            },
                            redditComment: {
                                id: 't1_g5ph5hy',
                                post: 't3_ioos37',
                                parent: 't3_ioos37',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/tech_stocks/',
                            title: 'TECH stocks',
                            posted: new Date('2022-08-31T00:31:22.000Z'),
                            body: 'Hey all, I’m looking into putting some money into a tech stock. Can anyone recommend some stocks/EFTs to look into?',
                            author: {
                                name: 'Nrth11',
                                userId: 'Nrth11',
                                key: 'reddit__Nrth11'
                            },
                            redditPost: {
                                id: 't3_x1zb4k',
                                subreddit: 'ASX',
                                score: 2,
                                ratio: 0.75
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/imktgh3',
                            posted: new Date('2022-08-31T21:37:43.000Z'),
                            body: 'Brainchip - BRN',
                            author: {
                                name: 'blue610610',
                                userId: 'blue610610',
                                key: 'reddit__blue610610'
                            },
                            redditComment: {
                                id: 't1_imktgh3',
                                post: 't3_x1zb4k',
                                parent: 't3_x1zb4k',
                                score: 2
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/imgtl3p',
                            posted: new Date('2022-08-31T01:35:57.000Z'),
                            body: 'NDQ (Nasdaq 100) is a popular one if you want US tech stocks.',
                            author: {
                                name: 'Slo20',
                                userId: 'Slo20',
                                key: 'reddit__Slo20'
                            },
                            redditComment: {
                                id: 't1_imgtl3p',
                                post: 't3_x1zb4k',
                                parent: 't3_x1zb4k',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/imgy1yk',
                            posted: new Date('2022-08-31T02:09:58.000Z'),
                            body: "TECH, HACK and RBTZ are all in my holdings. You might also want to check out ROBO, ACDC and CURE. all are etfs and have a pretty high risk factor and a high fee (most I think are 0.5% or higher). You'll also want to check for overlap if you pick up more than 1.",
                            author: {
                                name: 'Upper_Ranger_4877',
                                userId: 'Upper_Ranger_4877',
                                key: 'reddit__Upper_Ranger_4877'
                            },
                            redditComment: {
                                id: 't1_imgy1yk',
                                post: 't3_x1zb4k',
                                parent: 't3_x1zb4k',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/imhdzcy',
                            posted: new Date('2022-08-31T04:28:02.000Z'),
                            body: "I have EGG   \nIts been a bit of an up and down ..but hasn't everything been like that these days?",
                            author: {
                                name: 'Andrew_Higginbottom',
                                userId: 'Andrew_Higginbottom',
                                key: 'reddit__Andrew_Higginbottom'
                            },
                            redditComment: {
                                id: 't1_imhdzcy',
                                post: 't3_x1zb4k',
                                parent: 't3_x1zb4k',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/imhn5zk',
                            posted: new Date('2022-08-31T06:12:14.000Z'),
                            body: 'Fang if you want the big boys\n\nNdq if you want more general tech in US\n\nPERSONALLY bits about 15% each of my portfolio for these two.',
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_imhn5zk',
                                post: 't3_x1zb4k',
                                parent: 't3_x1zb4k',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/iml1ifw',
                            posted: new Date('2022-08-31T22:32:48.000Z'),
                            body: 'Why tech? Do you think they’re any better than the rest of the market?',
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_iml1ifw',
                                post: 't3_x1zb4k',
                                parent: 't3_x1zb4k',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/in4h58o',
                            posted: new Date('2022-09-05T00:32:14.000Z'),
                            body: 'I think technology is the way of the future, I don’t see why you wouldn’t want to invest is tech based stocks?',
                            author: {
                                name: 'Nrth11',
                                userId: 'Nrth11',
                                key: 'reddit__Nrth11'
                            },
                            redditComment: {
                                id: 't1_in4h58o',
                                post: 't3_x1zb4k',
                                parent: 't1_iml1ifw',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/in4obev',
                            posted: new Date('2022-09-05T01:28:13.000Z'),
                            body: 'The concept of "the market" already includes tech, and at a substantial proportion. The biggest companies in the market are already tech companies. The question, do you think they need to take an even larger portion of your portfolio?',
                            author: {
                                name: '[deleted]',
                                userId: '[deleted]',
                                key: 'reddit__[deleted]'
                            },
                            redditComment: {
                                id: 't1_in4obev',
                                post: 't3_x1zb4k',
                                parent: 't1_in4h58o',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/inejh6n',
                            posted: new Date('2022-09-07T03:11:42.000Z'),
                            body: 'I think most fundamentally, and this is a generalisation, tech has big upside around low incremental cost.\n\nAs to being any better than rest of the market; well that depends what better means. Current market conditions make it hard from a funding perspective - a tech business in the start up / acquisition phase is going to be having a hard time, regardless of how good their idea is.\n\nBut if you’re looking long run - say 5+ years - it’s hard to say that the tech majors are a bad investment at current prices. \n\nDisclaimer: not financial advice and there’s always a chance that Meta/Google/Apple go the way of MySpace.',
                            author: {
                                name: 'yeahbroyeahbro',
                                userId: 'yeahbroyeahbro',
                                key: 'reddit__yeahbroyeahbro'
                            },
                            redditComment: {
                                id: 't1_inejh6n',
                                post: 't3_x1zb4k',
                                parent: 't1_iml1ifw',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            url: 'https://www.reddit.com/r/ASX/comments/x1zb4k/comment/ipl155w',
                            posted: new Date('2022-09-23T11:12:14.000Z'),
                            body: 'HACK. Or profit making holdings within.',
                            author: {
                                name: 'PilbaraWanderer',
                                userId: 'PilbaraWanderer',
                                key: 'reddit__PilbaraWanderer'
                            },
                            redditComment: {
                                id: 't1_ipl155w',
                                post: 't3_x1zb4k',
                                parent: 't3_x1zb4k',
                                score: 1
                            },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        }
                    ]
                }
            ]
        });
    }, 30000);
});
