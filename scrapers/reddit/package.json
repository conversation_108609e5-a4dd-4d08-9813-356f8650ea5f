{"name": "@quarterback/reddit", "module": "index.ts", "type": "module", "scripts": {"start": "node ./dist/src/index.js", "build": "pnpm tsc", "begin": "node ./dist/src/index.js --symbol AUG --query \"\\\"Augustus Minerals\\\" OR \\\"AUG:ASX\\\"\" --symbol AZL --query \"\\\"Arizona Lithium\\\" OR \\\"AZL:ASX\\\"\" --symbol MNC --query \"\\\"Merino & Co\\\" OR \\\"MNC:ASX\\\"\" --symbol ICL --query \"\\\"iceni gold\\\" OR \\\"ICL:ASX\\\"\" --symbol TYP --query \"\\\"Tryptamine Therapeutics\\\" OR \\\"TYP:ASX\\\"\" --symbol WGX --query \"\\\"WGX\\\" OR \\\"West Gold Resources\\\" OR \\\"WGX:ASX\\\"\" --exchange ASX"}, "files": ["dist"], "peerDependencies": {"typescript": "^5.0.0"}, "packageManager": "pnpm@9.9.0", "dependencies": {"@google-cloud/pubsub": "^4.3.3", "@quarterback/scraper-utils": "workspace:*", "@quarterback/types": "workspace:*", "@quarterback/util": "workspace:*", "arg": "^5.0.2", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.12.7", "typescript": "^5.4.5"}}