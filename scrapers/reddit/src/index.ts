import arg from 'arg';
import { RateLimiter, RedditScraper } from './scraper.js';
import { LoggingPublisher, PubSubPublisher } from '@quarterback/scraper-utils/publishers';
import { HttpFetcher } from '@quarterback/scraper-utils/fetchers';

class TimeoutRateLimiter implements RateLimiter {
    constructor() {}

    async available(): Promise<true> {
        return new Promise((resolve) => {
            setTimeout(() => resolve(true), 4000);
        });
    }
}

const args = arg({
    '--symbol': [String],
    '--query': [String],
    '--exchange': String
});

if (!args['--symbol'] || !args['--symbol'].length)
    throw new Error('missing required argument: --symbol');
if (!args['--query'] || !args['--query'].length)
    throw new Error('missing required argument: --query');
if (args['--symbol'].length !== args['--query'].length)
    throw new Error("symbol and query lengths don't match");
if (!args['--exchange']) throw new Error('missing required argument: --exchange');

const publisher = process.env['PUBSUB_TOPIC']
    ? new PubSubPublisher(process.env['PUBSUB_TOPIC'])
    : new LoggingPublisher('scrapers');

const fetcher = new HttpFetcher();
const rateLimiter = new TimeoutRateLimiter();

for (const index in args['--symbol']) {
    const scraper = new RedditScraper(
        args['--symbol'][index],
        args['--exchange'],
        args['--query'][index],
        publisher,
        fetcher,
        rateLimiter
    );

    await scraper.run();
}
