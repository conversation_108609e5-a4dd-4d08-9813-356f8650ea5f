import { Publisher } from '@quarterback/scraper-utils/publishers';
import { Scraper } from '@quarterback/scraper-utils/scrapers';
import { Fetcher } from '@quarterback/scraper-utils/fetchers';
import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';
import xml2js from 'xml2js';
import { URL } from 'url';
import { log } from '@quarterback/util/gcp';
import { Activity2 } from '@quarterback/types';
import { chunked } from '@quarterback/util';
import decodeURL from './decodeUrl.js';
import * as cheerio from 'cheerio';

type NewsActivity = Activity2 & Required<Pick<Activity2, 'news'>>;

export class GoogleNewsScraper extends Scraper {
    fetcher: Fetcher;
    query: string;

    constructor(
        symbol: string,
        exchange: string,
        query: string,
        publisher: Publisher<PubSubMessage2>,
        fetcher: Fetcher
    ) {
        super(symbol, exchange, publisher);

        this.fetcher = fetcher;
        this.query = query;
    }

    async openGraph(
        url: string
    ): Promise<{ description: string | undefined; image: string | undefined }> {
        const response = await this.fetcher.fetch(url, {
            headers: {
                // Google News won't follow redirects unless a user agent is specified
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            signal: AbortSignal.timeout(5000)
        });

        const $ = cheerio.load(await response.text());

        return {
            description: $(`meta[property='og:description']`).attr()?.['content'],
            image: $(`meta[property='og:image']`).attr()?.['content']
        };
    }

    async *chunk() {
        const parser = new xml2js.Parser();
        const url = new URL(
            `https://news.google.com/rss/search?q=${encodeURIComponent(this.query)}&hl=en-AU&gl=AU&ceid=AU:en`
        );

        const { rss } = await this.fetcher
            .fetch(url)
            .then((it) => it.text())
            .then((it) => parser.parseStringPromise(it));

        const items = (rss['channel'] ?? [])
            .map((channel: any) => channel['item'] ?? [])
            .flat()
            .filter((it: any) => it['link'][0]);

        for (const chunk of chunked(items, 10)) {
            yield await Promise.allSettled(
                chunk.map(async (item: any): Promise<NewsActivity> => {
                    const posted = new Date(item['pubDate'][0].match(/^(.*)\s+(\w+)$/)[1] + 'Z');
                    const url = await decodeURL(item['link'][0]);
                    const source = {
                        name: item['source'][0]['_'],
                        url: item['source'][0]['$']['url'],
                        logo: null
                    };

                    try {
                        const { image, description } = await this.openGraph(url);

                        return {
                            posted,
                            title: item['title'][0].replace(new RegExp(` - ${source.name}$`), ''),
                            body: description,
                            url,
                            news: {
                                image,
                                source: {
                                    name: source.name,
                                    domain: source.url
                                }
                            },

                            symbol: this.symbol,
                            exchange: this.exchange
                        };
                    } catch (error) {
                        log('WARNING', `Scraper timed out for URL: ${url}`);
                        throw error;
                    }
                })
            ).then((it) =>
                it
                    .filter((it) => it.status === 'fulfilled')
                    .map((it) => (it as PromiseFulfilledResult<NewsActivity>).value)
            );
        }
    }
}
