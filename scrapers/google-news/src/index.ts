import arg from 'arg';
import { GoogleNewsScraper } from './scraper.js';
import { LoggingPublisher, PubSubPublisher } from '@quarterback/scraper-utils/publishers';
import { HttpFetcher } from '@quarterback/scraper-utils/fetchers';

const args = arg({
    '--symbol': String,
    '--exchange': String,
    '--query': String
});

if (!args['--symbol']) throw new Error('missing required argument: --symbol');
if (!args['--exchange']) throw new Error('missing required argument: --exchange');
if (!args['--query']) throw new Error('missing required argument: --query');

const publisher = process.env['PUBSUB_TOPIC']
    ? new PubSubPublisher(process.env['PUBSUB_TOPIC'])
    : new LoggingPublisher('scrapers');

const fetcher = new HttpFetcher();
const scraper = new GoogleNewsScraper(
    args['--symbol'],
    args['--exchange'],
    args['--query'],
    publisher,
    fetcher
);

await scraper.run();
