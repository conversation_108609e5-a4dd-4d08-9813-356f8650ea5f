import { URL } from 'url';

function fetchDecodedBatchExecute(id: string) {
    const s =
        '[[["Fbv4je","[\\"garturlreq\\",[[\\"en-US\\",\\"US\\",[\\"FINANCE_TOP_INDICES\\",\\"WEB_TEST_1_0_0\\"],null,null,1,1,\\"US:en\\",null,180,null,null,null,null,null,0,null,null,[1608992183,723341000]],\\"en-US\\",\\"US\\",1,[2,3,4,8],1,0,\\"655000234\\",0,0,null,0],\\"' +
        id +
        '\\"]",null,"generic"]]]';

    return fetch('https://news.google.com/_/DotsSplashUi/data/batchexecute?' + 'rpcids=Fbv4je', {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
            Referrer: 'https://news.google.com/'
        },
        body: 'f.req=' + encodeURIComponent(s),
        method: 'POST'
    })
        .then((e) => e.text())
        .then((s) => {
            const header = '[\\"garturlres\\",\\"';
            const footer = '\\",';
            if (!s.includes(header)) {
                throw new Error('header not found: ' + s);
            }
            const start = s.substring(s.indexOf(header) + header.length);
            if (!start.includes(footer)) {
                throw new Error('footer not found');
            }
            return start.substring(0, start.indexOf(footer));
        });
}

function base64Buffer(urlSafe: string) {
    const base64 = urlSafe.replace(/-/g, '+').replace(/_/g, '/');
    return Buffer.from(
        base64.padEnd(base64.length + ((4 - (base64.length % 4)) % 4), '='),
        'base64'
    );
}

// https://gist.github.com/huksley/bc3cb046157a99cd9d1517b32f91a99e
export default async function decodeURL(url: string): Promise<string> {
    const pathComponents = new URL(url).pathname.split('/');
    const base64str = pathComponents[pathComponents.length - 1];
    let decoded = base64Buffer(base64str).toString('latin1');

    const prefix = Buffer.from([0x08, 0x13, 0x22]).toString('binary');
    if (decoded.startsWith(prefix)) {
        decoded = decoded.substring(prefix.length);
    }

    const suffix = Buffer.from([0xd2, 0x01, 0x00]).toString('binary');
    if (decoded.endsWith(suffix)) {
        decoded = decoded.substring(0, decoded.length - suffix.length);
    }

    const bytes = Uint8Array.from(decoded, (c) => c.charCodeAt(0));
    const len = bytes.at(0)!;
    if (len >= 0x80) {
        decoded = decoded.substring(2, len + 1);
    } else {
        decoded = decoded.substring(1, len + 1);
    }

    if (decoded.startsWith('AU_yqL')) {
        return await fetchDecodedBatchExecute(base64str);
    }

    return decoded;
}
