{"compilerOptions": {"lib": ["ESNext"], "module": "NodeNext", "target": "esnext", "moduleResolution": "nodenext", "moduleDetection": "force", "allowImportingTsExtensions": false, "noEmit": false, "composite": true, "strict": true, "downlevelIteration": true, "skipLibCheck": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "types": ["node"], "esModuleInterop": true, "outDir": "dist", "rootDirs": ["src", "tests"]}, "include": ["src/**/*", "tests/**/*"]}