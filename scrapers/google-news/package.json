{"name": "@quarterback/google-news", "module": "index.ts", "type": "module", "scripts": {"start": "node ./dist/src/index.js", "build": "pnpm tsc"}, "files": ["dist"], "peerDependencies": {"typescript": "^5.0.0"}, "packageManager": "pnpm@9.9.0", "dependencies": {"@google-cloud/pubsub": "^4.3.3", "@quarterback/scraper-utils": "workspace:*", "@quarterback/types": "workspace:*", "@quarterback/util": "workspace:*", "arg": "^5.0.2", "cheerio": "1.0.0-rc.12", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "tsx": "^4.8.2", "xml2js": "^0.6.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.12.7", "@types/xml2js": "^0.4.14", "typescript": "^5.4.5"}}