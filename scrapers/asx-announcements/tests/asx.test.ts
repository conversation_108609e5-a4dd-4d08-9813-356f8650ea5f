import { expect, it, describe, afterAll } from '@jest/globals';
import { MockPublisher } from '@quarterback/scraper-utils/publishers';
import { ASXScraper } from '../src/scraper.js';
import { chromium } from 'playwright';
import { ASXPeriod } from '@quarterback/types';

// Toggle to re-record HAR test fixtures, e.g. if the HTML structure changes
const UPDATE_FIXTURES = false;

const browser = await chromium.launch({ headless: true });
const context = await browser.newContext(
    UPDATE_FIXTURES
        ? {
              recordHar: { path: new URL('./fixtures/brn.zip', import.meta.url).pathname }
          }
        : {
              serviceWorkers: 'block',
              javaScriptEnabled: false
          }
);

if (!UPDATE_FIXTURES) {
    await context.routeFromHAR(new URL('./fixtures/brn.zip', import.meta.url).pathname);
}

afterAll(async () => {
    await context.close();
    await browser.close();
});

describe('ASX Announcements scraper', () => {
    it('Parses ASX announcement pages, publishing activities', async () => {
        const publisher = new MockPublisher('scrapers');
        const scraper = new ASXScraper('BRN', 'ASX', ASXPeriod.month, publisher, context);

        await scraper.run();

        expect(publisher.messages).toEqual({
            scrapers: [
                {
                    type: 'activities',
                    activities: [
                        {
                            title: 'Notification regarding unquoted securities - BRN',
                            url: 'https://announcements.asx.com.au/asxpdf/20240814/pdf/066ll5rhcdlr9q.pdf',
                            posted: new Date('2024-08-13T22:50:00.000Z'),
                            asx: { sensitive: false },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Cleansing Notice',
                            url: 'https://announcements.asx.com.au/asxpdf/20240801/pdf/06667d5m1wsnn8.pdf',
                            posted: new Date('2024-07-31T22:46:00.000Z'),
                            asx: { sensitive: false },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Application for quotation of securities - BRN',
                            url: 'https://announcements.asx.com.au/asxpdf/20240801/pdf/0666719cmf2hf0.pdf',
                            posted: new Date('2024-07-31T22:42:00.000Z'),
                            asx: { sensitive: false },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Share Purchase Plan Offer',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xwb8mmbbb5k.pdf',
                            posted: new Date('2024-07-25T02:38:00.000Z'),
                            asx: { sensitive: true },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Cleansing Notice',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xrbsg2xs1ps.pdf',
                            posted: new Date('2024-07-25T00:25:00.000Z'),
                            asx: { sensitive: false },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        }
                    ]
                },
                {
                    type: 'activities',
                    activities: [
                        {
                            title: 'Proposed issue of securities - BRN',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xr8qb629yqr.pdf',
                            posted: new Date('2024-07-25T00:25:00.000Z'),
                            asx: { sensitive: false },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Proposed issue of securities - BRN',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xr6nq4l3tkw.pdf',
                            posted: new Date('2024-07-25T00:25:00.000Z'),
                            asx: { sensitive: false },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Investor Presentation',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xr4ll871x58.pdf',
                            posted: new Date('2024-07-25T00:24:00.000Z'),
                            asx: { sensitive: true },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'A$25million Equity Capital Raise',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xr2jg9031j9.pdf',
                            posted: new Date('2024-07-25T00:24:00.000Z'),
                            asx: { sensitive: true },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Top 20 Shareholders',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xr0411hsjn2.pdf',
                            posted: new Date('2024-07-25T00:22:00.000Z'),
                            asx: { sensitive: false },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        }
                    ]
                },
                {
                    type: 'activities',
                    activities: [
                        {
                            title: 'Appendix 4C and Quarterly Activities Report',
                            url: 'https://announcements.asx.com.au/asxpdf/20240725/pdf/065xqy1d8t2123.pdf',
                            posted: new Date('2024-07-25T00:22:00.000Z'),
                            asx: { sensitive: true },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        },
                        {
                            title: 'Trading Halt',
                            url: 'https://announcements.asx.com.au/asxpdf/20240723/pdf/065v63q7q94jwb.pdf',
                            posted: new Date('2024-07-22T23:50:00.000Z'),
                            asx: { sensitive: true },
                            symbol: 'BRN',
                            exchange: 'ASX'
                        }
                    ]
                }
            ]
        });
    }, 10000);
});
