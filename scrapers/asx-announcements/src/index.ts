import { chromium } from 'playwright';
import arg from 'arg';

import { ASXPeriod } from '@quarterback/types';
import { LoggingPublisher, PubSubPublisher } from '@quarterback/scraper-utils/publishers';
import { ASXScraper } from './scraper.js';

const args = arg({
    '--symbol': String,
    '--exchange': String,
    '--period': String
});

if (!args['--symbol']) throw new Error('missing required argument: --symbol');
if (!args['--exchange']) throw new Error('missing required argument: --exchange');
if (!args['--period']) args['--period'] = ASXPeriod.half;
if (!Object.values(ASXPeriod).includes(args['--period'] as ASXPeriod)) {
    throw new Error(`invalid argument value: --period ${args['--period']}`);
}

const browser = await chromium.launch({ headless: true });
const context = await browser.newContext();

const publisher = process.env['PUBSUB_TOPIC']
    ? new PubSubPublisher(process.env['PUBSUB_TOPIC'])
    : new LoggingPublisher('scrapers');

const scraper = new ASXScraper(
    args['--symbol'],
    args['--exchange'],
    args['--period'] as ASXPeriod,
    publisher,
    context
);

await scraper.run();

await context.close();
await browser.close();
