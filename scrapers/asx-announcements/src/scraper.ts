import { Activity2, ASXPeriod } from '@quarterback/types';
import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';
import { BrowserContext } from 'playwright';
import { URL } from 'url';
import { chunked } from '@quarterback/util';
import { Publisher } from '@quarterback/scraper-utils/publishers';
import { Scraper } from '@quarterback/scraper-utils/scrapers';
import { fromZonedTime } from 'date-fns-tz';
import { parse } from 'date-fns';
import pdf2md from '@opendocsg/pdf2md';
import { Readable } from 'node:stream';
import { log } from '@quarterback/util/gcp';

function asBuffer(stream: Readable): Promise<Buffer> {
    return new Promise((resolve, reject) => {
        const chunks: Array<Buffer> = [];

        stream.on('data', (chunk: Buffer) => {
            chunks.push(chunk);
        });

        stream.on('end', () => {
            resolve(Buffer.concat(chunks));
        });

        stream.on('error', (err) => {
            reject(err);
        });
    });
}

export class ASXScraper extends Scraper {
    period: ASXPeriod;
    context: BrowserContext;

    constructor(
        symbol: string,
        exchange: string,
        period: ASXPeriod,
        publisher: Publisher<PubSubMessage2>,
        context: BrowserContext
    ) {
        super(symbol, exchange, publisher);

        this.period = period;
        this.context = context;
    }

    async *chunk() {
        if (this.exchange !== 'ASX') {
            yield [];
        } else {
            const url = new URL(
                'https://www.asx.com.au/asx/v2/statistics/announcements.do'
            );

            url.searchParams.append('by', 'asxCode');
            url.searchParams.append('asxCode', this.symbol);
            url.searchParams.append('timeframe', 'D');
            url.searchParams.append('period', this.period);

            const page = await this.context.newPage();
            await page.goto(url.toString());

            const table = page
                .locator(`table[summary='Most recent company announcements']`)
                .first();
            const rows = await table.locator('tbody > tr').all();

            for (const chunk of chunked(rows, 5)) {
                yield await Promise.all(
                    chunk.map(async (row): Promise<Activity2> => {
                        const columns = await row.locator('td').all();

                        const date = await columns[0].innerText();
                        const sensitive = (await columns[1].locator('img').count()) > 0;
                        const pdf = await columns[2].locator('a').getAttribute('href');
                        const title = await columns[2]
                            .locator('a')
                            .evaluate((el) => el.firstChild.textContent)
                            .then((it) => it.trim());

                        const pdfPage = await this.context.newPage();
                        const downloadEvent = pdfPage.waitForEvent('download');

                        try {
                            await pdfPage.goto(`https://www.asx.com.au${pdf}`, {
                                timeout: 10000
                            });

                            await pdfPage
                                .locator('input[value="Agree and proceed"]')
                                .click();
                        } catch (_) {}

                        const download = await downloadEvent;

                        const url = download.url();
                        let body: string | undefined = undefined;

                        try {
                            const buffer = await asBuffer(
                                await download.createReadStream()
                            ).then((it) => it.buffer);

                            // @ts-ignore FML
                            body = await pdf2md(buffer);
                        } catch (error) {
                            log('ERROR', 'Error parsing announcement body');
                        }

                        await pdfPage.close();

                        return {
                            title,
                            body,
                            url: url!,
                            posted: fromZonedTime(
                                parse(
                                    date.replace('\n', ' '),
                                    'dd/MM/yyyy h:mm aa',
                                    new Date()
                                ),
                                'Australia/Sydney'
                            ),
                            asx: {
                                sensitive: sensitive
                            },
                            symbol: this.symbol,
                            exchange: this.exchange,
                            isBroadcast: true
                        };
                    })
                );
            }

            await page.close();
        }
    }
}
