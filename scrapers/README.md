# Scrapers

## Updating <PERSON>wright

When updating <PERSON><PERSON> we need to ensure the version is pinned to a specific version, and the Dockerfile is updated to reflect that new version, to prevent <PERSON><PERSON> from prompting to upgrade upon container run.

## Packages

-   asx-announcements
    -   Fetches announcements from the ASX announcements database
-   googl-news
    -   Fetches news results from the Google News RSS feed
-   hotcopper
    -   Fetches hotcopper chatter
