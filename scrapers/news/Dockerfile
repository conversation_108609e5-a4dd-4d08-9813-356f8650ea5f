FROM node:22-alpine AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
# RUN corepack enable #corepack is causing issues. enable later and remove line below
RUN npm install -g pnpm@9.15.5

FROM base AS build
COPY pnpm-lock.yaml /app/
COPY pnpm-workspace.yaml /app/
COPY package.json /app/
COPY ./types /app/types
COPY ./util /app/util
COPY ./scraper-utils /app/scraper-utils
COPY ./scrapers/news /app/scrapers/news
WORKDIR /app
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --filter=@quarterback/news --filter=@quarterback/types --filter=@quarterback/scraper-utils --filter=@quarterback/util --config.dedupe-peer-dependents=false --frozen-lockfile
RUN pnpm run -r --filter=@quarterback/news --filter=@quarterback/types --filter=@quarterback/scraper-utils --filter=@quarterback/util build
RUN pnpm deploy --filter=@quarterback/news --prod /dist/news

FROM base AS news-scraper
COPY --from=build /dist/news /app
WORKDIR /app
ENTRYPOINT [ "pnpm", "start" ]
