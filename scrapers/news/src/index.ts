import arg from 'arg';
import { LoggingPublisher, PubSubPublisher } from '@quarterback/scraper-utils/publishers';
import { HttpFetcher } from '@quarterback/scraper-utils/fetchers';
import { NewsScraper } from './scraper.js';

const args = arg({
    '--symbol': String,
    '--exchange': String,
    '--params': String
});

if (!args['--symbol']) throw new Error('missing required argument: --symbol');
if (!args['--exchange']) throw new Error('missing required argument: --exchange');
if (!args['--params']) throw new Error('missing required argument: --params');

if (!process.env['NEWSCATCHER_API_KEY']) {
    throw new Error('missing required API key');
}

const publisher = process.env['PUBSUB_TOPIC']
    ? new PubSubPublisher(process.env['PUBSUB_TOPIC'])
    : new LoggingPublisher('scrapers');

const fetcher = new HttpFetcher();

const scraper = new NewsScraper(
    args['--symbol'],
    args['--exchange'],
    args['--params'],
    publisher,
    fetcher
);

await scraper.run();
