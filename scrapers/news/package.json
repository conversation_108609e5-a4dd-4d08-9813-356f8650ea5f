{"name": "@quarterback/news", "module": "index.ts", "type": "module", "scripts": {"start": "node ./dist/index.js", "build": "pnpm tsc", "start:local": "node ./dist/index.js --symbol VEE --exchange ASX --params '{\"q\":\"\\\"VEEM Limited\\\" OR \\\"VEEM Ltd\\\" OR \\\"VEE?ASX\\\" OR \\\"ASX?VEE\\\"\",\"lang\":\"en\",\"exclude_duplicates\":true,\"ranked_only\":false,\"sort_by\":\"date\"}'"}, "files": ["dist"], "peerDependencies": {"typescript": "^5.0.0"}, "packageManager": "pnpm@9.9.0", "dependencies": {"@google-cloud/pubsub": "^4.3.3", "@quarterback/scraper-utils": "workspace:*", "@quarterback/types": "workspace:*", "@quarterback/util": "workspace:*", "arg": "^5.0.2", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.12.7", "typescript": "^5.4.5"}}