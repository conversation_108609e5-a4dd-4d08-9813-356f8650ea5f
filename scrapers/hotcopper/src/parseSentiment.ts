import { HotCopperSentiment } from '@quarterback/types';

export default function parseSentiment(sentiment: string | undefined): HotCopperSentiment {
    const lowercased = sentiment?.toLocaleLowerCase() ?? '';

    if (/buy$/.test(lowercased)) {
        return 'BUY';
    } else if (/sell$/.test(lowercased)) {
        return 'SELL';
    } else if (/hold$/.test(lowercased)) {
        return 'HOLD';
    } else {
        return 'NONE';
    }
}
