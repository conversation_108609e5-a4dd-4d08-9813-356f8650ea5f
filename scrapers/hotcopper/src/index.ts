import arg from 'arg';
import { HttpFetcher, LoggingPublisher, PubSubPublisher } from '@quarterback/scraper-utils';
import { HotCopperScraper } from './scraper.js';

const args = arg({
    '--symbol': String,
    '--exchange': String,
    '--from': String,
    '--max-age': String
});

if (!args['--symbol']) throw new Error('missing required argument: --symbol');
if (!args['--exchange']) throw new Error('missing required argument: --exchange');
if (!args['--from']) throw new Error('missing required argument: --from');
if (Number.isNaN(parseInt(args['--from']))) {
    throw new Error(`invalid value for argument --from: ${args['--from']}`);
}

const publisher = process.env['PUBSUB_TOPIC']
    ? new PubSubPublisher(process.env['PUBSUB_TOPIC'])
    : new LoggingPublisher('scrapers');

const fetcher = new HttpFetcher();
const scraper = new HotCopperScraper(
    args['--symbol'],
    args['--exchange'],
    parseInt(args['--from']),
    args['--max-age'] ?? 'P2M',
    publisher,
    fetcher
);

await scraper.run();
