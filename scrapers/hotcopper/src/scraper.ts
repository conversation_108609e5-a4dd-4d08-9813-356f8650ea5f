import { <PERSON><PERSON><PERSON>, Publisher, Scraper } from '@quarterback/scraper-utils';
import { Activity2 } from '@quarterback/types';
import { PubSubMessage2 } from '@quarterback/types/pubsub/scrapers';
import { URL } from 'url';
import { isMatch, parse, sub } from 'date-fns';
import { parse as parseISODuration } from 'duration-fns/dist/parse.js';
import * as cheerio from 'cheerio';
import clean from './clean.js';
import { fromZonedTime } from 'date-fns-tz';
import parseAbbreviatedNumber from './parseAbbreviatedNumber.js';
import { chunked, wait } from '@quarterback/util';
import parseSentiment from './parseSentiment.js';
import parseDisclosure from './parseDisclosure.js';

export class HotCopperScraper extends Scraper {
    from: number;
    maxAge: string;

    fetcher: Fetcher;

    constructor(
        symbol: string,
        exchange: string,
        from: number,
        maxAge: string,
        publisher: Publisher<PubSubMessage2>,
        fetcher: Fetcher
    ) {
        super(symbol, exchange, publisher);

        this.from = from;
        this.maxAge = maxAge;

        this.fetcher = fetcher;
    }

    async posts(page: number): Promise<Array<Activity2>> {
        const results: Array<Activity2> = [];

        const url = new URL(
            `https://hotcopper.com.au/asx/${this.symbol}/discussion/page-${page}`
        );
        const response = await this.fetcher.fetch(url, {
            signal: AbortSignal.timeout(10000)
        });
        const minDate = sub(new Date(), parseISODuration(this.maxAge));

        const $ = cheerio.load(await response.text());

        let lastPage =
            parseInt($('.pagination').first().attr()?.['data-last'] ?? '1') === page;
        const rows = $('#post-list-component')
            .find('table > tbody')
            .first()
            .children(
                'tr:not(.is-hidden-desktop):not(:has(#widget-spotlight-banner-container))'
            );

        for (const element of rows) {
            const row = $(element);

            const title = row.find('.title-td > a');
            const path = title.attr()?.['href'];
            const [_, thread, post] =
                title
                    .attr()
                    ?.[
                        'href'
                    ]?.match(/\/threads\/[a-z\-\d]+\.(\d+)\/(?:page\-\d+)?\?post_id=(\d+)$/) ??
                [];
            const user = clean(row.find('.last-post-td > a').text());
            const [replies, views, likes, posted] = row
                .find('.stats-td')
                .toArray()
                .map((it) => clean($(it).text()));

            const date = fromZonedTime(
                isMatch(posted, 'HH:mm')
                    ? parse(posted, 'HH:mm', new Date())
                    : parse(posted, 'dd/MM/yy', new Date()),
                'Australia/Sydney'
            );

            if (date < minDate) {
                lastPage = true;
                break;
            }

            if (parseInt(post) <= this.from) {
                lastPage = true;
                break;
            }

            results.push({
                title: clean(title.text()).replace(/^Re: /, ''),
                posted: date,
                author: {
                    key: `hotcopper__${user}`,
                    name: user,
                    userId: user
                },
                url: `https://hotcopper.com.au${path}`,
                likes: parseAbbreviatedNumber(likes),

                hotcopper: {
                    id: parseInt(post),
                    thread: {
                        id: thread,
                        views: parseAbbreviatedNumber(views)
                    }
                },
                isBroadcast: user.trim() === 'ASX News',
                symbol: this.symbol,
                exchange: this.exchange
            });
        }

        if (lastPage) {
            return results;
        } else {
            return [...results, ...(await this.posts(page + 1))];
        }
    }

    async *chunk(): AsyncGenerator<Array<Activity2>, void, void> {
        const posts = await this.posts(1).then((it) => it.reverse());

        for (const chunk of chunked(posts, 15)) {
            yield await Promise.all(
                chunk.map(async (post): Promise<Activity2> => {
                    const response = await this.fetcher.fetch(post.url!, {
                        signal: AbortSignal.timeout(10000)
                    });

                    const $ = cheerio.load(await response.text());
                    const body = $('blockquote.message-text').first();

                    body.find('.bbCodeQuote').first().remove();
                    body.find('#announcement-document-download').first().remove();
                    body.find('.pdf-modal').first().remove();

                    body.find('br').replaceWith('\n');

                    const [_, sentiment, disclosure] = $('.meta-details')
                        .toArray()
                        .map((it) => clean($(it).text()));

                    const date = $('.post-metadata-date').text().trim();
                    const time = $('.post-metadata-time').text().trim();

                    return {
                        ...post,
                        hotcopper: {
                            ...post.hotcopper,
                            sentiment: parseSentiment(sentiment),
                            disclosure: parseDisclosure(disclosure)
                        },
                        body: body
                            .text()
                            .split('\n')
                            .map((it) => it.trim())
                            .join('\n')
                            .trim(),
                        posted: fromZonedTime(
                            parse(`${date} ${time}`, 'dd/MM/yy HH:mm', new Date()),
                            'Australia/Sydney'
                        )
                    };
                })
            );

            await wait(2000);
        }
    }
}
