
// Parse numbers of the form '1K', '20K', '3M', etc.
export default function parseAbbreviatedNumber(str: string): number {
    const regex = /^(\d+(?:\.\d+)?)\s*([a-zA-Z]*)$/;
    const match = str.match(regex);
  
    if (match) {
        const number = parseFloat(match[1]);
        const abbreviation = match[2].toUpperCase();

        switch (abbreviation) {
        case 'K':
            return number * 1000;
        case 'M':
            return number * 1000000;
        case 'B':
            return number * 1000000000;
        case 'T':
            return number * 1000000000000;
        default:
            return number;
        }
    }

    return NaN;
}