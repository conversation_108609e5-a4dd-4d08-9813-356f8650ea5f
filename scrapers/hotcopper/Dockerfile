FROM node:22-alpine AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
# RUN corepack enable #corepack is causing issues. enable later and remove line below
RUN npm install -g pnpm@9.15.5

FROM base AS build
COPY pnpm-lock.yaml /app/
COPY pnpm-workspace.yaml /app/
COPY package.json /app/
COPY ./types /app/types
COPY ./util /app/util
COPY ./scraper-utils /app/scraper-utils
COPY ./scrapers/hotcopper /app/scrapers/hotcopper
WORKDIR /app
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --filter=@quarterback/hotcopper --filter=@quarterback/types --filter=@quarterback/scraper-utils --filter=@quarterback/util --config.dedupe-peer-dependents=false --frozen-lockfile
RUN pnpm run -r --filter=@quarterback/hotcopper --filter=@quarterback/types --filter=@quarterback/scraper-utils --filter=@quarterback/util build
RUN pnpm deploy --filter=@quarterback/hotcopper --prod /dist/hotcopper-scraper

FROM base AS hotcopper-scraper
COPY --from=build /dist/hotcopper-scraper /app
WORKDIR /app
ENTRYPOINT [ "pnpm", "start-memory-optimized" ]
