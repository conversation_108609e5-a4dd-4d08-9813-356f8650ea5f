{"name": "@quarterback/hotcopper", "module": "index.ts", "type": "module", "scripts": {"start": "node ./dist/src/index.js", "start-memory-optimized": "node --max_old_space_size=3584 --gc_interval=100 ./dist/src/index.js", "build": "pnpm tsc"}, "files": ["dist"], "peerDependencies": {"typescript": "^5.0.0"}, "packageManager": "pnpm@9.9.0", "dependencies": {"@google-cloud/pubsub": "^4.3.3", "@quarterback/scraper-utils": "workspace:*", "@quarterback/types": "workspace:*", "@quarterback/util": "workspace:*", "arg": "^5.0.2", "cheerio": "1.0.0-rc.12", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "duration-fns": "^3.0.2", "tsx": "^4.8.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.12.7", "typescript": "^5.4.5"}}