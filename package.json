{"name": "quarterback", "version": "1.0.0", "scripts": {"dev:backend": "cd backend/ && pnpm run dev", "dev:frontend": "cd frontend/ && pnpm run dev", "local:backend": "cd backend/ && pnpm run local", "local": "concurrently \"npm run local:backend\" \"npm run dev:frontend\"", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "test": "NODE_OPTIONS=\"$NODE_OPTIONS --experimental-vm-modules\" pnpm jest"}, "engines": {"node": "22.13.1"}, "volta": {"node": "22.13.1", "pnpm": "9.15.5"}, "packageManager": "pnpm@9.15.5", "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "@types/node": "^20.12.11", "concurrently": "^8.2.2", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.2.4", "typescript": "^5.4.5"}}