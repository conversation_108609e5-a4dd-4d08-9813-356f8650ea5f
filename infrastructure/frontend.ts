import * as run from '@pulumi/google-native/run/v2';
import * as gcpDomainMapping from '@pulumi/google-native/run/v1/domainMapping';

import * as docker from '@pulumi/docker';
import * as random from '@pulumi/random';
import * as pulumi from '@pulumi/pulumi';

import { repository as dockerRegistry, id as dockerRegistryId } from './docker/registry';

const config = new pulumi.Config('google-native');
const infrastructure = new pulumi.Config('infrastructure');

export const { id: serviceId } = new random.RandomPet('frontend-service', {
    length: 2
});

export const image = new docker.Image(
    'frontend',
    {
        imageName: pulumi.interpolate`${config.require('region')}-docker.pkg.dev/${config.require('project')}/${dockerRegistryId}/frontend:latest`,
        build: {
            context: '../',
            dockerfile: '../frontend/Dockerfile',
            args: {
                NEXT_PUBLIC_API_BASE_URL: infrastructure.require('api'),
                NEXT_PUBLIC_POSTHOG_KEY: 'phc_ApHhHIGWn6cJXwoPCNBaas9X0SwIHPX0djxuaojqtT5',
                NEXT_PUBLIC_POSTHOG_HOST: 'https://us.i.posthog.com',
                NEXT_PUBLIC_FRONTEND_URL: infrastructure.require('frontend'),
                SENTRY_AUTH_TOKEN: infrastructure.requireSecret('sentry-token'),
                SUPERTOKENS_CONNECTION_URI: infrastructure.require('supertokens-uri'),
                SUPERTOKENS_API_TOKEN: infrastructure.requireSecret('supertokens-token')
            },
            target: 'frontend',
            platform: 'linux/amd64'
        }
    },
    { dependsOn: [dockerRegistry] }
);

export const service = new run.Service(
    'frontend',
    {
        serviceId: serviceId,
        ingress: 'INGRESS_TRAFFIC_ALL',
        template: {
            containers: [
                {
                    image: image.repoDigest,
                    env: [
                        {
                            name: 'SENTRY_AUTH_TOKEN',
                            value: infrastructure.requireSecret('sentry-token')
                        },
                        {
                            name: 'SUPERTOKENS_API_TOKEN',
                            value: infrastructure.requireSecret('supertokens-token')
                        },
                        {
                            name: 'SUPERTOKENS_CONNECTION_URI',
                            value: infrastructure.require('supertokens-uri')
                        }
                    ]
                }
            ]
        }
    },
    {
        dependsOn: [image]
    }
);

export const iamBinding = new run.ServiceIamPolicy(
    'frontend-service-allow-all',
    {
        serviceId: service.serviceId,
        bindings: [{ role: 'roles/run.invoker', members: ['allUsers'] }]
    },
    { dependsOn: [service] }
);
