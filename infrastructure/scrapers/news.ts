import * as run from '@pulumi/google-native/run/v2';
import * as docker from '@pulumi/docker';
import * as random from '@pulumi/random';
import * as pulumi from '@pulumi/pulumi';

import { repository as dockerRegistry, id as dockerRegistryId } from '../docker/registry';

import { topic } from '../pubsub/scrapers';

const infrastructure = new pulumi.Config('infrastructure');

const config = new pulumi.Config('google-native');

export const { id } = new random.RandomPet('news-scraper-job', {
    length: 2
});

export const image = new docker.Image(
    'news-scraper',
    {
        imageName: pulumi.interpolate`${config.require('region')}-docker.pkg.dev/${config.require('project')}/${dockerRegistryId}/news-scraper:latest`,
        build: {
            context: '../',
            dockerfile: '../scrapers/news/Dockerfile',
            target: 'news-scraper',
            platform: 'linux/amd64',
            args: {
                BUILDKIT_INLINE_CACHE: '1'
            }
        }
    },
    { dependsOn: [dockerRegistry] }
);

export const scraper = new run.Job(
    'news-scraper',
    {
        jobId: id,
        name: '', // For some ridiculous reason this is required
        template: {
            template: {
                containers: [
                    {
                        image: image.imageName,
                        resources: {
                            limits: {
                                memory: '1Gi'
                            }
                        },
                        env: [
                            { name: 'PUBSUB_TOPIC', value: topic.name },
                            {
                                name: 'NEWSCATCHER_API_KEY',
                                value: infrastructure.requireSecret('newscatchertoken')
                            }
                        ],
                        args: [
                            '--symbol',
                            'VEE',
                            '--params',
                            '{"q":"\\"VEEM Limited\\" OR \\"VEEM Ltd\\" OR \\"VEE?ASX\\" OR \\"ASX?VEE\\"","lang":"en","exclude_duplicates":true,"ranked_only":false,"sort_by":"date"}',
                            '--exchange',
                            'ASX'
                        ]
                    }
                ]
            }
        }
    },
    { dependsOn: [image, topic] }
);
