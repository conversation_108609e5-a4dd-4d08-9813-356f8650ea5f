import * as cloudscheduler from '@pulumi/google-native/cloudscheduler/v1';
import * as pulumi from '@pulumi/pulumi';

import { base64 } from '@quarterback/util';

import { service as backendService } from '../backend';

import { topic } from '../pubsub/scrapers';
import { internalServiceAccount } from '../serviceAccount';

function scheduler(
    name: string,
    schedule: string,
    timeZone: string,
    scrapers: Array<string>
): cloudscheduler.Job {
    return new cloudscheduler.Job(
        name,
        {
            schedule,
            timeZone,
            httpTarget: {
                uri: pulumi.interpolate`${backendService.uri}/schedulers/scrapers`,
                httpMethod: 'POST',
                oidcToken: {
                    serviceAccountEmail: internalServiceAccount.email
                },
                headers: {
                    'Content-Type': 'application/json'
                },
                body: base64.encode(JSON.stringify(scrapers))
            },
            attemptDeadline: '1800s'
        },
        { dependsOn: [backendService, internalServiceAccount, topic] }
    );
}

export const asxMorningScheduler = scheduler(
    'asx-morning-scheduler',
    '*/30 8-10 * * *',
    'Australia/Sydney',
    ['asx-announcements']
);

export const asxAfternoonScheduler = scheduler(
    'asx-afternoon-scheduler',
    '0 12-20/2 * * *',
    'Australia/Sydney',
    ['asx-announcements']
);

export const hourlyScheduler = scheduler(
    'hourly-scheduler',
    '15 7-20 * * *',
    'Australia/Sydney',
    ['hotcopper', 'twitter', 'reddit', 'news', 'linkedin']
);

export const thriceDailyScheduler = scheduler(
    'thrice-daily-scheduler',
    '30 9,12,16 * * *',
    'Australia/Sydney',
    ['linkedinComments']
);

export const dailyScheduler = scheduler(
    'daily-scheduler',
    '0 9 * * *',
    'Australia/Sydney',
    ['twitter-followers', 'linkedin-followers', 'mailchimp']
);
