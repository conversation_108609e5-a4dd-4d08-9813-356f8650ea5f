import * as run from '@pulumi/google-native/run/v2';
import * as docker from '@pulumi/docker';
import * as random from '@pulumi/random';
import * as pulumi from '@pulumi/pulumi';

import { repository as dockerRegistry, id as dockerRegistryId } from '../docker/registry';
import { topic } from '../pubsub/scrapers';

const config = new pulumi.Config('google-native');

export const { id } = new random.RandomPet('reddit-scraper-job', {
    length: 2
});

export const image = new docker.Image(
    'reddit-scraper',
    {
        imageName: pulumi.interpolate`${config.require('region')}-docker.pkg.dev/${config.require('project')}/${dockerRegistryId}/reddit-scraper:latest`,
        build: {
            context: '../',
            dockerfile: '../scrapers/reddit/Dockerfile',
            target: 'reddit-scraper',
            platform: 'linux/amd64',
            args: {
                BUILDKIT_INLINE_CACHE: '1'
            }
        }
    },
    { dependsOn: [dockerRegistry] }
);

export const scraper = new run.Job(
    'reddit-scraper',
    {
        jobId: id,
        name: '', // For some ridiculous reason this is required, otherwise pulumi tries to auto name the jobs
        template: {
            template: {
                timeout: '9000s',
                containers: [
                    {
                        image: image.imageName,
                        resources: {
                            limits: {
                                memory: '1Gi'
                            }
                        },
                        env: [{ name: 'PUBSUB_TOPIC', value: topic.name }],
                        args: ['--symbol', 'VEE', '--query', 'Veem', '--from', '0']
                    }
                ]
            }
        }
    },
    { dependsOn: [image, topic] }
);
