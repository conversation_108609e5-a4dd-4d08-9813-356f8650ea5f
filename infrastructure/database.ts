import * as sqladmin from '@pulumi/google-native/sqladmin/v1';
import * as random from '@pulumi/random';
import * as pulumi from '@pulumi/pulumi';

const config = new pulumi.Config('google-native');

export const rootPassword = new random.RandomPassword('database-root-password', {
    length: 24,
    special: false
});

export const instance = new sqladmin.Instance('database', {
    backendType: 'SECOND_GEN',
    instanceType: 'CLOUD_SQL_INSTANCE',
    databaseVersion: 'POSTGRES_15',
    region: config.require('region'),
    settings: {
        availabilityType: 'ZONAL',
        dataDiskSizeGb: '10',
        tier: 'db-f1-micro'
    },
    rootPassword: rootPassword.result
});

export const database = new sqladmin.Database(
    'database',
    {
        instance: instance.name,
        name: 'quarterback'
    },
    { dependsOn: [instance] }
);

export const backendUserPassword = new random.RandomPassword('backend-user-password', {
    length: 24,
    special: false
});

export const backendUser = new sqladmin.User(
    'backend-user',
    {
        instance: instance.name,
        name: 'backend',
        password: backendUserPassword.result,
        type: 'BUILT_IN'
    },
    { dependsOn: [instance] }
);
