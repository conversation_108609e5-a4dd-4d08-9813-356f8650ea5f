import * as pulumi from '@pulumi/pulumi';
import * as gcp from '@pulumi/gcp';
import * as random from '@pulumi/random';
import { internalServiceAccount } from './serviceAccount';

const config = new pulumi.Config('google-native');
const infrastructure = new pulumi.Config('infrastructure');
const stack = pulumi.getStack();

// Generate a unique suffix for the bucket name
export const { id: bucketSuffix } = new random.RandomPet('activity-files-bucket-suffix', {
    length: 2
});

// Create a GCP storage bucket for activity files
export const activityFilesBucket = new gcp.storage.Bucket('activity-files-bucket', {
    name: pulumi.interpolate`activity-files-${bucketSuffix}`,
    location: config.require('region'),
    uniformBucketLevelAccess: true,
    forceDestroy: true, // Allow Pulumi to delete the bucket even if it contains objects
    cors: [
        {
            origins: [
                infrastructure.require('frontend'),
                infrastructure.require('api'),
                ...(stack === 'quarterback/dev' ? ['http://localhost:3000'] : [])
            ],
            methods: ['GET', 'HEAD', 'PUT', 'POST', 'DELETE'],
            responseHeaders: ['*'],
            maxAgeSeconds: 3600
        }
    ]
});

// Grant the service account access to the bucket
export const bucketIamBinding = new gcp.storage.BucketIAMMember(
    'bucket-iam-binding',
    {
        bucket: activityFilesBucket.name,
        role: 'roles/storage.objectAdmin',
        member: pulumi.interpolate`serviceAccount:${internalServiceAccount.email}`
    },
    {
        dependsOn: [activityFilesBucket, internalServiceAccount]
    }
);

// Grant the compute service account permission to create tokens
export const computeSaTokenCreatorBinding = new gcp.serviceaccount.IAMMember(
    'compute-sa-token-creator-binding',
    {
        serviceAccountId: pulumi.interpolate`projects/${config.require('project')}/serviceAccounts/${config.require('project-number')}-<EMAIL>`,
        role: 'roles/iam.serviceAccountTokenCreator',
        member: pulumi.interpolate`serviceAccount:${config.require('project-number')}-<EMAIL>`
    }
);
