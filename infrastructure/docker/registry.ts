import * as pulumi from '@pulumi/pulumi';
import * as registry from '@pulumi/google-native/artifactregistry/v1beta2';
import * as random from '@pulumi/random';

export const { id } = new random.RandomPet('docker-registry', {
    length: 2
});

export const repository = new registry.Repository(
    'registry',
    {
        format: registry.RepositoryFormat.Docker,
        repositoryId: id,
    },
    {
        ignoreChanges: ['createTime', 'updateTime'],
        dependsOn: []
    }
);
