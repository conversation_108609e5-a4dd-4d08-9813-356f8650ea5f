import * as run from '@pulumi/google-native/run/v2';
import * as eventarc from '@pulumi/google-native/eventarc/v1';

import * as docker from '@pulumi/docker';
import * as random from '@pulumi/random';
import * as pulumi from '@pulumi/pulumi';

import { repository as dockerRegistry, id as dockerRegistryId } from './docker/registry';

import { scraper as asxAnnouncementsScraper } from './scrapers/asx-announcements';
import { scraper as googleNewsScraper } from './scrapers/google-news';
import { scraper as hotCopperScraper } from './scrapers/hotcopper';
import { scraper as redditScraper } from './scrapers/reddit';
import { scraper as newsScraper } from './scrapers/news';
import { topic as scrapersTopic } from './pubsub/scrapers';

import {
    instance as databaseInstance,
    database,
    backendUser,
    backendUserPassword
} from './database';

import { activityFilesBucket } from './storage';
import { internalServiceAccount } from './serviceAccount';

const config = new pulumi.Config('google-native');
const infrastructure = new pulumi.Config('infrastructure');

export const { id: serviceId } = new random.RandomPet('backend-service', {
    length: 2
});

export const image = new docker.Image(
    'backend',
    {
        imageName: pulumi.interpolate`${config.require('region')}-docker.pkg.dev/${config.require('project')}/${dockerRegistryId}/backend:latest`,
        build: {
            context: '../',
            dockerfile: '../backend/Dockerfile',
            builderVersion: 'BuilderBuildKit',
            target: 'backend',
            platform: 'linux/amd64'
        }
    },
    { dependsOn: [dockerRegistry] }
);

// https://github.com/pulumi/pulumi-gcp/issues/1166
export const service = new run.Service(
    'backend',
    {
        serviceId: serviceId,
        ingress: 'INGRESS_TRAFFIC_ALL',
        template: {
            timeout: '1800s',
            containers: [
                {
                    image: image.repoDigest,
                    env: [
                        {
                            name: 'ASX_ANNOUNCEMENTS_SCRAPER',
                            value: asxAnnouncementsScraper.name
                        },
                        { name: 'HOTCOPPER_SCRAPER', value: hotCopperScraper.name },
                        { name: 'GOOGLE_NEWS_SCRAPER', value: googleNewsScraper.name },
                        { name: 'NEWS_SCRAPER', value: newsScraper.name },
                        { name: 'REDDIT_SCRAPER', value: redditScraper.name },
                        {
                            name: 'APIFY_TOKEN',
                            value: infrastructure.requireSecret('apify-token')
                        },
                        {
                            name: 'INTERNAL_IAM_EMAIL',
                            value: internalServiceAccount.email
                        },
                        {
                            name: 'DATABASE_URL',
                            value: pulumi.interpolate`postgresql://${backendUser.name}:${backendUserPassword.result}@localhost/${database.name}?host=/cloudsql/${databaseInstance.connectionName}`
                        },
                        { name: 'SCRAPERS_PUBSUB_TOPIC', value: scrapersTopic.name },
                        { name: 'JWKS_URI', value: infrastructure.require('jwks') },
                        {
                            name: 'FRONTEND_URL',
                            value: infrastructure.require('frontend')
                        },
                        {
                            name: 'OPENAI_PROJECT_ID',
                            value: infrastructure.require('openai-project')
                        },
                        {
                            name: 'OPENAI_API_KEY',
                            value: infrastructure.requireSecret('openai-key')
                        },
                        {
                            name: 'PINECONE_API_KEY',
                            value: infrastructure.requireSecret('pinecone-key')
                        },
                        {
                            name: 'RESEND_KEY',
                            value: infrastructure.requireSecret('resend-key')
                        },
                        {
                            name: 'GCP_ACTIVITY_FILES_BUCKET',
                            value: activityFilesBucket.name
                        }
                    ],
                    volumeMounts: [{ name: 'cloudsql', mountPath: '/cloudsql' }]
                }
            ],
            volumes: [
                {
                    name: 'cloudsql',
                    cloudSqlInstance: { instances: [databaseInstance.connectionName] }
                }
            ]
        }
    },
    {
        dependsOn: [
            image,
            asxAnnouncementsScraper,
            hotCopperScraper,
            googleNewsScraper,
            newsScraper,
            internalServiceAccount,
            database,
            scrapersTopic,
            activityFilesBucket
        ]
    }
);

export const iamBinding = new run.ServiceIamPolicy(
    'backend-service-allow-all',
    {
        serviceId: service.serviceId,
        bindings: [{ role: 'roles/run.invoker', members: ['allUsers'] }]
    },
    { dependsOn: [service] }
);

export const { id: triggerId } = new random.RandomPet('backend-pubsub-scrapers-trigger', {
    length: 2
});

export const trigger = new eventarc.Trigger(
    'backend-pubsub-scrapers-trigger',
    {
        triggerId,
        destination: {
            cloudRun: {
                service: service.serviceId,
                region: config.require('region'),
                path: '/pubsub/scrapers'
            }
        },
        eventFilters: [
            { attribute: 'type', value: 'google.cloud.pubsub.topic.v1.messagePublished' }
        ],
        transport: {
            pubsub: { topic: scrapersTopic.name }
        },
        serviceAccount: internalServiceAccount.email
    },
    { dependsOn: [scrapersTopic, service, internalServiceAccount] }
);
