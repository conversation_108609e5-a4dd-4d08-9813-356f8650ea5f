import * as cloudscheduler from '@pulumi/google-native/cloudscheduler/v1';
import * as pulumi from '@pulumi/pulumi';

import { service as backendService } from '../backend';
import { topic } from '../pubsub/scrapers';
import { internalServiceAccount } from '../serviceAccount';

export const alerts = new cloudscheduler.Job(
    'alerts-scheduler',
    {
        schedule: '*/10 8-18 * * *',
        timeZone: 'Australia/Sydney',
        httpTarget: {
            uri: pulumi.interpolate`${backendService.uri}/schedulers/alerts`,
            httpMethod: 'POST',
            oidcToken: {
                serviceAccountEmail: internalServiceAccount.email
            },
            headers: {
                'Content-Type': 'application/json'
            }
        },
        attemptDeadline: '1800s'
    },
    { dependsOn: [backendService, internalServiceAccount, topic] }
);
