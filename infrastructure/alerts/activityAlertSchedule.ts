import * as cloudscheduler from '@pulumi/google-native/cloudscheduler/v1';
import * as pulumi from '@pulumi/pulumi';

import { service as backendService } from '../backend';
import { topic } from '../pubsub/scrapers';
import { internalServiceAccount } from '../serviceAccount';

export const activityAlertsScheduler = new cloudscheduler.Job(
    'activity-alerts-scheduler',
    {
        schedule: '25 7-20 * * *',
        timeZone: 'Australia/Sydney',
        httpTarget: {
            uri: pulumi.interpolate`${backendService.uri}/schedulers/alerts/activity`,
            httpMethod: 'POST',
            oidcToken: {
                serviceAccountEmail: internalServiceAccount.email
            },
            headers: {
                'Content-Type': 'application/json'
            }
        },
        attemptDeadline: '1800s'
    },
    { dependsOn: [backendService, internalServiceAccount, topic] }
);
