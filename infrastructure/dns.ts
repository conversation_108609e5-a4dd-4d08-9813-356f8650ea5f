import * as cloudflare from '@pulumi/cloudflare';
import * as pulumi from '@pulumi/pulumi';

import { ipaddress } from './loadbalancer';

const cloudflareConfig = new pulumi.Config('cloudflare');
const infrastructureConfig = new pulumi.Config('infrastructure');

export const frontend = new cloudflare.Record(
    'frontend-dns',
    {
        name: 'app',
        zoneId: infrastructureConfig.require('cloudflare-zone'),
        type: 'A',
        content: ipaddress.address,
        ttl: 3600,
        proxied: false
    },
    { dependsOn: [ipaddress] }
);

export const backend = new cloudflare.Record(
    'backend-dns',
    {
        name: 'api',
        zoneId: infrastructureConfig.require('cloudflare-zone'),
        type: 'A',
        content: ipaddress.address,
        ttl: 3600,
        proxied: false
    },
    { dependsOn: [ipaddress] }
);
