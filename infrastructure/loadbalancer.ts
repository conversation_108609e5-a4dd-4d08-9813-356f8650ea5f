import * as pulumi from '@pulumi/pulumi';
import * as gcp from '@pulumi/gcp';

import { service as frontendService } from './frontend';
import { service as backendService } from './backend';

const config = new pulumi.Config('infrastructure');
const domain = config.require('domain');

export const ipaddress = new gcp.compute.GlobalAddress('external-ipaddress', {
    addressType: 'EXTERNAL'
});

const frontendEndpointGroup = new gcp.compute.RegionNetworkEndpointGroup('frontend-group', {
    networkEndpointType: 'SERVERLESS',
    region: 'australia-southeast1',
    cloudRun: { service: frontendService.serviceId }
});

const backendEndpointGroup = new gcp.compute.RegionNetworkEndpointGroup('backend-group', {
    networkEndpointType: 'SERVERLESS',
    region: 'australia-southeast1',
    cloudRun: { service: backendService.serviceId }
});

const frontendBackendService = new gcp.compute.BackendService('frontend-backend-service', {
    enableCdn: false,
    connectionDrainingTimeoutSec: 10,
    backends: [{ group: frontendEndpointGroup.id }]
});

const backendBackendService = new gcp.compute.BackendService('backend-backend-service', {
    enableCdn: false,
    connectionDrainingTimeoutSec: 10,
    backends: [{ group: backendEndpointGroup.id }]
});

const https_paths = new gcp.compute.URLMap('https-map', {
    defaultService: backendBackendService.id,
    hostRules: [
        {
            hosts: [`app.${domain}`],
            pathMatcher: 'frontend-all-paths'
        },
        {
            hosts: [`api.${domain}`],
            pathMatcher: 'backend-all-paths'
        }
    ],
    pathMatchers: [
        {
            name: 'frontend-all-paths',
            defaultService: frontendBackendService.id,
            pathRules: [
                {
                    paths: ['/*'],
                    service: frontendBackendService.id
                }
            ]
        },
        {
            name: 'backend-all-paths',
            defaultService: backendBackendService.id,
            pathRules: [
                {
                    paths: ['/*'],
                    service: backendBackendService.id
                }
            ]
        }
    ]
});

const certificate = new gcp.compute.ManagedSslCertificate('qback-ssl-certificate', {
    managed: {
        domains: [`app.${domain}`, `api.${domain}`]
    }
});

const https_proxy = new gcp.compute.TargetHttpsProxy('https-proxy', {
    urlMap: https_paths.selfLink,
    sslCertificates: [certificate.id]
});

new gcp.compute.GlobalForwardingRule('https-forwarding-rule', {
    target: https_proxy.selfLink,
    ipAddress: ipaddress.address,
    portRange: '443',
    loadBalancingScheme: 'EXTERNAL'
});
