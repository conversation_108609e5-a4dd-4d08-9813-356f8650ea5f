# Quarterback Platform

## First run

From the root directory, run:

```sh
pnpm install
pnpm -r --filter=\!@quarterback/frontend build
```

## Local development

### Setup the database proxy

```sh
gcloud auth application-default login
backend/proxy.dev.sh # this requires cloud-sql-proxy installed
```

### Running the application server

```sh
pnpm run dev # this will run both /backend and /frontend dev servers
```

## Building and running containers

Containers can be built by running `docker build` from the root directory. e.g. `docker build . --target google-news-scraper --tag quarterback:google-news-scraper`.

To start a container run `docker run quarterback:tag` e.g. `docker run quarterback:google-news-scraper`.
